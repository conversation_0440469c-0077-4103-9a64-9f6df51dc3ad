import frappe

def quotation_on_submit(doc, method):
    """
    Hook that runs when quotation is submitted
    Updates CRM Deal status when opportunity status changes to 'Quotation'
    """
    if doc.opportunity:
        # Update CRM Deal status when quotation is submitted
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            frappe.db.set_value("CRM Deal", deal_name, "status", "Quotation")
            frappe.db.commit()
            
            # Optional: Add a comment to track the change
            frappe.get_doc("CRM Deal", deal_name).add_comment(
                "Info", 
                f"Status updated to 'Quotation' due to quotation {doc.name} submission"
            )

def quotation_on_cancel(doc, method):
    """
    Hook that runs when quotation is cancelled
    Resets CRM Deal status back to appropriate status
    """
    if doc.opportunity:
        # Get the current opportunity status
        opp_status = frappe.db.get_value("Opportunity", doc.opportunity, "status")
        
        # Map opportunity status to CRM Deal status
        status_map = { 
            "Open": "Open",
            "Replied": "Negotiation", 
            "Converted": "Won",
            "Lost": "Lost",
            "Closed": "Lost"
        }
        
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            crm_status = status_map.get(opp_status, "Open")
            frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
            frappe.db.commit()
            
            # Optional: Add a comment to track the change
            frappe.get_doc("CRM Deal", deal_name).add_comment(
                "Info", 
                f"Status updated to '{crm_status}' due to quotation {doc.name} cancellation"
            )

def opportunity_before_save(doc, method):
    """
    Your existing hook for manual status changes
    This will continue to work for manual updates from the front desk
    """
    # Only process if this is not a new document and status has changed
    if not doc.is_new() and doc.has_value_changed("status"):
        status_map = { 
            "Replied": "Negotiation", 
            "Converted": "Won" 
        } 
        
        if doc.status not in ["Open","Closed","Lost"]: 
            deal_name = "P" + doc.name[3:] 
            if frappe.db.exists("CRM Deal", deal_name): 
                crm_status = status_map.get(doc.status, doc.status) 
                frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
                frappe.db.commit()
