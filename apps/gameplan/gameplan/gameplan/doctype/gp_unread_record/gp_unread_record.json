{"actions": [], "allow_rename": 1, "autoname": "autoincrement", "creation": "2025-08-18 22:54:50.938015", "doctype": "DocType", "engine": "InnoDB", "field_order": ["user", "project", "column_break_vvtv", "discussion", "comment", "is_unread"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_standard_filter": 1, "label": "User", "options": "User"}, {"default": "1", "fieldname": "is_unread", "fieldtype": "Check", "in_list_view": 1, "in_standard_filter": 1, "label": "Is Unread"}, {"fieldname": "column_break_vvtv", "fieldtype": "Column Break"}, {"fieldname": "discussion", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Discussion", "options": "GP Discussion"}, {"fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Project", "options": "GP Project"}, {"fieldname": "comment", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Comment", "options": "GP Comment"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-08-19 15:59:12.679694", "modified_by": "<EMAIL>", "module": "Gameplan", "name": "GP Unread Record", "naming_rule": "Autoincrement", "owner": "<EMAIL>", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}