<template>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_b_525_5376)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M9.50545 11.0115H11.5132C13.1775 11.0115 14.5268 9.66229 14.5268 7.99794C14.5268 6.3336 13.1775 4.98438 11.5132 4.98438L9.50545 4.98438V3.98438H11.5132C13.7298 3.98438 15.5268 5.78131 15.5268 7.99794C15.5268 10.2146 13.7298 12.0115 11.5132 12.0115H9.50545V11.0115ZM6.49422 4.98465H4.48647C2.82212 4.98465 1.4729 6.33387 1.4729 7.99822C1.4729 9.66256 2.82212 11.0118 4.48647 11.0118H6.49422V12.0118H4.48647C2.26984 12.0118 0.4729 10.2148 0.4729 7.99822C0.4729 5.78159 2.26983 3.98465 4.48647 3.98465H6.49422V4.98465ZM10.0079 8.49808H10.5079V7.49808H10.0079H5.99239H5.49239V8.49808H5.99239H10.0079Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <filter
        id="filter0_b_525_5376"
        x="-4"
        y="-4"
        width="24"
        height="24"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="2" />
        <feComposite
          in2="SourceAlpha"
          operator="in"
          result="effect1_backgroundBlur_525_5376"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_backgroundBlur_525_5376"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
</template>
