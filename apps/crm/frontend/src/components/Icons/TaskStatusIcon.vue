<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    class="text-ink-gray-7"
    :aria-label="status"
  >
    <path
      v-if="status == 'Backlog'"
      fill="currentColor"
      fill-rule="evenodd"
      d="M4 5 2.576 3.574A6.968 6.968 0 0 0 1.043 7.22H3.06c.13-.824.46-1.582.94-2.22Zm-.948 3.72H1.037a6.968 6.968 0 0 0 1.54 3.707l1.425-1.424a4.975 4.975 0 0 1-.95-2.283Zm2.02 3.333-1.427 1.428a6.966 6.966 0 0 0 3.6 1.479v-2.017a4.972 4.972 0 0 1-2.173-.89Zm3.673.892v2.016a6.968 6.968 0 0 0 3.68-1.537L11.001 12a4.974 4.974 0 0 1-2.256.945Zm3.307-2.015 1.427 1.427a6.967 6.967 0 0 0 1.484-3.637H12.95a4.973 4.973 0 0 1-.897 2.21Zm.888-3.71h2.017a6.967 6.967 0 0 0-1.476-3.575l-1.428 1.427c.452.623.762 1.355.887 2.148Zm-1.937-3.218 1.424-1.425A6.968 6.968 0 0 0 8.745 1.04v2.016c.839.125 1.61.459 2.258.947Zm-3.758-.945V1.04a6.966 6.966 0 0 0-3.602 1.48L5.07 3.949a4.973 4.973 0 0 1 2.175-.891Z"
      clip-rule="evenodd"
    />
    <path
      v-else-if="status == 'Todo'"
      fill="currentColor"
      fill-rule="evenodd"
      d="M8 13A5 5 0 1 0 8 3a5 5 0 0 0 0 10Zm0 2A7 7 0 1 0 8 1a7 7 0 0 0 0 14Z"
      clip-rule="evenodd"
    />
    <path
      v-else-if="status == 'In Progress'"
      fill="currentColor"
      fill-rule="evenodd"
      d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm0-1.75a5.25 5.25 0 1 1 0-10.5 5.25 5.25 0 0 1 0 10.5Zm2.828-2.422A4 4 0 0 1 8 12V4a4 4 0 0 1 2.828 6.828Z"
      clip-rule="evenodd"
    />
    <path
      v-else-if="status == 'Done'"
      fill="currentColor"
      fill-rule="evenodd"
      d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm3.173-9.015a.5.5 0 0 0-.772-.636l-3.45 4.183L5.612 7.8a.5.5 0 1 0-.792.612l1.725 2.228a.5.5 0 0 0 .781.013l3.848-4.667Z"
      clip-rule="evenodd"
    />
    <path
      v-else-if="status == 'Canceled'"
      fill="currentColor"
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM5.14645 5.14645C4.95118 5.34171 4.95118 5.65829 5.14645 5.85355L7.29289 8L5.14645 10.1464C4.95118 10.3417 4.95118 10.6583 5.14645 10.8536C5.34171 11.0488 5.65829 11.0488 5.85355 10.8536L8 8.70711L10.1464 10.8536C10.3417 11.0488 10.6583 11.0488 10.8536 10.8536C11.0488 10.6583 11.0488 10.3417 10.8536 10.1464L8.70711 8L10.8536 5.85355C11.0488 5.65829 11.0488 5.34171 10.8536 5.14645C10.6583 4.95118 10.3417 4.95118 10.1464 5.14645L8 7.29289L5.85355 5.14645C5.65829 4.95118 5.34171 4.95118 5.14645 5.14645Z"
    />
    <circle v-else-if="status" cx="8" cy="8" r="7" fill="currentColor" />
  </svg>
</template>
<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true,
  },
})
</script>
