msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-08-03 09:38+0000\n"
"PO-Revision-Date: 2025-08-07 10:07\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Serbian (Latin)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sr-CS\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: sr_CS\n"

#: frontend/src/components/ViewControls.vue:1217
msgid " (New)"
msgstr " (Novo)"

#: frontend/src/components/Modals/TaskModal.vue:99
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "04.01.2024. 23:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "pre 1 sata"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "pre 1 minut"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "pre 1 mesec"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "pre 1 nedelju"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "pre 1 godinu"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>META</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>PREČICE</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:98
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:98
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "<p>Zdravo {{ lead_name }},</p>\\n\\n<p>Ovo je podsetnik za plaćanje {{ grand_total }}.</p>\\n\\n<p>Hvala,</p>\\n<p>Frappé</p>"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>PORTAL</b></span>"

#: frontend/src/components/CommunicationArea.vue:85
msgid "@John, can you please check this?"
msgstr "@John, možeš li molim te proveriti ovo?"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:56
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Potencijalni klijent zahteva ili ime osobe ili naziv organizacije"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Key"
msgstr "API ključ"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "API ključ je obavezan"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Secret"
msgstr "API tajna"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API token"

#: frontend/src/components/Telephony/TwilioCallUI.vue:92
msgid "Accept"
msgstr "Prihvati"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "Prihvati pozivnicu"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Prihvaćeno"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "Prihvaćeno"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Ključ za pristup"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "Ključ za pristup je obavezan za pružaoca usluga: {0}"

#: frontend/src/components/Settings/General/CurrencySettings.vue:97
msgid "Access key"
msgstr "Ključ za pristup"

#: frontend/src/components/Settings/General/CurrencySettings.vue:101
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Ključ za pristup Exchangerate Host. Neophodan za preuzimanje deviznih kurseva."

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Naziv naloga"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "SID naloga"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "Naziv naloga je obavezan"

#: frontend/src/components/CustomActions.vue:73
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1109
msgid "Actions"
msgstr "Radnje"

#: frontend/src/pages/Deal.vue:540 frontend/src/pages/Lead.vue:406
#: frontend/src/pages/MobileDeal.vue:432 frontend/src/pages/MobileLead.vue:339
msgid "Activity"
msgstr "Aktivnost"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:53
msgid "Add"
msgstr "Dodaj"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "Dodaj nalog"

#: frontend/src/components/ColumnSettings.vue:69
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Dodaj kolonu"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "Dodaj postojećeg korisnika"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:58
#: frontend/src/components/FieldLayoutEditor.vue:173
#: frontend/src/components/Kanban/KanbanSettings.vue:84
#: frontend/src/components/SidePanelLayoutEditor.vue:98
msgid "Add Field"
msgstr "Dodaj polje"

#: frontend/src/components/Filter.vue:138
msgid "Add Filter"
msgstr "Dodaj filter"

#: frontend/src/components/Controls/Grid.vue:321
msgid "Add Row"
msgstr "Dodaj red"

#: frontend/src/components/FieldLayoutEditor.vue:200
#: frontend/src/components/SidePanelLayoutEditor.vue:130
msgid "Add Section"
msgstr "Dodaj odeljak"

#: frontend/src/components/SortBy.vue:148
msgid "Add Sort"
msgstr "Dodaj sortiranje"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "Dodaj karticu"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Dodaj nedeljne praznike"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "Dodaj dijagram"

#: frontend/src/components/FieldLayoutEditor.vue:426
msgid "Add column"
msgstr "Dodaj kolonu"

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "Dodaj opis..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "Dodaj postojeće sistemske korisnike u ovaj CRM. Dodeli im ulogu kako bi im omogućio pristup sa postojećim kredencijalima."

#: frontend/src/components/ViewControls.vue:104
msgid "Add filter"
msgstr "Dodaj filter"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "Dodaj napomenu"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "Dodaj probne podatke"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "Dodaj svg kod ili koristi feather ikonice, npr. 'podešavanje'"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "Dodaj zadatak"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Dodaj u praznike"

#: frontend/src/components/Layouts/AppSidebar.vue:434
msgid "Add your first comment"
msgstr "Dodajte svoj prvi komentar"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "Dodajte, uredite i upravljajte imejl šablonima za različite CRM komunikacije"

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Adresa"

#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:172
#: frontend/src/components/Settings/InviteUserPage.vue:179
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:184
#: frontend/src/components/Settings/Users.vue:244
#: frontend/src/components/Settings/Users.vue:247
msgid "Admin"
msgstr "Administrator"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "Agent trenutno nije dostupan da primi poziv, molimo Vas da pokušate kasnije."

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Sve"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:512 frontend/src/pages/MobileContact.vue:510
#: frontend/src/pages/MobileOrganization.vue:454
#: frontend/src/pages/Organization.vue:463
msgid "Amount"
msgstr "Iznos"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "Iznos nakon popusta"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "Dogodila se greška"

#: frontend/src/data/document.js:63
msgid "An error occurred while updating the document"
msgstr "Došlo je do greške prilikom ažuriranja dokumenta"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "Ikonica sa ekstenzijom .ico. Trebalo bi da bude razmere 16 x 16 px. Generisano pomoću favicon generatora. [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "Preporučuje se slika u razmeri 1:1 i 2:1"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "And"
msgstr "i"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "Godišnji prihod"

#: frontend/src/components/Modals/DealModal.vue:201
#: frontend/src/components/Modals/LeadModal.vue:142
msgid "Annual Revenue should be a number"
msgstr "Godišnji prihod treba da bude broj"

#: frontend/src/components/Settings/General/BrandSettings.vue:69
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "Pojavljuje se u levoj bočnoj traci. Preporučena veličina je 32x32 px u PNG ili SVG formatu"

#: frontend/src/components/Settings/General/BrandSettings.vue:103
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "Pojavljuje se pored naslova u kartici internet pretraživača. Preporučena veličina je 32x32 px u PNG ili ICO formatu"

#: frontend/src/components/Kanban/KanbanSettings.vue:107
#: frontend/src/components/Kanban/KanbanView.vue:45
msgid "Apply"
msgstr "Primeni"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Primeni na"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Primeni na"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "Aplikacije"

#: frontend/src/components/Activities/AttachmentArea.vue:139
msgid "Are you sure you want to delete this attachment?"
msgstr "Da li ste sigurni da želite da obrišete ove priloge?"

#: frontend/src/pages/MobileContact.vue:263
msgid "Are you sure you want to delete this contact?"
msgstr "Da li ste sigurni da želite da obrišete ovaj kontakt?"

#: frontend/src/pages/MobileOrganization.vue:264
msgid "Are you sure you want to delete this organization?"
msgstr "Da li ste sigurni da želite da obrišete ovu organizaciju?"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "Da li ste sigurni da želite da obrišete ovaj zadatak?"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "Da li ste sigurni da želite da obrišete {0} povezanih stavki?"

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Da li ste sigurni da želite da se prijavite na svoju Frappe Cloud kontrolnu tablu?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "Da li ste sigurni da želite da resetujete skriptu obrasca 'Kreiraj ponudu iz CRM poslovne prilike'?"

#: frontend/src/components/Settings/General/CurrencySettings.vue:174
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "Da li ste sigurni da želite da postavite valutu kao {0}? Ovo se ne može promeniti kasnije."

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "Da li ste sigurni da želite da poništite povezivanje {0} povezanih stavki?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "Zatražite od svog menadžera da postavi provajdera deviznih kurseva, jer podrazumevani provajder ne podržava konverziju valuta iz {0} u {1}."

#: frontend/src/components/ListBulkActions.vue:184
#: frontend/src/components/Modals/AssignmentModal.vue:5
msgid "Assign To"
msgstr "Dodeli"

#: frontend/src/components/AssignTo.vue:9
msgid "Assign to"
msgstr "Dodeli"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Dodeljeno"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Zadatak"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Pravilo dodele zadatka"

#: frontend/src/components/ListBulkActions.vue:152
msgid "Assignment cleared successfully"
msgstr "Zadatak je uspešno uklonjen"

#: frontend/src/components/Layouts/AppSidebar.vue:577
msgid "Assignment rule"
msgstr "Pravilo dodele zadatka"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:176
msgid "At least one field is required"
msgstr "Barem jedno polje je obavezno"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:76
msgid "Attach"
msgstr "Priloži"

#: frontend/src/pages/Deal.vue:117 frontend/src/pages/Lead.vue:174
msgid "Attach a file"
msgstr "Priloži fajl"

#: frontend/src/pages/Deal.vue:575 frontend/src/pages/Lead.vue:441
#: frontend/src/pages/MobileDeal.vue:468 frontend/src/pages/MobileLead.vue:375
msgid "Attachments"
msgstr "Prilozi"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "Autentifikacioni token"

#: crm/api/dashboard.py:238
msgid "Average deal value of non won/lost deals"
msgstr "Prosečna vrednost poslovne prilike koja nije ni dobijena ni izgubljena"

#: crm/api/dashboard.py:411
msgid "Average deal value of ongoing & won deals"
msgstr "Prosečna vrednost tekućih i dobijenih poslovnih prilika"

#: crm/api/dashboard.py:354
msgid "Average deal value of won deals"
msgstr "Prosečna vrednost dobijenih poslovnih prilika"

#: crm/api/dashboard.py:518
msgid "Average time taken from deal creation to deal closure"
msgstr "Prosečno vreme od kreiranja do zatvaranja poslovne prilike"

#: crm/api/dashboard.py:464
msgid "Average time taken from lead creation to deal closure"
msgstr "Prosečno vreme od kreiranja potencijalnog klijenta do zatvaranja poslovne prilike"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "Prosečna vrednost poslovne prilike"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "Prosečna vrednost tekuće poslovne prilike"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "Prosečno vreme za zatvaranje poslovne prilike"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "Prosečno vreme za zatvaranje potencijalnog klijenta"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "Prosečna vrednost dobijene poslovne prilike"

#: crm/api/dashboard.py:410
msgid "Avg. deal value"
msgstr "Prosečna vrednost poslovne prilike"

#: crm/api/dashboard.py:237
msgid "Avg. ongoing deal value"
msgstr "Prosečna vrednost tekuće poslovne prilike"

#: crm/api/dashboard.py:517
msgid "Avg. time to close a deal"
msgstr "Prosečno vreme za zatvaranje poslovne prilike"

#: crm/api/dashboard.py:463
msgid "Avg. time to close a lead"
msgstr "Prosečno vreme za zatvaranje potencijalnog klijenta"

#: crm/api/dashboard.py:353
msgid "Avg. won deal value"
msgstr "Prosečna vrednost dobijene poslovne prilike"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "Dijagram sa osama"

#: frontend/src/components/Activities/EmailArea.vue:72
#: frontend/src/components/EmailEditor.vue:44
#: frontend/src/components/EmailEditor.vue:69
msgid "BCC"
msgstr "BCC"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "Nazad"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "Nazad na otpremanje fajlova"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "Zakašnjenje"

#: frontend/src/components/Filter.vue:355
msgid "Between"
msgstr "Između"

#: frontend/src/components/Settings/General/BrandSettings.vue:40
msgid "Brand name"
msgstr "Naziv brenda"

#: frontend/src/components/Settings/General/BrandSettings.vue:9
msgid "Brand settings"
msgstr "Podešavanje brenda"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "Brendiranje"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "Masovno uređivanje"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Zauzet"

#: frontend/src/components/Activities/EmailArea.vue:67
#: frontend/src/components/EmailEditor.vue:34
#: frontend/src/components/EmailEditor.vue:56
msgid "CC"
msgstr "CC"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "CRM evidencija poziva"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "CRM status komunikacije"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "CRM kontakti"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:318
msgid "CRM Dashboard"
msgstr "CRM kontrolna tabla"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "CRM poslovna prilika"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "CRM status poslovne prilike"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "CRM stavka padajućeg menija"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "CRM Exotel podešavanja"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "CRM raspored polja"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "CRM skripta obrasca"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "CRM globalna podešavanja"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "CRM praznik"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "CRM lista praznika"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "CRM industrija"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "CRM pozivnica"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "CRM potencijalni klijent"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "CRM izvor potencijalnog klijenta"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "CRM status potencijalnog klijenta"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "CRM razlog gubitka"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "CRM obaveštenje"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "CRM organizacija"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "CRM stranica portala"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "CRM proizvod"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "CRM proizvodi"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "CRN dan pružanja usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "CRM sporazum o nivou usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "CRM prioritet nivoa usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "CRM evidencija promena statusa"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "CRM zadatak"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "CRM agent telefonije"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "CRM broj telefonije"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "CRM teritorija"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "CRM Twilio podešavanja"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "CRM podešavanje prikaza"

#: frontend/src/components/Settings/General/CurrencySettings.vue:42
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "CRM valuta za sve novčane vrednosti. Jednom kada se postavi, ne može se menjati."

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Detalji poziva"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "Poziv primio"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "Trajanje poziva u sekundama"

#: frontend/src/components/Layouts/AppSidebar.vue:551
msgid "Call log"
msgstr "Evidencija poziva"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "Poziv koristeći {0}"

#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:43
msgid "Call with John Doe"
msgstr "Poziv sa Petrom Petrovićem"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "Pozivalac"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "Sredstvo za pozivanje"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Calling..."
msgstr "Pozivanje..."

#: frontend/src/pages/Deal.vue:560 frontend/src/pages/Lead.vue:426
#: frontend/src/pages/MobileDeal.vue:452 frontend/src/pages/MobileLead.vue:359
msgid "Calls"
msgstr "Pozivi"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Kamera"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/ColumnSettings.vue:132
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:114
#: frontend/src/components/Modals/AssignmentModal.vue:9
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:77
#: frontend/src/components/ViewControls.vue:56
#: frontend/src/components/ViewControls.vue:156
#: frontend/src/pages/Dashboard.vue:41
msgid "Cancel"
msgstr "Otkaži"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Otkazano"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "Nije moguće promeniti ulogu korisniku sa administratorskim pristupom"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "Nije moguće obrisati standardnu stavku {0}"

#: frontend/src/components/FilesUploader/FilesUploader.vue:94
msgid "Capture"
msgstr "Zabeleži"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Capturing leads"
msgstr "Beleženje potencijalnih klijenata"

#: frontend/src/components/Layouts/AppSidebar.vue:485
msgid "Change"
msgstr "Promena"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Promeni lozinku"

#: frontend/src/components/Activities/TaskArea.vue:44
msgid "Change Status"
msgstr "Promeni status"

#: frontend/src/components/Layouts/AppSidebar.vue:476
#: frontend/src/components/Layouts/AppSidebar.vue:484
msgid "Change deal status"
msgstr "Promeni status poslovne prilike"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:41 frontend/src/pages/Lead.vue:95
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:41
msgid "Change image"
msgstr "Promeni sliku"

#: frontend/src/pages/Dashboard.vue:28
msgid "Chart"
msgstr "Dijagram"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Vrsta dijagrama"

#: frontend/src/components/Modals/ConvertToDealModal.vue:43
#: frontend/src/components/Modals/ConvertToDealModal.vue:69
#: frontend/src/pages/MobileLead.vue:124 frontend/src/pages/MobileLead.vue:151
msgid "Choose Existing"
msgstr "Izaberi postojeće"

#: frontend/src/components/Modals/DealModal.vue:45
msgid "Choose Existing Contact"
msgstr "Izaberi postojeći kontakt"

#: frontend/src/components/Modals/DealModal.vue:38
msgid "Choose Existing Organization"
msgstr "Izaberi postojeću organizaciju"

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "Izaberi provajdera imejl usluge koga želiš da konfigurišeš."

#: frontend/src/components/Controls/Link.vue:62
msgid "Clear"
msgstr "Očisti"

#: frontend/src/components/ListBulkActions.vue:134
#: frontend/src/components/ListBulkActions.vue:142
#: frontend/src/components/ListBulkActions.vue:188
msgid "Clear Assignment"
msgstr "Očisti dodeljene zadatke"

#: frontend/src/components/SortBy.vue:160
msgid "Clear Sort"
msgstr "Očisti sortiranje"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Očisti tabelu"

#: frontend/src/components/Filter.vue:18 frontend/src/components/Filter.vue:150
msgid "Clear all Filter"
msgstr "Očisti sve filtere"

#: frontend/src/components/Notifications.vue:28
msgid "Close"
msgstr "Zatvori"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "Datum zatvaranja"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Sažmi"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Collapsible"
msgstr "Može se sažeti"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Boja"

#: frontend/src/components/FieldLayoutEditor.vue:423
msgid "Column"
msgstr "Kolona"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:15
msgid "Column Field"
msgstr "Polje kolone"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Kolone"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:80
#: frontend/src/components/CommunicationArea.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:574
msgid "Comment"
msgstr "Komentar"

#: frontend/src/pages/Deal.vue:550 frontend/src/pages/Lead.vue:416
#: frontend/src/pages/MobileDeal.vue:442 frontend/src/pages/MobileLead.vue:349
msgid "Comments"
msgstr "Komentari"

#: crm/api/dashboard.py:884
msgid "Common reasons for losing deals"
msgstr "Uobičajeni razlozi za gubitak poslovnih prilika"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "Status komunikacije"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "Statusi komunikacije"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "Kompanija na ERPNext veb-sajtu"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Završeno"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Računar"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Uslov"

#: frontend/src/components/Settings/General/GeneralSettings.vue:8
msgid "Configure general settings for your CRM"
msgstr "Konfigurišite opšta podešavanja Vašeg CRM"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "Konfigurišite podešavanja telefonije za Vaš CRM"

#: frontend/src/components/Settings/General/CurrencySettings.vue:70
msgid "Configure the exchange rate provider for your CRM"
msgstr "Konfigurišite provajdera deviznih kurseva za Vaš CRM"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Potvrdi"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:250
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:253
msgid "Confirm Delete"
msgstr "Potvrdi brisanje"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "Potvrdi lozinku"

#: frontend/src/components/Settings/Users.vue:225
#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "Potvrdi uklanjanje"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "Povežite Vaš imejl"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:547
#: frontend/src/components/Modals/ConvertToDealModal.vue:65
#: frontend/src/pages/MobileLead.vue:147
msgid "Contact"
msgstr "Kontakt"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:212
msgid "Contact Already Exists"
msgstr "Kontakt već postoji"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "Kontaktirajte podršku"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "Kontaktirajte nas"

#: frontend/src/pages/Deal.vue:655 frontend/src/pages/MobileDeal.vue:546
msgid "Contact added"
msgstr "Kontakt je dodat"

#: frontend/src/pages/Deal.vue:645 frontend/src/pages/MobileDeal.vue:536
msgid "Contact already added"
msgstr "Kontakt je već dodat"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:211
msgid "Contact already exists with {0}"
msgstr "Kontakt već postoji sa {0}"

#: frontend/src/pages/Contact.vue:282 frontend/src/pages/MobileContact.vue:255
msgid "Contact image updated"
msgstr "Slika kontakta je ažurirana"

#: frontend/src/pages/Deal.vue:666 frontend/src/pages/MobileDeal.vue:557
msgid "Contact removed"
msgstr "Kontakt je uklonjen"

#: frontend/src/pages/Contact.vue:437 frontend/src/pages/Contact.vue:450
#: frontend/src/pages/Contact.vue:463 frontend/src/pages/Contact.vue:473
#: frontend/src/pages/MobileContact.vue:435
#: frontend/src/pages/MobileContact.vue:448
#: frontend/src/pages/MobileContact.vue:461
#: frontend/src/pages/MobileContact.vue:471
msgid "Contact updated"
msgstr "Kontakt je ažuriran"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:237 frontend/src/pages/MobileContact.vue:215
#: frontend/src/pages/MobileOrganization.vue:334
msgid "Contacts"
msgstr "Kontakti"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:34
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:105
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:105
msgid "Content"
msgstr "Sadržaj"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Content Type"
msgstr "Vrsta sadržaja"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:167
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:165
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:169
msgid "Content is required"
msgstr "Sadržaj je obavezan"

#: frontend/src/components/Layouts/AppSidebar.vue:375
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:8
#: frontend/src/pages/MobileLead.vue:56 frontend/src/pages/MobileLead.vue:110
msgid "Convert"
msgstr "Prebaci"

#: frontend/src/components/Layouts/AppSidebar.vue:366
#: frontend/src/components/Layouts/AppSidebar.vue:374
msgid "Convert lead to deal"
msgstr "Prebaci potencijalnog klijenta u poslovnu priliku"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:195
#: frontend/src/components/Modals/ConvertToDealModal.vue:19
#: frontend/src/pages/Lead.vue:45 frontend/src/pages/MobileLead.vue:106
msgid "Convert to Deal"
msgstr "Prebaci u poslovnu priliku"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Prebačeno"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "Uspešno prebačeno"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Kopirano u međuspremnik"

#: crm/api/dashboard.py:607 crm/api/dashboard.py:736 crm/api/dashboard.py:794
#: crm/api/dashboard.py:891
msgid "Count"
msgstr "Brojčano"

#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/ContactModal.vue:41
#: frontend/src/components/Modals/CreateDocumentModal.vue:93
#: frontend/src/components/Modals/DealModal.vue:67
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/LeadModal.vue:38
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/OrganizationModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Modals/ViewModal.vue:16
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/pages/CallLogs.vue:11 frontend/src/pages/Contacts.vue:13
#: frontend/src/pages/Contacts.vue:60 frontend/src/pages/Deals.vue:13
#: frontend/src/pages/Deals.vue:236 frontend/src/pages/Leads.vue:13
#: frontend/src/pages/Leads.vue:262 frontend/src/pages/Notes.vue:7
#: frontend/src/pages/Notes.vue:93 frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:11
#: frontend/src/pages/Tasks.vue:185
msgid "Create"
msgstr "Kreiraj"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "Kreiraj poslovnu priliku"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Kreiraj potencijalnog klijenta"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/SidePanelLayout.vue:137
msgid "Create New"
msgstr "Kreiraj novi"

#: frontend/src/components/Activities/Activities.vue:384
#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Create Note"
msgstr "Kreiraj belešku"

#: frontend/src/components/Activities/Activities.vue:399
#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Create Task"
msgstr "Kreiraj zadatak"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "Kreiraj prikaz"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "Kreiraj klijenta pri promeni statusa"

#: frontend/src/components/Modals/CallLogDetailModal.vue:152
msgid "Create lead"
msgstr "Kreiraj potencijalnog klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:344
msgid "Create your first lead"
msgstr "Kreiraj svoj prvog potencijalnog klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:414
msgid "Create your first note"
msgstr "Kreiraj svoju prvu belešku"

#: frontend/src/components/Layouts/AppSidebar.vue:394
msgid "Create your first task"
msgstr "Kreiraj svoj prvi zadatak"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:38
msgid "Currency"
msgstr "Valuta"

#: frontend/src/components/Settings/General/CurrencySettings.vue:9
msgid "Currency & Exchange rate provider"
msgstr "Valuta i provajder deviznih kurseva"

#: frontend/src/components/Settings/General/CurrencySettings.vue:188
msgid "Currency set as {0} successfully"
msgstr "Valuta je uspešno postavljena kao {0}"

#: crm/api/dashboard.py:839
msgid "Current pipeline distribution"
msgstr "Trenutna raspodela prodajnog toka"

#: frontend/src/components/Layouts/AppSidebar.vue:586
msgid "Custom actions"
msgstr "Prilagođene radnje"

#: frontend/src/components/Layouts/AppSidebar.vue:536
msgid "Custom branding"
msgstr "Prilagođeno brendiranje"

#: frontend/src/components/Layouts/AppSidebar.vue:585
msgid "Custom fields"
msgstr "Prilagođena polja"

#: frontend/src/components/Layouts/AppSidebar.vue:588
msgid "Custom list actions"
msgstr "Prilagođene radnje na listi"

#: frontend/src/components/Layouts/AppSidebar.vue:587
msgid "Custom statuses"
msgstr "Prilagođeni statusi"

#: frontend/src/pages/Deal.vue:486
msgid "Customer created successfully"
msgstr "Klijent je uspešno kreiran"

#: frontend/src/components/Layouts/AppSidebar.vue:582
msgid "Customization"
msgstr "Prilagođavanje"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "Prilagodi brze filtere"

#: crm/api/dashboard.py:599
msgid "Daily performance of leads, deals, and wins"
msgstr "Dnevne performanse po potencijalnim klijentima, poslovnim prilikama i dobijenim poslovima"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:575
#: frontend/src/pages/Deal.vue:555 frontend/src/pages/Lead.vue:421
#: frontend/src/pages/MobileDeal.vue:447 frontend/src/pages/MobileLead.vue:354
msgid "Data"
msgstr "Podaci"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "Polja za podatak"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:601 crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "Date"
msgstr "Datum"

#: frontend/src/components/Layouts/AppSidebar.vue:546
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:62
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:62
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:129
msgid "Deal"
msgstr "Poslovna prilika"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Vlasnik poslovne prilike"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "Status poslovne prilike"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "Statusi poslovne prilike"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "Vrednost poslovne prilike"

#: crm/api/dashboard.py:977
msgid "Deal generation channel analysis"
msgstr "Analiza kanala za generisanje poslovnih prilika"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:531
#: frontend/src/pages/MobileOrganization.vue:475
#: frontend/src/pages/Organization.vue:484
msgid "Deal owner"
msgstr "Vlasnik poslovne prilike"

#: crm/api/dashboard.py:1030 crm/api/dashboard.py:1087
msgid "Deal value"
msgstr "Vrednost poslovne prilike"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:500 frontend/src/pages/MobileContact.vue:291
#: frontend/src/pages/MobileDeal.vue:386
#: frontend/src/pages/MobileOrganization.vue:328
msgid "Deals"
msgstr "Poslovne prilike"

#: crm/api/dashboard.py:788
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "Poslovne prilike po tekućoj i dobijenoj fazi"

#: crm/api/dashboard.py:1076
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "Poslovne prilike po prodavcu"

#: crm/api/dashboard.py:976
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "Poslovne prilike po izvoru"

#: crm/api/dashboard.py:838
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "Poslovne prilike po fazi"

#: crm/api/dashboard.py:1019
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "Poslovne prilike po teritoriji"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:115
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:115
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "Zdravo {{ lead_name }}, \\n\\nOvo je podsetnik za iznos od {{ grand_total }}.\\n\\nHvala, \\nFrappé"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Podrazumevano"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Podrazumevana prijemna pošta"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Podrazumevani ulazni"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "Podrazumevano sredstvo"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Podrazumevani izlazni"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Podrazumevani prioritet"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Podrazumevano slanje"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Podrazumevano slanje i prijemna pošta"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "Podrazumevani sporazum o nivou usluge već postoji za {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "Podrazumevano sredstvo za pozivanje za prijavljene korisnike"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "Podrazumevano sredstvo za pozivanje je uspešno postavljeno na {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "Podrazumevano sredstvo za pozivanje je uspešno ažurirano"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "Podrazumevano sredstvo"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "Podrazumevani statusi, prilagođena polja i rasporedi su uspešno vraćeni."

#: frontend/src/components/Activities/AttachmentArea.vue:142
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:316
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:89
#: frontend/src/components/Kanban/KanbanView.vue:225
#: frontend/src/components/ListBulkActions.vue:177
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:235
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:238
#: frontend/src/components/ViewControls.vue:1161
#: frontend/src/components/ViewControls.vue:1172
#: frontend/src/pages/Contact.vue:103 frontend/src/pages/Deal.vue:124
#: frontend/src/pages/Lead.vue:183 frontend/src/pages/MobileContact.vue:82
#: frontend/src/pages/MobileContact.vue:266
#: frontend/src/pages/MobileDeal.vue:517
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:267
#: frontend/src/pages/Notes.vue:40 frontend/src/pages/Organization.vue:83
#: frontend/src/pages/Tasks.vue:369
msgid "Delete"
msgstr "Obriši"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "Obriši i vrati"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "Obriši zadatak"

#: frontend/src/components/ViewControls.vue:1157
#: frontend/src/components/ViewControls.vue:1165
msgid "Delete View"
msgstr "Obriši prikaz"

#: frontend/src/components/DeleteLinkedDocModal.vue:65
msgid "Delete all"
msgstr "Obriši sve"

#: frontend/src/components/Activities/AttachmentArea.vue:62
#: frontend/src/components/Activities/AttachmentArea.vue:138
msgid "Delete attachment"
msgstr "Obriši prilog"

#: frontend/src/pages/MobileContact.vue:262
msgid "Delete contact"
msgstr "Obriši kontakt"

#: frontend/src/components/DeleteLinkedDocModal.vue:229
msgid "Delete linked item"
msgstr "Obriši povezanu stavku"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "Obriši ili poništi povezivanje povezanih dokumenata"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "Obrišite ili poništite povezivanje ovih dokumenata pre nego što obrišete ovaj dokument"

#: frontend/src/pages/MobileOrganization.vue:263
msgid "Delete organization"
msgstr "Obriši organizaciju"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete {0} item(s)"
msgstr "Obriši {0} stavku"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "Obriši {0} stavki"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Modals/TaskModal.vue:49
msgid "Description"
msgstr "Opis"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Radna površina"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:286
#: frontend/src/pages/MobileDeal.vue:426 frontend/src/pages/MobileLead.vue:333
#: frontend/src/pages/MobileOrganization.vue:323
msgid "Details"
msgstr "Detalji"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "Uređaj"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Onemogući"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Onemogućeno"

#: frontend/src/components/CommentBox.vue:76
#: frontend/src/components/EmailEditor.vue:158
msgid "Discard"
msgstr "Odbaci"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Popust %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Iznos popusta"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "DocType"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "Vrsta dokumenta"

#: frontend/src/data/document.js:28
msgid "Document does not exist"
msgstr "Dokument ne postoji"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "Dokument nije pronađen"

#: frontend/src/data/document.js:42
msgid "Document updated successfully"
msgstr "Dokument je uspešno ažuriran"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "Dokumentacija"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "Završeno"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "Prstenasti dijagram"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Preuzmi"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "Prevuci i otpusti fajlove ovde ili ih otpremi iz"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "Otpusti fajlove ovde"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "Stavke padajućeg menija"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Datum dospeća"

#: frontend/src/components/Modals/ViewModal.vue:15
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:225
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:228
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/components/ViewControls.vue:1113
msgid "Duplicate"
msgstr "Duplikat"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "Duplikat prikaza"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "Duplikat šablona"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Trajanje"

#: frontend/src/components/Layouts/AppSidebar.vue:599
#: frontend/src/components/Settings/Settings.vue:135
msgid "ERPNext"
msgstr "ERPNext"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "ERPNext CRM podešavanja"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "ERPNext API sajta"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "ERPNext URL sajta"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "ERPNext nije instaliran na trenutnom sajtu"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "ERPNext nije integrisan as CRM-om"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "ERPNext podešavanja"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "ERPNext podešavanja su ažurirana"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:319
#: frontend/src/components/FieldLayoutEditor.vue:345
#: frontend/src/components/ListBulkActions.vue:170
#: frontend/src/components/ViewControls.vue:1131
#: frontend/src/pages/Dashboard.vue:19
msgid "Edit"
msgstr "Uredi"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "Edit Call Log"
msgstr "Uredi evidenciju poziva"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "Uredi raspored polja sa podacima"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "Uredi imejl"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "Uredi raspored polja"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "Uredi raspored polja u tabeli"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "Uredi raspored polja u redu tabele"

#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Edit Note"
msgstr "Uredi belešku"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "Uredi raspored za brzi unos"

#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Edit Task"
msgstr "Uredi zadatak"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "Uredi prikaz"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "Uredi belešku"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "Uredi zadatak"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "Uređivanje reda {0}"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:523
#: frontend/src/pages/MobileContact.vue:521
#: frontend/src/pages/MobileOrganization.vue:465
#: frontend/src/pages/MobileOrganization.vue:493
#: frontend/src/pages/Organization.vue:474
#: frontend/src/pages/Organization.vue:502
msgid "Email"
msgstr "Imejl"

#: frontend/src/components/Settings/Settings.vue:107
msgid "Email Accounts"
msgstr "Imejl nalozi"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "Imejl ID je obavezan"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "Imejl poslat u"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:113
msgid "Email Templates"
msgstr "Imejl šabloni"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "Imejl nalog je uspešno kreiran"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "Imejl nalog je uspešno ažuriran"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "Imejl nalozi"

#: frontend/src/components/Layouts/AppSidebar.vue:573
msgid "Email communication"
msgstr "Imejl komunikacija"

#: frontend/src/components/EmailEditor.vue:206
msgid "Email from Lead"
msgstr "Imejl od potencijalnog klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:552
msgid "Email template"
msgstr "Imejl šablon"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "Imejl šabloni"

#: frontend/src/pages/Deal.vue:545 frontend/src/pages/Lead.vue:411
#: frontend/src/pages/MobileDeal.vue:437 frontend/src/pages/MobileLead.vue:344
msgid "Emails"
msgstr "Imejlovi"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Prazno"

#: frontend/src/components/Filter.vue:124
msgid "Empty - Choose a field to filter by"
msgstr "Prazno - Izaberite polje po kojem želite da filtirate"

#: frontend/src/components/SortBy.vue:134
msgid "Empty - Choose a field to sort by"
msgstr "Prazno - Izaberite polje po kojem želite da sortirate"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Omogući"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "Omogući prognoziranje"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Omogući ulazni"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Omogući izlaznu"

#: frontend/src/components/Settings/General/GeneralSettings.vue:19
msgid "Enable forecasting"
msgstr "Omogući prognoziranje"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:33
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:33
msgid "Enabled"
msgstr "Omogućeno"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Datum završetka"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "End Time"
msgstr "Vreme završetka"

#: frontend/src/components/Settings/General/CurrencySettings.vue:122
msgid "Enter access key"
msgstr "Unesite ključ za pristup"

#: frontend/src/components/FieldLayout/Field.vue:334
msgid "Enter {0}"
msgstr "Unesite {0}"

#: frontend/src/components/Filter.vue:67 frontend/src/components/Filter.vue:100
#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:310
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:332
#: frontend/src/components/Filter.vue:348
msgid "Equals"
msgstr "Jednako"

#: frontend/src/components/Modals/ConvertToDealModal.vue:185
msgid "Error converting to deal: {0}"
msgstr "Greška prilikom prebacivanja u poslovnu priliku: {0}"

#: frontend/src/pages/Deal.vue:739 frontend/src/pages/Lead.vue:486
#: frontend/src/pages/MobileDeal.vue:614 frontend/src/pages/MobileLead.vue:415
msgid "Error updating field"
msgstr "Greška prilikom ažuriranja polja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:261
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "Greška prilikom kreiranja klijenta u ERPNext-u, molimo Vas da proverite evidenciju grešaka za više detalja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:173
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "Greška prilikom kreiranja potencijalnog kupca u ERPNext-u, molimo Vas da proverite evidenciju grešaka za više detalja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "Greška prilikom preuzimanja podataka o klijentima u ERPNext-u, molimo Vas da proverite evidenciju grešaka za više detalja"

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Devizni kurs"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "Provajder deviznih kurseva"

#: frontend/src/components/Settings/General/CurrencySettings.vue:67
msgid "Exchange rate provider"
msgstr "Provajder deviznih kurseva"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:597
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel izuzetak"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel broj"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Exotel broj nedostaje"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel broj {0} nije važeći"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel nije omogućen"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel podešavanja su uspešno ažurirana"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Proširi"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "Očekivani datum zatvaranja"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:161
msgid "Expected Closure Date is required."
msgstr "Očekivani datum zatvaranja je obavezan."

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "Očekivana vrednost poslovne prilike"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:159
msgid "Expected Deal Value is required."
msgstr "Očekivana vrednost poslovne prilike je obavezna."

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Isteklo"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Izvoz"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "Izvoz svih {0} zapisa"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Vrsta izvoza"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "FCRM beleška"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "FCRM podešavanja"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Neuspešno"

#: frontend/src/components/Modals/AddExistingUserModal.vue:109
msgid "Failed to add users"
msgstr "Neuspešno dodavanje korisnika"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "Neuspešno snimanje putem Twilio"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "Neuspešno kreiranje imejl naloga, nevažeći kredencijali"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:182
msgid "Failed to create template"
msgstr "Neuspešno kreiranje šablona"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:217
msgid "Failed to delete template"
msgstr "Neuspešno brisanje šablona"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "Neuspešno preuzimanje deviznog kursa iz {0} u {1} na {2}. Proverite svoju internet konekciju ili pokušajte ponovo kasnije."

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "Neuspešno učitavanje form controller-a: {0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:246
msgid "Failed to rename template"
msgstr "Neuspešno preimenovanje šablona"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "Neuspešno ažuriranje statusa poziva na Twilio"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "Neuspešno ažuriranje imejl naloga, nevažeći kredencijali"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "Neuspešno ažuriranje lozinke"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "Neuspešno ažuriranje profila"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:212
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:202
msgid "Failed to update template"
msgstr "Neuspešno ažuriranje šablona"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:81
msgid "Favicon"
msgstr "Favicon"

#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Polje"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:51
msgid "Fields Order"
msgstr "Redosled polja"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "Fajl \"{0}\" je preskočen zbog nevažeće vrste fajla"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "Fajl \"{0}\" je preskočen jer je dozvoljeno samo {1} otpremanja"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "Fajl \"{0}\" je preskočen jer je dozvoljeno samo {1} otpremanja za DocType \"{2}\""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Filter"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Filteri"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:112
#: frontend/src/components/Filter.vue:58 frontend/src/components/Filter.vue:89
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:106
#: frontend/src/components/SortBy.vue:140
msgid "First Name"
msgstr "Ime"

#: frontend/src/components/Modals/LeadModal.vue:135
msgid "First Name is mandatory"
msgstr "Ime je obavezno"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Prvi odgovor dat na"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "Rok za prvi odgovor"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "Vreme za prvi odgovor"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Vreme za prvi odgovor"

#: frontend/src/components/Filter.vue:131
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "Ime"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
msgid "For"
msgstr "Za"

#: crm/api/dashboard.py:666
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "Prognoza prihoda"

#: frontend/src/components/Settings/General/GeneralSettings.vue:100
msgid "Forecasting disabled successfully"
msgstr "Prognoziranje je uspešno onemogućeno"

#: frontend/src/components/Settings/General/GeneralSettings.vue:99
msgid "Forecasting enabled successfully"
msgstr "Prognoziranje je uspešno omogućeno"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "Obrazac"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "Skripta obrasca je uspešno ažurirana"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:603
msgid "Frappe CRM mobile"
msgstr "Frappe CRM mobilna aplikacija"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Petak"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "Od"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "Datum početka"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "Od vrste"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "Od strane korisnika"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Ispunjeno"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Ime i prezime"

#: crm/api/dashboard.py:728
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "Konverzija u prodajnom levku"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Rod"

#: frontend/src/components/Settings/General/GeneralSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:89
msgid "General"
msgstr "Opšte"

#: crm/api/dashboard.py:1020
msgid "Geographic distribution of deals and revenue"
msgstr "Geografska raspodela poslovnih prilika i prihoda"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "GitHub repozitorijum"

#: frontend/src/pages/Deal.vue:104 frontend/src/pages/Lead.vue:159
msgid "Go to website"
msgstr "Idi na veb-sajt"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "Red u tabeli"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Grupisano po"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "Grupisano po polju"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Grupisano po: "

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Pomoć"

#: frontend/src/components/CommunicationArea.vue:62
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "Zdravo Petre, \\n\\nMožete li molim Vas da pružite više detalja o ovome..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Sakriveno"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Hide"
msgstr "Sakrij"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "Sakrij lozinku"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Hide Recording"
msgstr "Sakrij snimak"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Hide border"
msgstr "Sakrij ivicu"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Hide label"
msgstr "Sakrij oznaku"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "Sakrij prikaz"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Visok"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Lista praznika"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Naziv liste praznika"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Praznici"

#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/General/HomeActions.vue:9
msgid "Home actions"
msgstr "Radnje na početnoj stranici"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Ikonica"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "Ukoliko je omogućeno, svi odlazni imejlovi će se slati sa ovog naloga. Napomena: Isključivo jedan nalog može biti podrazumevani za odlazne poruke."

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "Ukoliko je omogućeno, svi odgovori ka Vašoj kompaniji (npr. odgovori@vašakompanija.com) dolaziće na ovaj nalog. Napomena: Isključivo jedan nalog može biti podrazumevani za dolazne poruke."

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "Ukoliko je omogućeno, odlazni imejlovi mogu biti poslati sa ovog naloga."

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "Ukoliko je omogućeno, zapisi mogu biti kreirani iz dolaznih imejlova na ovom nalogu."

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Slika"

#: frontend/src/components/Filter.vue:276
#: frontend/src/components/Filter.vue:297
#: frontend/src/components/Filter.vue:312
#: frontend/src/components/Filter.vue:325
#: frontend/src/components/Filter.vue:339
msgid "In"
msgstr "U"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "U toku"

#: frontend/src/components/SLASection.vue:75
msgid "In less than a minute"
msgstr "Za manje od jednog minuta"

#: frontend/src/components/Activities/CallArea.vue:35
msgid "Inbound Call"
msgstr "Dolazni poziv"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Prijemna pošta"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Dolazni"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "Dolazni poziv..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "Industrije"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Industrija"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Započeto"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "Započinjanje poziva..."

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Integration"
msgstr "Integracija"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "Integracija nije omogućena"

#: frontend/src/components/Settings/Settings.vue:120
msgctxt "FCRM"
msgid "Integrations"
msgstr "Integracije"

#: frontend/src/components/Layouts/AppSidebar.vue:524
#: frontend/src/components/Layouts/AppSidebar.vue:527
msgid "Introduction"
msgstr "Uvod"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "Nevažeći SID naloga ili autentifikacioni token."

#: frontend/src/components/Modals/DealModal.vue:213
#: frontend/src/components/Modals/LeadModal.vue:154
msgid "Invalid Email"
msgstr "Nevažeći imejl"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "Nevažeći Exotel broj"

#: crm/api/dashboard.py:73
msgid "Invalid chart name"
msgstr "Nevažeći naziv dijagrama"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "Nevažeći kredencijali"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "Nevažeći imejl ID"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "Pozovite novog korisnika"

#: frontend/src/components/Settings/Settings.vue:101
msgid "Invite User"
msgstr "Pozovite korisnika"

#: frontend/src/components/Settings/InviteUserPage.vue:56
msgid "Invite as"
msgstr "Pozovite kao"

#: frontend/src/components/Settings/InviteUserPage.vue:29
msgid "Invite by email"
msgstr "Pozovite putem imejla"

#: frontend/src/components/Layouts/AppSidebar.vue:538
msgid "Invite users"
msgstr "Pozovite korisnike"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "Pozovite korisnike da pristupe CRM. Navedite njihove uloge radi kontrole pristupa i dozvola"

#: frontend/src/components/Layouts/AppSidebar.vue:354
msgid "Invite your team"
msgstr "Pozovite svoj tim"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "Pozvan od strane"

#: frontend/src/components/Filter.vue:278
#: frontend/src/components/Filter.vue:287
#: frontend/src/components/Filter.vue:299
#: frontend/src/components/Filter.vue:314
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:341
#: frontend/src/components/Filter.vue:350
msgid "Is"
msgstr "Jeste"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Podrazumevano"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "Da li je ERPNext instaliran na drugom sajtu?"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Grupa"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Primarno"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Standardno"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "Učiniće da su \"Očekivani datum zatvaranja\" i \"Očekivana vrednost poslovne prilike\" obavezni za precizno dobijanje uvida u prognozu"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Radno mesto"

#: frontend/src/components/Filter.vue:75 frontend/src/components/Filter.vue:108
#: frontend/src/components/Modals/AssignmentModal.vue:35
#: frontend/src/components/Modals/TaskModal.vue:76
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "Petar Petrović"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Kanban"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "Kanban kolone"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "Kanban polja"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:11
msgid "Kanban Settings"
msgstr "Kanban podešavanje"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Key"
msgstr "Ključ"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:109
msgid "Label"
msgstr "Oznaka"

#: frontend/src/components/Filter.vue:620
msgid "Last 6 Months"
msgstr "Poslednjih 6 meseci"

#: frontend/src/components/Filter.vue:612
msgid "Last Month"
msgstr "Prošli mesec"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Prezime"

#: frontend/src/components/Filter.vue:616
msgid "Last Quarter"
msgstr "Prošli kvartal"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "Evidencija poslednje promene statusa"

#: frontend/src/components/Filter.vue:608
msgid "Last Week"
msgstr "Prošla nedelja"

#: frontend/src/components/Filter.vue:624
msgid "Last Year"
msgstr "Prošla godina"

#: frontend/src/pages/Contact.vue:538 frontend/src/pages/MobileContact.vue:536
#: frontend/src/pages/MobileOrganization.vue:480
#: frontend/src/pages/MobileOrganization.vue:508
#: frontend/src/pages/Organization.vue:489
#: frontend/src/pages/Organization.vue:517
msgid "Last modified"
msgstr "Poslednji put izmenjeno"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "Prezime"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "Raspored"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:545
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:58
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:58
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:130
msgid "Lead"
msgstr "Potencijalni klijent"

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Detalji potencijalnog klijenata"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Naziv potencijalnog klijenta"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Vlasnik potencijalnog klijenta"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:73
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "Vlasnik potencijalnog klijenta ne može biti isti kao imejl adresa potencijalnog klijenta"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "Izvori potencijalnog klijenta"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "Statusi potencijalnog klijenta"

#: crm/api/dashboard.py:935
msgid "Lead generation channel analysis"
msgstr "Analiza kanala za generisanje potencijalnih klijenata"

#: crm/api/dashboard.py:729
msgid "Lead to deal conversion pipeline"
msgstr "Tok prebacivanja potencijalnih klijenata u poslovne prilike"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Lead.vue:369 frontend/src/pages/MobileLead.vue:293
msgid "Leads"
msgstr "Potencijalni klijenti"

#: crm/api/dashboard.py:934
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "Potencijalni klijenti po izvoru"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Levo"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "Biblioteka"

#: frontend/src/components/Filter.vue:274
#: frontend/src/components/Filter.vue:285
#: frontend/src/components/Filter.vue:295
#: frontend/src/components/Filter.vue:323
#: frontend/src/components/Filter.vue:337
msgid "Like"
msgstr "Lajk"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Link"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Linkovi"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Lista"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Listen"
msgstr "Slušaj"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "Učitaj podrazumevane kolone"

#: frontend/src/components/Kanban/KanbanView.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Učitaj više"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:37
#: frontend/src/pages/Deal.vue:193 frontend/src/pages/MobileDeal.vue:119
msgid "Loading..."
msgstr "Učitavanje..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Evidencija"

#: frontend/src/components/Activities/Activities.vue:803
#: frontend/src/components/Activities/ActivityHeader.vue:137
#: frontend/src/components/Activities/ActivityHeader.vue:180
msgid "Log a Call"
msgstr "Evidentiraj poziv"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Prijavljivanje na Frappe Cloud?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:47
msgid "Logo"
msgstr "Logo"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Izgubljeno"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "Beleške o gubicima"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Razlog gubitka"

#: crm/api/dashboard.py:883
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "Razlozi gubitaka poslovnih prilika"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "Beleške o gubicima"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "Beleške o gubicima su obavezne kada je razlog gubitka \"Ostalo\""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "Razlog gubitka"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "Razlog gubitka je obavezan"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Nizak"

#: frontend/src/pages/Contact.vue:94 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "Pozovi"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Private"
msgstr "Postavi kao privatno"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Public"
msgstr "Postavi kao javno"

#: frontend/src/components/Activities/Activities.vue:807
#: frontend/src/components/Activities/ActivityHeader.vue:142
#: frontend/src/components/Activities/ActivityHeader.vue:185
#: frontend/src/pages/Deals.vue:504 frontend/src/pages/Leads.vue:531
msgid "Make a Call"
msgstr "Pozovi"

#: frontend/src/pages/Deal.vue:86 frontend/src/pages/Lead.vue:128
msgid "Make a call"
msgstr "Pozovi"

#: frontend/src/components/Activities/AttachmentArea.vue:109
msgid "Make attachment {0}"
msgstr "Dodaj prilog {0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "Pozovi"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "Postavi kao privatno"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "Postavi kao javno"

#: frontend/src/components/Activities/AttachmentArea.vue:118
msgid "Make {0}"
msgstr "Napravite {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "Postavi {0} kao podrazumevano sredstvo za pozivanje"

#: frontend/src/components/Settings/General/GeneralSettings.vue:23
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "Postavlja \"Očekivani datum zatvaranja\" i \"Očekivana vrednost poslovne prilike\" kao obavezna polja za prognoziranje vrednosti poslovne prilike"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "Upravljajte CRM korisnicima dodavanjem ili pozivanjem, i dodeljivanjem uloga kako biste kontrolisali njihov pristup i ovlašćenja"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "Upravljajte svojim imejl nalozima za slanje i prijem poruka direktno iz CRM. Možete dodati više naloga i postaviti jedan kao podrazumevani za dolazne i odlazne imejlove."

#: frontend/src/components/Modals/AddExistingUserModal.vue:91
#: frontend/src/components/Settings/InviteUserPage.vue:171
#: frontend/src/components/Settings/InviteUserPage.vue:178
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:185
#: frontend/src/components/Settings/Users.vue:256
#: frontend/src/components/Settings/Users.vue:259
msgid "Manager"
msgstr "Menadžer"

#: frontend/src/data/document.js:54
msgid "Mandatory field error: {0}"
msgstr "Greška u obaveznom polju: {0}"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Ručno"

#: frontend/src/components/Notifications.vue:19
#: frontend/src/pages/MobileNotification.vue:11
#: frontend/src/pages/MobileNotification.vue:14
msgid "Mark all as read"
msgstr "Označi sve kao pročitano"

#: frontend/src/components/Layouts/AppSidebar.vue:542
msgid "Masters"
msgstr "Master podaci"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Srednje"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "Pominjanje"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Poruka"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Srednje ime"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "Broj mobilnog telefona"

#: frontend/src/components/Modals/DealModal.vue:209
#: frontend/src/components/Modals/LeadModal.vue:150
msgid "Mobile No should be a number"
msgstr "Broj mobilnog telefona treba da bude broj"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "Broj mobilnog telefona."

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "Broj mobilnog telefona"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "Broj mobilnog telefona nedostaje"

#: frontend/src/components/Layouts/AppSidebar.vue:606
msgid "Mobile app installation"
msgstr "Instalacija mobilne aplikacije"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:526
#: frontend/src/pages/MobileOrganization.vue:470
#: frontend/src/pages/Organization.vue:479
msgid "Mobile no"
msgstr "Broj mobilnog telefona"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Ponedeljak"

#: crm/api/dashboard.py:669
msgid "Month"
msgstr "Mesec"

#: frontend/src/components/FieldLayoutEditor.vue:454
msgid "Move to next section"
msgstr "Idi na sledeći odeljak"

#: frontend/src/components/FieldLayoutEditor.vue:407
msgid "Move to next tab"
msgstr "Idi na sledeću karticu"

#: frontend/src/components/FieldLayoutEditor.vue:464
msgid "Move to previous section"
msgstr "Idi na prethodni odeljak"

#: frontend/src/components/FieldLayoutEditor.vue:393
msgid "Move to previous tab"
msgstr "Idi na prethodnu karticu"

#: frontend/src/components/Modals/ViewModal.vue:40
msgid "My Open Deals"
msgstr "Moje otvorene poslovne prilike"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:42
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:42
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:488
#: frontend/src/pages/Organization.vue:497
msgid "Name"
msgstr "Naziv"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:157
msgid "Name is required"
msgstr "Naziv je obavezan"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Serije imenovanja"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Neto iznos"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Neto ukupno"

#: frontend/src/components/Activities/ActivityHeader.vue:82
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Novi"

#: frontend/src/components/Modals/AddressModal.vue:94
msgid "New Address"
msgstr "Nova adresa"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "New Call Log"
msgstr "Nova evidencija poziva"

#: frontend/src/components/Activities/Activities.vue:394
#: frontend/src/components/Activities/ActivityHeader.vue:27
#: frontend/src/components/Activities/ActivityHeader.vue:132
msgid "New Comment"
msgstr "Novi komentar"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "Novi kontakt"

#: frontend/src/components/Activities/Activities.vue:389
#: frontend/src/components/Activities/ActivityHeader.vue:17
#: frontend/src/components/Activities/ActivityHeader.vue:127
msgid "New Email"
msgstr "Novi imejl"

#: frontend/src/components/Activities/ActivityHeader.vue:73
msgid "New Message"
msgstr "Nova poruka"

#: frontend/src/components/Activities/ActivityHeader.vue:42
#: frontend/src/components/Activities/ActivityHeader.vue:148
#: frontend/src/pages/Deals.vue:510 frontend/src/pages/Leads.vue:537
msgid "New Note"
msgstr "Nova beleška"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "Nova organizacija"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Nova lozinka"

#: frontend/src/components/FieldLayoutEditor.vue:203
#: frontend/src/components/SidePanelLayoutEditor.vue:133
msgid "New Section"
msgstr "Novi odeljak"

#: frontend/src/components/FieldLayoutEditor.vue:299
#: frontend/src/components/FieldLayoutEditor.vue:304
msgid "New Tab"
msgstr "Nova kartica"

#: frontend/src/components/Activities/ActivityHeader.vue:52
#: frontend/src/components/Activities/ActivityHeader.vue:153
#: frontend/src/pages/Deals.vue:515 frontend/src/pages/Leads.vue:542
msgid "New Task"
msgstr "Novi zadatak"

#: frontend/src/components/Activities/ActivityHeader.vue:163
msgid "New WhatsApp Message"
msgstr "Nova WhatsApp poruka"

#: frontend/src/components/Modals/ConvertToDealModal.vue:81
#: frontend/src/pages/MobileLead.vue:164
msgid "New contact will be created based on the person's details"
msgstr "Novi kontakt će biti kreiran na osnovu podataka o osobi"

#: frontend/src/components/Modals/ConvertToDealModal.vue:56
#: frontend/src/pages/MobileLead.vue:138
msgid "New organization will be created based on the data in details section"
msgstr "Nova organizacija će biti kreirana na osnovu podataka u odeljku detalji"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "Novi šablon"

#: frontend/src/components/Modals/CreateDocumentModal.vue:89
msgid "New {0}"
msgstr "Novi {0}"

#: frontend/src/components/Filter.vue:668
msgid "Next 6 Months"
msgstr "Sledećih 6 meseci"

#: frontend/src/components/Filter.vue:660
msgid "Next Month"
msgstr "Sledeći mesec"

#: frontend/src/components/Filter.vue:664
msgid "Next Quarter"
msgstr "Sledeći kvartal"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "Sledeći korak"

#: frontend/src/components/Filter.vue:656
msgid "Next Week"
msgstr "Sledeća nedelja"

#: frontend/src/components/Filter.vue:672
msgid "Next Year"
msgstr "Sledeća godina"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Ne"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "Bez odgovora"

#: frontend/src/components/Controls/Grid.vue:309
msgid "No Data"
msgstr "Bez podataka"

#: frontend/src/components/Kanban/KanbanView.vue:102
#: frontend/src/pages/Deals.vue:106 frontend/src/pages/Leads.vue:122
#: frontend/src/pages/Tasks.vue:68
msgid "No Title"
msgstr "Bez naslova"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "Nije izvršena nijedna promena"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:282 frontend/src/pages/MobileDeal.vue:207
msgid "No contacts added"
msgstr "Nema dodatnih kontakata"

#: frontend/src/pages/Deal.vue:97 frontend/src/pages/Lead.vue:150
msgid "No email set"
msgstr "Nije postavljen imejla"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "Nisu pronađeni šabloni imejlova"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "Bez oznake"

#: frontend/src/pages/Deal.vue:705
msgid "No mobile number set"
msgstr "Nije postavljen broj mobilnog telefona"

#: frontend/src/components/Notifications.vue:83
#: frontend/src/pages/MobileNotification.vue:67
msgid "No new notifications"
msgstr "Nema novih obaveštenja"

#: frontend/src/pages/Lead.vue:135
msgid "No phone number set"
msgstr "Nije postavljen broj telefona"

#: frontend/src/pages/Deal.vue:700
msgid "No primary contact set"
msgstr "Nije postavljen primarni kontakt"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:72
#: frontend/src/components/Controls/MultiSelectUserInput.vue:72
msgid "No results found"
msgstr "Nema rezultata"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "Nijedan obrazac nije pronađen"

#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "Nisu pronađeni korisnici"

#: frontend/src/pages/MobileOrganization.vue:284
#: frontend/src/pages/Organization.vue:300
msgid "No website found"
msgstr "Nije pronađen veb-sajt"

#: frontend/src/pages/Deal.vue:110 frontend/src/pages/Lead.vue:165
msgid "No website set"
msgstr "Nije postavljen veb-sajt"

#: frontend/src/components/SidePanelLayout.vue:128
msgid "No {0} Available"
msgstr "Nema dostupnih {0}"

#: frontend/src/pages/CallLogs.vue:56 frontend/src/pages/Contact.vue:159
#: frontend/src/pages/Contacts.vue:59 frontend/src/pages/Deals.vue:235
#: frontend/src/pages/Leads.vue:261 frontend/src/pages/MobileContact.vue:150
#: frontend/src/pages/MobileOrganization.vue:142
#: frontend/src/pages/Notes.vue:92 frontend/src/pages/Organization.vue:156
#: frontend/src/pages/Organizations.vue:59 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "Nije pronađen nijedan {0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Broj zaposlenih lica"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "Normalno"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Nije dozvoljeno"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:311
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:349
msgid "Not Equals"
msgstr "Nije jednako"

#: frontend/src/components/Filter.vue:277
#: frontend/src/components/Filter.vue:298
#: frontend/src/components/Filter.vue:313
#: frontend/src/components/Filter.vue:326
#: frontend/src/components/Filter.vue:340
msgid "Not In"
msgstr "Nije u"

#: frontend/src/components/Filter.vue:275
#: frontend/src/components/Filter.vue:286
#: frontend/src/components/Filter.vue:296
#: frontend/src/components/Filter.vue:324
#: frontend/src/components/Filter.vue:338
msgid "Not Like"
msgstr "Nije slično"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/General/BrandSettings.vue:16
#: frontend/src/components/Settings/General/CurrencySettings.vue:16
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Nije sačuvano"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:260
msgid "Not allowed to add contact to Deal"
msgstr "Nije dozvoljeno dodati kontakt u poslovnu priliku"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:408
msgid "Not allowed to convert Lead to Deal"
msgstr "Nije dozvoljeno prebaciti potencijalnog klijenta u poslovnu priliku"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:271
msgid "Not allowed to remove contact from Deal"
msgstr "Nije dozvoljeno ukloniti kontakt iz poslovne prilike"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:282
msgid "Not allowed to set primary contact for Deal"
msgstr "Nije dozvoljeno postaviti primarni kontakt za poslovnu priliku"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:549
msgid "Note"
msgstr "Beleška"

#: frontend/src/pages/Deal.vue:570 frontend/src/pages/Lead.vue:436
#: frontend/src/pages/MobileDeal.vue:463 frontend/src/pages/MobileLead.vue:370
msgid "Notes"
msgstr "Beleške"

#: frontend/src/pages/Notes.vue:20
msgid "Notes View"
msgstr "Prikaz beleški"

#: frontend/src/components/Activities/EmailArea.vue:13
#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Notification"
msgstr "Obaveštenje"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "Tekst obaveštenja"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "Vrsta obaveštenja dokumenta"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "Vrsta obaveštenja DocType"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "Obaveštenja"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Broj"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "Brojčani dijagram"

#: crm/api/dashboard.py:1027 crm/api/dashboard.py:1084
msgid "Number of deals"
msgstr "Broj poslovnih prilika"

#: crm/api/dashboard.py:1077
msgid "Number of deals and total value per salesperson"
msgstr "Broj poslovni prilika i ukupna vrednost po prodavcu"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Prethodna matična"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "Na čekanju"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "U toku"

#: crm/api/dashboard.py:181
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "Tekuće poslovne prilike"

#: frontend/src/utils/index.js:444
msgid "Only image files are allowed"
msgstr "Dozvoljeni su samo fajlovi u formatu slike"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Može biti postavljen samo jedan {0} kao primarni."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Otvoreno"

#: frontend/src/components/Modals/NoteModal.vue:18
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Open Deal"
msgstr "Otvorene poslovne prilike"

#: frontend/src/components/Modals/NoteModal.vue:19
#: frontend/src/components/Modals/TaskModal.vue:26
msgid "Open Lead"
msgstr "Otvoreni potencijalni klijent"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "Otvori u portalu"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "Otvori u novom prozoru"

#: frontend/src/pages/Organization.vue:92
msgid "Open website"
msgstr "Otvori veb-sajt"

#: frontend/src/components/Kanban/KanbanView.vue:221
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "Opcije"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "Ili kreirajte potencijalne klijente ručno"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Narudžbina od"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:548
#: frontend/src/components/Modals/ConvertToDealModal.vue:39
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:505
#: frontend/src/pages/MobileLead.vue:120
#: frontend/src/pages/MobileOrganization.vue:449
#: frontend/src/pages/MobileOrganization.vue:503
#: frontend/src/pages/Organization.vue:458
#: frontend/src/pages/Organization.vue:512
msgid "Organization"
msgstr "Organizacija"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "Detalji organizacije"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "Logo organizacije"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Naziv organizacije"

#: frontend/src/pages/Deal.vue:69
msgid "Organization logo"
msgstr "Logo organizacije"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:208
#: frontend/src/pages/Organization.vue:238
msgid "Organizations"
msgstr "Organizacije"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Other features"
msgstr "Ostale funkcionalnosti"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Ostalo"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Outbound Call"
msgstr "Odlazni poziv"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Odlazni"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Vlasnik"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "Matična CRM teritorija"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Lozinka"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "Lozinka ne može biti resetovana od strane demo korisnika {}"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "Lozinka ja obavezna"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "Lozinka je uspešno ažurirana"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:41
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:41
msgid "Payment Reminder"
msgstr "Podsetnik za plaćanje"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:72
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:72
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Podsetnik za plaćanje od Frappé - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "Na čekanju"

#: frontend/src/components/Settings/InviteUserPage.vue:66
msgid "Pending Invites"
msgstr "Pozivi na čekanju"

#: frontend/src/pages/Dashboard.vue:79
msgid "Period"
msgstr "Period"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "Osoba"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:498
#: frontend/src/pages/Organization.vue:507
msgid "Phone"
msgstr "Telefon"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "Brojevi telefona"

#: frontend/src/components/ViewControls.vue:1138
msgid "Pin View"
msgstr "Fiksiraj prikaz"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "Fiksirano"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "Fiksirani prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Pinned view"
msgstr "Fiksirani prikaz"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "Brzina reprodukcije"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "Molimo Vas da dodate imejl nalog kako biste nastavili."

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "Molimo Vas da omogućite Twilio podešavanja pre nego što pozovete."

#: frontend/src/components/FilesUploader/FilesUploader.vue:168
msgid "Please enter a valid URL"
msgstr "Molimo Vas da unesete važeći URL"

#: frontend/src/components/Settings/General/CurrencySettings.vue:159
msgid "Please enter the Exchangerate Host access key."
msgstr "Molimo Vas da unesete Exchangerate Host ključ za pristup."

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "Molimo Vas da navedete razlog označavanja ove poslovne prilike kao izgubljene"

#: frontend/src/components/Settings/General/CurrencySettings.vue:152
msgid "Please select a currency before saving."
msgstr "Molimo Vas da izaberete valutu pre čuvanja."

#: frontend/src/components/Modals/ConvertToDealModal.vue:145
#: frontend/src/pages/MobileLead.vue:434
msgid "Please select an existing contact"
msgstr "Molimo Vas da izaberete postojeći kontakt"

#: frontend/src/components/Modals/ConvertToDealModal.vue:150
#: frontend/src/pages/MobileLead.vue:439
msgid "Please select an existing organization"
msgstr "Molimo Vas da izaberete postojeću organizaciju"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "Molimo Vas da postavite Exotel integraciju"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:169
msgid "Please specify a reason for losing the deal."
msgstr "Molimo Vas da navedete razlog gubitka poslovne prilike."

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Please specify the reason for losing the deal."
msgstr "Molimo Vas da precizirate razlog gubitka poslovne prilike."

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "Pozicija"

#: frontend/src/pages/Deal.vue:222 frontend/src/pages/MobileDeal.vue:151
msgid "Primary"
msgstr "Primarna"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "Primarni imejl"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "Primarni broj mobilnog telefona"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "Primarni telefon"

#: frontend/src/pages/Deal.vue:677 frontend/src/pages/MobileDeal.vue:568
msgid "Primary contact set"
msgstr "Primarni kontakt je postavljen"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Prioriteti"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Priority"
msgstr "Prioritet"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Privatno"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Verovatnoća"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Proizvod"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "Šifra proizvoda"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "Naziv proizvoda"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Proizvodi"

#: frontend/src/components/Layouts/AppSidebar.vue:535
#: frontend/src/components/Settings/Settings.vue:79
msgid "Profile"
msgstr "Profil"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "Profil je uspešno ažuriran"

#: crm/api/dashboard.py:667
msgid "Projected vs actual revenue based on deal probability"
msgstr "Očekivani naspram stvarnih prihoda na osnovu verovatnoće poslovne prilike"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "Javni"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "Javni prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:565
msgid "Public view"
msgstr "Javni prikaz"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Količina"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "U redu"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Brzi unos"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Brzi filteri"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "Brzi filteri su uspešno ažurirani"

#: frontend/src/components/Layouts/AppSidebar.vue:589
msgid "Quick entry layout"
msgstr "Raspored za brzi unos"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Jedinična cena"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Pročitano"

#: crm/api/dashboard.py:886
msgid "Reason"
msgstr "Razlog"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "Zabeleži pozive"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "Zabeleži odlazne pozive"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "Zabeležiti URL"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "Referenca dokumenta"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "DocType referenca"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Vrsta referentnog dokumenta"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Naziv reference"

#: frontend/src/components/ViewControls.vue:25
#: frontend/src/components/ViewControls.vue:160
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Osveži"

#: frontend/src/components/Telephony/TwilioCallUI.vue:104
msgid "Reject"
msgstr "Odbij"

#: frontend/src/components/Settings/Users.vue:210
#: frontend/src/components/Settings/Users.vue:213
#: frontend/src/pages/Deal.vue:626
msgid "Remove"
msgstr "Ukloni"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "Ukloni sve"

#: frontend/src/components/FieldLayoutEditor.vue:444
msgid "Remove and move fields to previous column"
msgstr "Ukloni i premesti polja u prethodnu kolonu"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove column"
msgstr "Ukloni kolonu"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:101
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:47
msgid "Remove image"
msgstr "Ukloni sliku"

#: frontend/src/components/FieldLayoutEditor.vue:365
msgid "Remove section"
msgstr "Ukloni odeljak"

#: frontend/src/components/FieldLayoutEditor.vue:324
msgid "Remove tab"
msgstr "Ukloni karticu"

#: frontend/src/components/Activities/EmailArea.vue:31
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Odgovori"

#: frontend/src/components/Activities/EmailArea.vue:44
msgid "Reply All"
msgstr "Odgovori svima"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Prijavi problem"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "Obavezna polja"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:82
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Resetuj"

#: frontend/src/components/ColumnSettings.vue:82
msgid "Reset Changes"
msgstr "Resetuj izmene"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "Resetuj ERPNext skriptu obrasca"

#: frontend/src/components/ColumnSettings.vue:93
msgid "Reset to Default"
msgstr "Resetuj na podrazumevano"

#: frontend/src/pages/Dashboard.vue:34
msgid "Reset to default"
msgstr "Vrati na podrazumevano"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Odgovorio"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Detalji odgovora"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "Odgovor i praćenje"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Vrati"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "Vrati podrazumevano"

#: frontend/src/components/FilesUploader/FilesUploader.vue:54
msgid "Retake"
msgstr "Ponovi"

#: crm/api/dashboard.py:675
msgid "Revenue"
msgstr "Prihod"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:84
msgid "Rich Text"
msgstr "Bogati tekst"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Desno"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Zvonjenje"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Ringing..."
msgstr "Zvonjenje..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:44
msgid "Role"
msgstr "Uloga"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "Putanja"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "Naziv putanje"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "Redovi"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "Sporazum o nivou usluge"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "Kreiranje sporazuma o nivou usluge"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "Naziv sporazuma o nivou usluge"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "Status sporazuma o nivou usluge"

#: frontend/src/components/EmailEditor.vue:82
msgid "SUBJECT"
msgstr "NASLOV"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Sales Manager"
msgstr "Menadžer prodaje"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:90
#: frontend/src/components/Settings/InviteUserPage.vue:170
#: frontend/src/components/Settings/InviteUserPage.vue:177
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:186
#: frontend/src/components/Settings/Users.vue:268
#: frontend/src/components/Settings/Users.vue:271
msgid "Sales User"
msgstr "Korisnik prodaje"

#: crm/api/dashboard.py:598
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "Trend prodaje"

#: frontend/src/pages/Dashboard.vue:106
msgid "Sales user"
msgstr "Korisnik prodaje"

#: crm/api/dashboard.py:1079
msgid "Salesperson"
msgstr "Prodavac"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Pozdrav"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Subota"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:87
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/DropdownItem.vue:21
#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/Settings/General/CurrencySettings.vue:182
#: frontend/src/components/Telephony/ExotelCallUI.vue:231
#: frontend/src/components/ViewControls.vue:123
#: frontend/src/pages/Dashboard.vue:45
msgid "Save"
msgstr "Sačuvaj"

#: frontend/src/components/Modals/ViewModal.vue:13
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:157
msgid "Save Changes"
msgstr "Sačuvaj promene"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "Sačuvani prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:564
msgid "Saved view"
msgstr "Sačuvani prikaz"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "Zakažite zadatak..."

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "Skripta"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "Pretraga šablona"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "Pretraga korisnika"

#: frontend/src/components/FieldLayoutEditor.vue:342
msgid "Section"
msgstr "Odeljak"

#: frontend/src/pages/Dashboard.vue:59
msgid "Select Range"
msgstr "Izaberi opseg"

#: frontend/src/components/Settings/General/CurrencySettings.vue:58
msgid "Select currency"
msgstr "Izaberi valutu"

#: frontend/src/components/Settings/General/CurrencySettings.vue:82
msgid "Select provider"
msgstr "Izaberite provajdera"

#: frontend/src/components/FieldLayout/Field.vue:332
msgid "Select {0}"
msgstr "Izaberite {0}"

#: frontend/src/components/EmailEditor.vue:162
msgid "Send"
msgstr "Pošalji"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "Pošaljite pozivnice"

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "Send Template"
msgstr "Pošaljite šablon"

#: frontend/src/pages/Deal.vue:93 frontend/src/pages/Lead.vue:144
msgid "Send an email"
msgstr "Pošaljite kao imejl"

#: frontend/src/components/Layouts/AppSidebar.vue:455
msgid "Send email"
msgstr "Pošalji imejl"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "Pošalji pozivnice za"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Separator"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Serija"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "Pružalac usluge"

#: frontend/src/components/Layouts/AppSidebar.vue:576
msgid "Service level agreement"
msgstr "Sporazum o nivou usluge"

#: frontend/src/components/FilesUploader/FilesUploader.vue:69
msgid "Set all as private"
msgstr "Postavi sve kao privatno"

#: frontend/src/components/FilesUploader/FilesUploader.vue:62
msgid "Set all as public"
msgstr "Postavi sve kao javno"

#: frontend/src/pages/Deal.vue:80
msgid "Set an organization"
msgstr "Postavi organizaciju"

#: frontend/src/pages/Deal.vue:634 frontend/src/pages/MobileDeal.vue:525
msgid "Set as Primary Contact"
msgstr "Postavi kao primarni kontakt"

#: frontend/src/components/ViewControls.vue:1123
msgid "Set as default"
msgstr "Postavi kao podrazumevano"

#: frontend/src/components/Settings/General/CurrencySettings.vue:173
msgid "Set currency"
msgstr "Postavi valutu"

#: frontend/src/pages/Lead.vue:122
msgid "Set first name"
msgstr "Postavi ime"

#: frontend/src/components/Layouts/AppSidebar.vue:528
msgid "Setting up"
msgstr "Podešavanje"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "Podešavanje Frappe Mail zahteva da imate API ključ i API tajnu za svoj imejl nalog. Saznajte više "

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Podešavanje Gmail-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Podešavanje Outlook-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Podešavanje Sendgrid-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Podešavanje SparkPost-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Podešavanje Yahoo-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Podešavanje Yandex-a zahteva uključivanje dvofaktorske autentifikacije i lozinke za specifične aplikacije. Saznajte više "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:532
#: frontend/src/components/Settings/Settings.vue:11
#: frontend/src/components/Settings/Settings.vue:75
msgid "Settings"
msgstr "Podešavanja"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "Podesite imejl"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "Podesite provajdera deviznih kurseva kao 'Exchangerate Host' u podešavanjima, jer podrazumevani provajder ne podržava konverziju valuta iz {0} u {1}."

#: frontend/src/components/Layouts/AppSidebar.vue:334
msgid "Setup your password"
msgstr "Postavite Vašu lozinku"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Show"
msgstr "Prikaži"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "Prikaži lozinku"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Show border"
msgstr "Prikaži ivicu"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Show label"
msgstr "Prikaži oznaku"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "Prikaži prikaz"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "Bočni panel"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Stavke bočne trake"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "Jednostavni python izraz, na primer: doc.status == 'Otvoreno' and doc.lead_source == 'Reklame'"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:22
#: frontend/src/components/SortBy.vue:240
msgid "Sort"
msgstr "Sortiraj"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Modals/EditValueModal.vue:10
msgid "Source"
msgstr "Izvor"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "Source Name"
msgstr "Naziv izvora"

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "Razmak"

#: crm/api/dashboard.py:731 crm/api/dashboard.py:790
msgid "Stage"
msgstr "Faza"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "Standardne skripte obrasca ne mogu da se menjaju, umesto toga neophodan je duplikat skripti."

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Standardna prodajna cena"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "Standardni prikazi"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Datum početka"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Start Time"
msgstr "Vreme početka"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "Započni sa 10 probnih potencijalnih klijenata"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:516
#: frontend/src/pages/MobileOrganization.vue:460
#: frontend/src/pages/Organization.vue:469
msgid "Status"
msgstr "Status"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "Evidencija promena statusa"

#: frontend/src/components/Modals/DealModal.vue:217
#: frontend/src/components/Modals/LeadModal.vue:158
msgid "Status is required"
msgstr "Status je obavezan"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Poddomen"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:71
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:71
msgid "Subject"
msgstr "Naslov"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:161
msgid "Subject is required"
msgstr "Naslov je neophodan"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "Naslov: {0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Nedelja"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "Podrška / Prodaja"

#: frontend/src/components/FilesUploader/FilesUploader.vue:49
msgid "Switch camera"
msgstr "Promeni kameru"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "Sinhronizuj svoje kontakte, imejlove i kalendare"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "System Manager"
msgstr "Sistem menadžer"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "ZA"

#: frontend/src/components/Telephony/ExotelCallUI.vue:151
msgid "Take a note..."
msgstr "Zapiši belešku..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:550
msgid "Task"
msgstr "Zadatak"

#: frontend/src/pages/Deal.vue:565 frontend/src/pages/Lead.vue:431
#: frontend/src/pages/MobileDeal.vue:458 frontend/src/pages/MobileLead.vue:365
msgid "Tasks"
msgstr "Zadaci"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Telegram kanal"

#: frontend/src/components/Settings/Settings.vue:123
msgid "Telephony"
msgstr "Telefonija"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "Sredstvo telefonije"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "Podešavanje telefonije"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:178
msgid "Template created successfully"
msgstr "Šablon je uspešno kreiran"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:214
msgid "Template deleted successfully"
msgstr "Šablon je uspešno obrisan"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:198
msgid "Template disabled successfully"
msgstr "Šablon je uspešno onemogućen"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template enabled successfully"
msgstr "Šablon je uspešno omogućen"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "Naziv šablona"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:243
msgid "Template renamed successfully"
msgstr "Šablon je uspešno preimenovan"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
msgid "Template updated successfully"
msgstr "Šablon je uspešno ažuriran"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "Teritorije"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1022 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Teritorija"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Menadžer teritorije"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Naziv teritorije"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "Uslov '{0}' je nevažeći: {1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs koji se unosi za konvertovanje valute poslovne prilike u osnovnu valutu iz Vašeg CRM-a (postavljeno u CRM podešavanjima). Postavlja se jednom, prilikom dodavanja valute i ne menja se automatski."

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs koji se koristi za konvertovanje valute organizacije u osnovnu valutu iz Vašeg CRM-a (postavljeno u CRM podešavanjima). Postavlja se jednom, prilikom dodavanja valute i ne menja se automatski."

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "U tabeli prioriteta može postojati samo jedan podrazumevani prioritet"

#: frontend/src/components/Modals/AddressModal.vue:129
#: frontend/src/components/Modals/CallLogModal.vue:132
msgid "These fields are required: {0}"
msgstr "Ova polja su neophodna: {0}"

#: frontend/src/components/Filter.vue:644
msgid "This Month"
msgstr "Ovaj mesec"

#: frontend/src/components/Filter.vue:648
msgid "This Quarter"
msgstr "Ovaj kvartal"

#: frontend/src/components/Filter.vue:640
msgid "This Week"
msgstr "Ove nedelje"

#: frontend/src/components/Filter.vue:652
msgid "This Year"
msgstr "Ove godine"

#: frontend/src/components/SidePanelLayoutEditor.vue:119
msgid "This section is not editable"
msgstr "Ovaj odeljak se ne može urediti"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "Ovo će obrisati izabrane stavke i povezane stavke sa njima, da li ste sigurni?"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "Ovo će obrisati izabrane stavke i poništiti povezane stavke sa njima, da li ste sigurni?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "Ova opcija će vratiti (ukoliko ne postoje) sve podrazumevane statuse, prilagođena polja i rasporede. Obriši i vrati će obrisati podrazumevane rasporede i potom ih ponovo kreirati."

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Četvrtak"

#: frontend/src/components/Filter.vue:356
msgid "Timespan"
msgstr "Vremenski opseg"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:41
msgid "Title"
msgstr "Naslov"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:32
msgid "Title Field"
msgstr "Polje za naslov"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:63
msgid "To"
msgstr "Za"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Datum završetka"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "Do vrste"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "Za korisnika"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "Da biste saznali više o podešavanju imejl naloga, kliknite"

#: frontend/src/components/Filter.vue:632
msgid "Today"
msgstr "Danas"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "Za uraditi"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "Prebaci za prikaz"

#: frontend/src/components/Filter.vue:636
msgid "Tomorrow"
msgstr "Sutra"

#: frontend/src/components/Modals/NoteModal.vue:37
#: frontend/src/components/Modals/TaskModal.vue:59
msgid "Took a call with John Doe and discussed the new project."
msgstr "Obavljen poziv sa Petrom Petrovićem i razgovarano o novom projektu."

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "Ukupno"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Ukupno praznika"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "Ukupno nakon popusta"

#: crm/api/dashboard.py:123
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "Ukupno potencijalnih klijenata"

#: crm/api/dashboard.py:124
msgid "Total number of leads"
msgstr "Ukupan broj potencijalnih klijenata"

#: crm/api/dashboard.py:182
msgid "Total number of non won/lost deals"
msgstr "Ukupan broj poslovnih prika koje nisu dobijene niti izgubljene"

#: crm/api/dashboard.py:297
msgid "Total number of won deals based on its closure date"
msgstr "Ukupan broj dobijenih poslovnih prilika prema datumu zatvaranja"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Utorak"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:596
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Greška prilikom kreiranja Twilio API kredencijala."

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio broj"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio nije omogućen"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio podešavanja su uspešno ažurirana"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Type"
msgstr "Vrsta"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:73
#: frontend/src/components/Controls/MultiSelectUserInput.vue:73
msgid "Type an email address to invite"
msgstr "Unesite imejl adresu za pozivanje"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "Ovde unesite Vašu poruku..."

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "Neovlašćeni zahtev"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Uncollapsible"
msgstr "Sažimanje nije moguće"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:130
msgid "Unknown"
msgstr "Nepoznat"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "Poništi povezivanje"

#: frontend/src/components/DeleteLinkedDocModal.vue:77
msgid "Unlink all"
msgstr "Poništi sva povezivanja"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "Poništi povezivanje i obriši"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "Poništi povezivanje i obriši {0} stavki"

#: frontend/src/components/DeleteLinkedDocModal.vue:242
msgid "Unlink linked item"
msgstr "Poništi povezivanje povezane stavke"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink {0} item(s)"
msgstr "Poništi povezivanje {0} stavki"

#: frontend/src/components/ViewControls.vue:1138
msgid "Unpin View"
msgstr "Ukloni fiksiranje prikaza"

#: frontend/src/components/ViewControls.vue:975
msgid "Unsaved Changes"
msgstr "Nesačuvane promene"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "Bez naslova"

#: frontend/src/components/ColumnSettings.vue:138
#: frontend/src/components/Modals/AssignmentModal.vue:17
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:17
#: frontend/src/components/Settings/General/BrandSettings.vue:23
#: frontend/src/components/Settings/General/CurrencySettings.vue:23
#: frontend/src/components/Settings/General/HomeActions.vue:17
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:219
#: frontend/src/components/ViewControls.vue:980
msgid "Update"
msgstr "Ažuriraj"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "Ažuriraj nalog"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "Ažuriraj {0} zapisa"

#: frontend/src/components/FilesUploader/FilesUploader.vue:86
msgid "Upload"
msgstr "Otpremi"

#: frontend/src/components/Activities/Activities.vue:404
#: frontend/src/components/Activities/ActivityHeader.vue:62
#: frontend/src/components/Activities/ActivityHeader.vue:158
msgid "Upload Attachment"
msgstr "Otpremi prilog"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "Otpremi dokument"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "Otpremi sliku"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "Otpremi video"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:42 frontend/src/pages/Lead.vue:96
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:42
msgid "Upload image"
msgstr "Otpremi sliku"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Korisnik"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Korisničko ime"

#: frontend/src/components/Settings/Users.vue:301
msgid "User {0} has been removed"
msgstr "Korisnik {0} je uklonjen"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:95
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Korisnici"

#: frontend/src/components/Modals/AddExistingUserModal.vue:103
msgid "Users added successfully"
msgstr "Korisnici su uspešno dodati"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Važenje"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Vrednost"

#: frontend/src/components/Modals/ViewModal.vue:25
msgid "View Name"
msgstr "Naziv prikaza"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Views"
msgstr "Prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:558
msgid "Web form"
msgstr "Veb-obrazac"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Token za verifikaciju webhook-a"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Veb-sajt"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Sreda"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Nedeljni odmor"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "Poruka dobrodošlice"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "Dobro došli {0}, hajde da dodamo tvog prvog potencijalnog klijenta"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:598
#: frontend/src/components/Settings/Settings.vue:129
#: frontend/src/pages/Deal.vue:580 frontend/src/pages/Lead.vue:446
#: frontend/src/pages/MobileDeal.vue:473 frontend/src/pages/MobileLead.vue:380
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp šabloni"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "Where"
msgstr "Gde"

#: frontend/src/components/ColumnSettings.vue:117
msgid "Width"
msgstr "Širina"

#: frontend/src/components/ColumnSettings.vue:122
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "Širina može biti broj, pixel ili rem (npr. 3, 30px, 10rem)"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "Dobijeno"

#: crm/api/dashboard.py:296
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "Dobijene poslovne prilike"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Radni dan"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Radno vreme"

#: frontend/src/components/Filter.vue:628
msgid "Yesterday"
msgstr "Juče"

#: crm/api/whatsapp.py:36 crm/api/whatsapp.py:216 crm/api/whatsapp.py:230
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Vi"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "Nemate dozvolu da pristupite ovom resursu."

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "Možete promeniti podrazumevano sredstvo za pozivanje u podešavanjima"

#: frontend/src/components/Settings/General/CurrencySettings.vue:107
msgid "You can get your access key from "
msgstr "Svoj ključ za pristup možete dobiti sa "

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "Nemate podešen Exotel broj u svom agentu telefonije"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "Nemate podešen broj mobilnog telefona u svom agentu telefonije"

#: frontend/src/data/document.js:32
msgid "You do not have permission to access this document"
msgstr "Nemate dozvolu za pristup ovom dokumentu"

#: frontend/src/components/ViewControls.vue:976
msgid "You have unsaved changes. Do you want to save them?"
msgstr "Imate nesačuvane promene. Da li želite da ih sačuvate?"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "Morate biti u razvojnom režimu da biste uredili standardnu skriptu obrasca"

#: crm/api/todo.py:111
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "Vaše zaduženje na zadatku {0} je uklonjeno od strane {1}"

#: crm/api/todo.py:46 crm/api/todo.py:89
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Vaša dodela za {0} {1} je uklonjena od strane {2}"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "dodato je"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "amber"

#: crm/api/todo.py:120
msgid "assigned a new task {0} to you"
msgstr "dodeljen Vam je novi zadatak {0}"

#: crm/api/todo.py:100
msgid "assigned a {0} {1} to you"
msgstr "dodeljen Vam je {0} {1}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "crna"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "plavo"

#: frontend/src/components/Activities/Activities.vue:232
msgid "changes from"
msgstr "izmene iz"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "komentar"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "cijan"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:268
#: frontend/src/components/Controls/MultiSelectUserInput.vue:242
msgid "email already exists"
msgstr "imejl već postoji"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:113
msgid "exchangerate.host"
msgstr "exchangerate.host"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "frankfurter.app"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "siva"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "zelena"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "group_by"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "je obavio poziv"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "je stupio u kontakt"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "ovde"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "za 1 čas"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "za 1 minut"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "za 1 godinu"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "za {0} M"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "za {0} d"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "za {0} dana"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "za {0} h"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "za {0} časova"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "za {0} m"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "za {0} minuta"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "za {0} meseci"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "za {0} w"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "za {0} nedelja"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "za {0} y"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "za {0} godina"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
#: frontend/src/components/Settings/InviteUserPage.vue:37
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "upravo sada"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "kanban"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "oznaka"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "lista"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:65
#: frontend/src/pages/MobileNotification.vue:52
msgid "mentioned you in {0}"
msgstr "Vas je pomenuo u {0}"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "next"
msgstr "sledeći"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "sada"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "narandžasta"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "roze"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "previous"
msgstr "prethodno"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "private"
msgstr "privatno"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "public"
msgstr "javno"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "purpurna"

#: crm/api/whatsapp.py:37
msgid "received a whatsapp message in {0}"
msgstr "primljena WhatsApp poruka u {0}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "crvena"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "tirkizna"

#: frontend/src/components/Activities/Activities.vue:274
#: frontend/src/components/Activities/Activities.vue:337
msgid "to"
msgstr "ka"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "sutra"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "ljubičasta"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "žuta"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "juče"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} M"

#: crm/api/todo.py:50
msgid "{0} assigned a {1} {2} to you"
msgstr "Dodeljeno Vam je {1} {2} od strane {0}"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} d"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "pre {0} dana"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0} h"

#: frontend/src/components/Settings/Users.vue:291
msgid "{0} has been granted {1} access"
msgstr "{0} je dodeljen pristup sa ulogom {1}"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "pre {0} časova"

#: frontend/src/components/EmailEditor.vue:29
#: frontend/src/components/EmailEditor.vue:64
#: frontend/src/components/EmailEditor.vue:77
#: frontend/src/components/Modals/AddExistingUserModal.vue:36
#: frontend/src/components/Settings/InviteUserPage.vue:41
msgid "{0} is an invalid email address"
msgstr "{0} je nevažeća imejl adresa"

#: frontend/src/components/Modals/ConvertToDealModal.vue:181
msgid "{0} is required"
msgstr "{0} je obavezan"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} m"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "pre {0} minuta"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "pre {0} meseci"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} w"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "pre {0} nedelja"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0} y"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "pre {0} godina"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "Izbegavajte korišćenje \"trigger\" kao naziva polja - u sukobu je sa ugrađenom trigger() metodom."

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "Metodа \"{0}\" nije pronađenа u klasi."

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "Nijedna klasa nije pronađena za DocType: {0}, obavezno mora postojati klasa za matični DocType. Može biti prazna, ali mora postojati."

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "Nema podataka za matično polje: {0}"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "Nijedan red nije pronađen za idx: {0} u matičnom polju: {1}"

