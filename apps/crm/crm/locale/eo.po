msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-08-03 09:38+0000\n"
"PO-Revision-Date: 2025-08-04 08:35\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Esperanto\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: eo\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: eo_UY\n"

#: frontend/src/components/ViewControls.vue:1217
msgid " (New)"
msgstr "crwdns152667:0crwdne152667:0"

#: frontend/src/components/Modals/TaskModal.vue:99
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "crwdns152669:0crwdne152669:0"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "crwdns156086:0crwdne156086:0"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "crwdns156088:0crwdne156088:0"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "crwdns156090:0crwdne156090:0"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "crwdns156092:0crwdne156092:0"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "crwdns156094:0crwdne156094:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "crwdns152671:0crwdne152671:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "crwdns152673:0crwdne152673:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "crwdns152675:0crwdne152675:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "crwdns152677:0crwdne152677:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "crwdns152679:0crwdne152679:0"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "crwdns152681:0crwdne152681:0"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "crwdns152683:0crwdne152683:0"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "crwdns152685:0crwdne152685:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:98
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:98
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "crwdns152687:0{{ lead_name }}crwdnd152687:0{{ grand_total }}crwdne152687:0"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "crwdns152689:0crwdne152689:0"

#: frontend/src/components/CommunicationArea.vue:85
msgid "@John, can you please check this?"
msgstr "crwdns152691:0crwdne152691:0"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:56
msgid "A Lead requires either a person's name or an organization's name"
msgstr "crwdns152693:0crwdne152693:0"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Key"
msgstr "crwdns152695:0crwdne152695:0"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "crwdns156096:0crwdne156096:0"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Secret"
msgstr "crwdns152697:0crwdne152697:0"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "crwdns152699:0crwdne152699:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:92
msgid "Accept"
msgstr "crwdns152701:0crwdne152701:0"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "crwdns152703:0crwdne152703:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "crwdns152705:0crwdne152705:0"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "crwdns152707:0crwdne152707:0"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "crwdns157240:0crwdne157240:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "crwdns157242:0{0}crwdne157242:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:97
msgid "Access key"
msgstr "crwdns157244:0crwdne157244:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:101
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "crwdns157246:0crwdne157246:0"

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "crwdns156098:0crwdne156098:0"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "crwdns152709:0crwdne152709:0"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "crwdns156100:0crwdne156100:0"

#: frontend/src/components/CustomActions.vue:73
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1109
msgid "Actions"
msgstr "crwdns152711:0crwdne152711:0"

#: frontend/src/pages/Deal.vue:540 frontend/src/pages/Lead.vue:406
#: frontend/src/pages/MobileDeal.vue:432 frontend/src/pages/MobileLead.vue:339
msgid "Activity"
msgstr "crwdns152713:0crwdne152713:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:53
msgid "Add"
msgstr "crwdns156102:0crwdne156102:0"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "crwdns156104:0crwdne156104:0"

#: frontend/src/components/ColumnSettings.vue:69
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "crwdns152715:0crwdne152715:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "crwdns156106:0crwdne156106:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:58
#: frontend/src/components/FieldLayoutEditor.vue:173
#: frontend/src/components/Kanban/KanbanSettings.vue:84
#: frontend/src/components/SidePanelLayoutEditor.vue:98
msgid "Add Field"
msgstr "crwdns152717:0crwdne152717:0"

#: frontend/src/components/Filter.vue:138
msgid "Add Filter"
msgstr "crwdns152719:0crwdne152719:0"

#: frontend/src/components/Controls/Grid.vue:321
msgid "Add Row"
msgstr "crwdns152721:0crwdne152721:0"

#: frontend/src/components/FieldLayoutEditor.vue:200
#: frontend/src/components/SidePanelLayoutEditor.vue:130
msgid "Add Section"
msgstr "crwdns152723:0crwdne152723:0"

#: frontend/src/components/SortBy.vue:148
msgid "Add Sort"
msgstr "crwdns152725:0crwdne152725:0"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "crwdns152727:0crwdne152727:0"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "crwdns152729:0crwdne152729:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "crwdns156108:0crwdne156108:0"

#: frontend/src/components/FieldLayoutEditor.vue:426
msgid "Add column"
msgstr "crwdns152731:0crwdne152731:0"

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "crwdns152733:0crwdne152733:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "crwdns156110:0crwdne156110:0"

#: frontend/src/components/ViewControls.vue:104
msgid "Add filter"
msgstr "crwdns152735:0crwdne152735:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "crwdns152737:0crwdne152737:0"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "crwdns156112:0crwdne156112:0"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "crwdns152739:0crwdne152739:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "crwdns152741:0crwdne152741:0"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "crwdns152743:0crwdne152743:0"

#: frontend/src/components/Layouts/AppSidebar.vue:434
msgid "Add your first comment"
msgstr "crwdns156114:0crwdne156114:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "crwdns156116:0crwdne156116:0"

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "crwdns152745:0crwdne152745:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:172
#: frontend/src/components/Settings/InviteUserPage.vue:179
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:184
#: frontend/src/components/Settings/Users.vue:244
#: frontend/src/components/Settings/Users.vue:247
msgid "Admin"
msgstr "crwdns156118:0crwdne156118:0"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "crwdns152747:0crwdne152747:0"

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "crwdns152749:0crwdne152749:0"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:512 frontend/src/pages/MobileContact.vue:510
#: frontend/src/pages/MobileOrganization.vue:454
#: frontend/src/pages/Organization.vue:463
msgid "Amount"
msgstr "crwdns152751:0crwdne152751:0"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "crwdns156120:0crwdne156120:0"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "crwdns156122:0crwdne156122:0"

#: frontend/src/data/document.js:63
msgid "An error occurred while updating the document"
msgstr "crwdns157248:0crwdne157248:0"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "crwdns152753:0crwdne152753:0"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "crwdns152755:0crwdne152755:0"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "And"
msgstr "crwdns152757:0crwdne152757:0"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "crwdns152759:0crwdne152759:0"

#: frontend/src/components/Modals/DealModal.vue:201
#: frontend/src/components/Modals/LeadModal.vue:142
msgid "Annual Revenue should be a number"
msgstr "crwdns152761:0crwdne152761:0"

#: frontend/src/components/Settings/General/BrandSettings.vue:69
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "crwdns152763:0crwdne152763:0"

#: frontend/src/components/Settings/General/BrandSettings.vue:103
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "crwdns152765:0crwdne152765:0"

#: frontend/src/components/Kanban/KanbanSettings.vue:107
#: frontend/src/components/Kanban/KanbanView.vue:45
msgid "Apply"
msgstr "crwdns152767:0crwdne152767:0"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "crwdns152769:0crwdne152769:0"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "crwdns152771:0crwdne152771:0"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "crwdns152773:0crwdne152773:0"

#: frontend/src/components/Activities/AttachmentArea.vue:139
msgid "Are you sure you want to delete this attachment?"
msgstr "crwdns152775:0crwdne152775:0"

#: frontend/src/pages/MobileContact.vue:263
msgid "Are you sure you want to delete this contact?"
msgstr "crwdns152777:0crwdne152777:0"

#: frontend/src/pages/MobileOrganization.vue:264
msgid "Are you sure you want to delete this organization?"
msgstr "crwdns152779:0crwdne152779:0"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "crwdns152781:0crwdne152781:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "crwdns156124:0{0}crwdne156124:0"

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "crwdns152783:0crwdne152783:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "crwdns152785:0crwdne152785:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:174
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "crwdns156126:0{0}crwdne156126:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "crwdns156128:0{0}crwdne156128:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "crwdns157250:0{0}crwdnd157250:0{1}crwdne157250:0"

#: frontend/src/components/ListBulkActions.vue:184
#: frontend/src/components/Modals/AssignmentModal.vue:5
msgid "Assign To"
msgstr "crwdns152787:0crwdne152787:0"

#: frontend/src/components/AssignTo.vue:9
msgid "Assign to"
msgstr "crwdns152789:0crwdne152789:0"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "crwdns152791:0crwdne152791:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "crwdns152793:0crwdne152793:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "crwdns152795:0crwdne152795:0"

#: frontend/src/components/ListBulkActions.vue:152
msgid "Assignment cleared successfully"
msgstr "crwdns152797:0crwdne152797:0"

#: frontend/src/components/Layouts/AppSidebar.vue:577
msgid "Assignment rule"
msgstr "crwdns156130:0crwdne156130:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:176
msgid "At least one field is required"
msgstr "crwdns152799:0crwdne152799:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:76
msgid "Attach"
msgstr "crwdns152801:0crwdne152801:0"

#: frontend/src/pages/Deal.vue:117 frontend/src/pages/Lead.vue:174
msgid "Attach a file"
msgstr "crwdns152803:0crwdne152803:0"

#: frontend/src/pages/Deal.vue:575 frontend/src/pages/Lead.vue:441
#: frontend/src/pages/MobileDeal.vue:468 frontend/src/pages/MobileLead.vue:375
msgid "Attachments"
msgstr "crwdns152805:0crwdne152805:0"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "crwdns152807:0crwdne152807:0"

#: crm/api/dashboard.py:238
msgid "Average deal value of non won/lost deals"
msgstr "crwdns156132:0crwdne156132:0"

#: crm/api/dashboard.py:411
msgid "Average deal value of ongoing & won deals"
msgstr "crwdns156134:0crwdne156134:0"

#: crm/api/dashboard.py:354
msgid "Average deal value of won deals"
msgstr "crwdns156136:0crwdne156136:0"

#: crm/api/dashboard.py:518
msgid "Average time taken from deal creation to deal closure"
msgstr "crwdns156138:0crwdne156138:0"

#: crm/api/dashboard.py:464
msgid "Average time taken from lead creation to deal closure"
msgstr "crwdns156140:0crwdne156140:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "crwdns156142:0crwdne156142:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "crwdns156144:0crwdne156144:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "crwdns156146:0crwdne156146:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "crwdns156148:0crwdne156148:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "crwdns156150:0crwdne156150:0"

#: crm/api/dashboard.py:410
msgid "Avg. deal value"
msgstr "crwdns156152:0crwdne156152:0"

#: crm/api/dashboard.py:237
msgid "Avg. ongoing deal value"
msgstr "crwdns156154:0crwdne156154:0"

#: crm/api/dashboard.py:517
msgid "Avg. time to close a deal"
msgstr "crwdns156156:0crwdne156156:0"

#: crm/api/dashboard.py:463
msgid "Avg. time to close a lead"
msgstr "crwdns156158:0crwdne156158:0"

#: crm/api/dashboard.py:353
msgid "Avg. won deal value"
msgstr "crwdns156160:0crwdne156160:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "crwdns156162:0crwdne156162:0"

#: frontend/src/components/Activities/EmailArea.vue:72
#: frontend/src/components/EmailEditor.vue:44
#: frontend/src/components/EmailEditor.vue:69
msgid "BCC"
msgstr "crwdns152809:0crwdne152809:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "crwdns152811:0crwdne152811:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "crwdns152813:0crwdne152813:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "crwdns152815:0crwdne152815:0"

#: frontend/src/components/Filter.vue:355
msgid "Between"
msgstr "crwdns152817:0crwdne152817:0"

#: frontend/src/components/Settings/General/BrandSettings.vue:40
msgid "Brand name"
msgstr "crwdns156164:0crwdne156164:0"

#: frontend/src/components/Settings/General/BrandSettings.vue:9
msgid "Brand settings"
msgstr "crwdns156166:0crwdne156166:0"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "crwdns152821:0crwdne152821:0"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "crwdns152823:0crwdne152823:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "crwdns152825:0crwdne152825:0"

#: frontend/src/components/Activities/EmailArea.vue:67
#: frontend/src/components/EmailEditor.vue:34
#: frontend/src/components/EmailEditor.vue:56
msgid "CC"
msgstr "crwdns152827:0crwdne152827:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "crwdns152829:0crwdne152829:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "crwdns152831:0crwdne152831:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "crwdns152833:0crwdne152833:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:318
msgid "CRM Dashboard"
msgstr "crwdns156168:0crwdne156168:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "crwdns152835:0crwdne152835:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "crwdns152837:0crwdne152837:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "crwdns152839:0crwdne152839:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "crwdns152841:0crwdne152841:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "crwdns152843:0crwdne152843:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "crwdns152845:0crwdne152845:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "crwdns152847:0crwdne152847:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "crwdns152849:0crwdne152849:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "crwdns152851:0crwdne152851:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "crwdns152853:0crwdne152853:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "crwdns152855:0crwdne152855:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "crwdns152857:0crwdne152857:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "crwdns152859:0crwdne152859:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "crwdns152861:0crwdne152861:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "crwdns156170:0crwdne156170:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "crwdns152863:0crwdne152863:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "crwdns152865:0crwdne152865:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "crwdns152867:0crwdne152867:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "crwdns156172:0crwdne156172:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "crwdns156174:0crwdne156174:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "crwdns152869:0crwdne152869:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "crwdns152871:0crwdne152871:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "crwdns152873:0crwdne152873:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "crwdns152875:0crwdne152875:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "crwdns152877:0crwdne152877:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "crwdns152879:0crwdne152879:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "crwdns152881:0crwdne152881:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "crwdns152883:0crwdne152883:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "crwdns152885:0crwdne152885:0"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "crwdns152887:0crwdne152887:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:42
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "crwdns156176:0crwdne156176:0"

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "crwdns152889:0crwdne152889:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "crwdns152891:0crwdne152891:0"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "crwdns152893:0crwdne152893:0"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "crwdns152895:0crwdne152895:0"

#: frontend/src/components/Layouts/AppSidebar.vue:551
msgid "Call log"
msgstr "crwdns156178:0crwdne156178:0"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "crwdns152897:0{0}crwdne152897:0"

#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:43
msgid "Call with John Doe"
msgstr "crwdns152899:0crwdne152899:0"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "crwdns152901:0crwdne152901:0"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "crwdns152903:0crwdne152903:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Calling..."
msgstr "crwdns152905:0crwdne152905:0"

#: frontend/src/pages/Deal.vue:560 frontend/src/pages/Lead.vue:426
#: frontend/src/pages/MobileDeal.vue:452 frontend/src/pages/MobileLead.vue:359
msgid "Calls"
msgstr "crwdns152907:0crwdne152907:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "crwdns152909:0crwdne152909:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/ColumnSettings.vue:132
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:114
#: frontend/src/components/Modals/AssignmentModal.vue:9
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:77
#: frontend/src/components/ViewControls.vue:56
#: frontend/src/components/ViewControls.vue:156
#: frontend/src/pages/Dashboard.vue:41
msgid "Cancel"
msgstr "crwdns152911:0crwdne152911:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "crwdns152913:0crwdne152913:0"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "crwdns156180:0crwdne156180:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "crwdns152915:0{0}crwdne152915:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:94
msgid "Capture"
msgstr "crwdns152917:0crwdne152917:0"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Capturing leads"
msgstr "crwdns156182:0crwdne156182:0"

#: frontend/src/components/Layouts/AppSidebar.vue:485
msgid "Change"
msgstr "crwdns156184:0crwdne156184:0"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "crwdns156186:0crwdne156186:0"

#: frontend/src/components/Activities/TaskArea.vue:44
msgid "Change Status"
msgstr "crwdns152919:0crwdne152919:0"

#: frontend/src/components/Layouts/AppSidebar.vue:476
#: frontend/src/components/Layouts/AppSidebar.vue:484
msgid "Change deal status"
msgstr "crwdns156188:0crwdne156188:0"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:41 frontend/src/pages/Lead.vue:95
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:41
msgid "Change image"
msgstr "crwdns152921:0crwdne152921:0"

#: frontend/src/pages/Dashboard.vue:28
msgid "Chart"
msgstr "crwdns156190:0crwdne156190:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "crwdns156192:0crwdne156192:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:43
#: frontend/src/components/Modals/ConvertToDealModal.vue:69
#: frontend/src/pages/MobileLead.vue:124 frontend/src/pages/MobileLead.vue:151
msgid "Choose Existing"
msgstr "crwdns152923:0crwdne152923:0"

#: frontend/src/components/Modals/DealModal.vue:45
msgid "Choose Existing Contact"
msgstr "crwdns152925:0crwdne152925:0"

#: frontend/src/components/Modals/DealModal.vue:38
msgid "Choose Existing Organization"
msgstr "crwdns152927:0crwdne152927:0"

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "crwdns156194:0crwdne156194:0"

#: frontend/src/components/Controls/Link.vue:62
msgid "Clear"
msgstr "crwdns152929:0crwdne152929:0"

#: frontend/src/components/ListBulkActions.vue:134
#: frontend/src/components/ListBulkActions.vue:142
#: frontend/src/components/ListBulkActions.vue:188
msgid "Clear Assignment"
msgstr "crwdns152931:0crwdne152931:0"

#: frontend/src/components/SortBy.vue:160
msgid "Clear Sort"
msgstr "crwdns152933:0crwdne152933:0"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "crwdns152935:0crwdne152935:0"

#: frontend/src/components/Filter.vue:18 frontend/src/components/Filter.vue:150
msgid "Clear all Filter"
msgstr "crwdns152937:0crwdne152937:0"

#: frontend/src/components/Notifications.vue:28
msgid "Close"
msgstr "crwdns152939:0crwdne152939:0"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "crwdns156198:0crwdne156198:0"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "crwdns152943:0crwdne152943:0"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Collapsible"
msgstr "crwdns152945:0crwdne152945:0"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "crwdns152947:0crwdne152947:0"

#: frontend/src/components/FieldLayoutEditor.vue:423
msgid "Column"
msgstr "crwdns152949:0crwdne152949:0"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:15
msgid "Column Field"
msgstr "crwdns152951:0crwdne152951:0"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "crwdns152953:0crwdne152953:0"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:80
#: frontend/src/components/CommunicationArea.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:574
msgid "Comment"
msgstr "crwdns152955:0crwdne152955:0"

#: frontend/src/pages/Deal.vue:550 frontend/src/pages/Lead.vue:416
#: frontend/src/pages/MobileDeal.vue:442 frontend/src/pages/MobileLead.vue:349
msgid "Comments"
msgstr "crwdns152957:0crwdne152957:0"

#: crm/api/dashboard.py:884
msgid "Common reasons for losing deals"
msgstr "crwdns156200:0crwdne156200:0"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "crwdns152959:0crwdne152959:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "crwdns152961:0crwdne152961:0"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "crwdns152963:0crwdne152963:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "crwdns152965:0crwdne152965:0"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "crwdns152967:0crwdne152967:0"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "crwdns152969:0crwdne152969:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:8
msgid "Configure general settings for your CRM"
msgstr "crwdns156202:0crwdne156202:0"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "crwdns156204:0crwdne156204:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:70
msgid "Configure the exchange rate provider for your CRM"
msgstr "crwdns157252:0crwdne157252:0"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "crwdns152971:0crwdne152971:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:250
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:253
msgid "Confirm Delete"
msgstr "crwdns156206:0crwdne156206:0"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "crwdns156208:0crwdne156208:0"

#: frontend/src/components/Settings/Users.vue:225
#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "crwdns156210:0crwdne156210:0"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "crwdns156212:0crwdne156212:0"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:547
#: frontend/src/components/Modals/ConvertToDealModal.vue:65
#: frontend/src/pages/MobileLead.vue:147
msgid "Contact"
msgstr "crwdns152973:0crwdne152973:0"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:212
msgid "Contact Already Exists"
msgstr "crwdns152975:0crwdne152975:0"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "crwdns156214:0crwdne156214:0"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "crwdns152977:0crwdne152977:0"

#: frontend/src/pages/Deal.vue:655 frontend/src/pages/MobileDeal.vue:546
msgid "Contact added"
msgstr "crwdns152979:0crwdne152979:0"

#: frontend/src/pages/Deal.vue:645 frontend/src/pages/MobileDeal.vue:536
msgid "Contact already added"
msgstr "crwdns152981:0crwdne152981:0"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:211
msgid "Contact already exists with {0}"
msgstr "crwdns152983:0{0}crwdne152983:0"

#: frontend/src/pages/Contact.vue:282 frontend/src/pages/MobileContact.vue:255
msgid "Contact image updated"
msgstr "crwdns156216:0crwdne156216:0"

#: frontend/src/pages/Deal.vue:666 frontend/src/pages/MobileDeal.vue:557
msgid "Contact removed"
msgstr "crwdns152987:0crwdne152987:0"

#: frontend/src/pages/Contact.vue:437 frontend/src/pages/Contact.vue:450
#: frontend/src/pages/Contact.vue:463 frontend/src/pages/Contact.vue:473
#: frontend/src/pages/MobileContact.vue:435
#: frontend/src/pages/MobileContact.vue:448
#: frontend/src/pages/MobileContact.vue:461
#: frontend/src/pages/MobileContact.vue:471
msgid "Contact updated"
msgstr "crwdns156218:0crwdne156218:0"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:237 frontend/src/pages/MobileContact.vue:215
#: frontend/src/pages/MobileOrganization.vue:334
msgid "Contacts"
msgstr "crwdns152989:0crwdne152989:0"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:34
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:105
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:105
msgid "Content"
msgstr "crwdns152991:0crwdne152991:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Content Type"
msgstr "crwdns152993:0crwdne152993:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:167
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:165
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:169
msgid "Content is required"
msgstr "crwdns156220:0crwdne156220:0"

#: frontend/src/components/Layouts/AppSidebar.vue:375
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:8
#: frontend/src/pages/MobileLead.vue:56 frontend/src/pages/MobileLead.vue:110
msgid "Convert"
msgstr "crwdns152995:0crwdne152995:0"

#: frontend/src/components/Layouts/AppSidebar.vue:366
#: frontend/src/components/Layouts/AppSidebar.vue:374
msgid "Convert lead to deal"
msgstr "crwdns156222:0crwdne156222:0"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:195
#: frontend/src/components/Modals/ConvertToDealModal.vue:19
#: frontend/src/pages/Lead.vue:45 frontend/src/pages/MobileLead.vue:106
msgid "Convert to Deal"
msgstr "crwdns152997:0crwdne152997:0"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "crwdns152999:0crwdne152999:0"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "crwdns153001:0crwdne153001:0"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "crwdns156224:0crwdne156224:0"

#: crm/api/dashboard.py:607 crm/api/dashboard.py:736 crm/api/dashboard.py:794
#: crm/api/dashboard.py:891
msgid "Count"
msgstr "crwdns156226:0crwdne156226:0"

#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/ContactModal.vue:41
#: frontend/src/components/Modals/CreateDocumentModal.vue:93
#: frontend/src/components/Modals/DealModal.vue:67
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/LeadModal.vue:38
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/OrganizationModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Modals/ViewModal.vue:16
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/pages/CallLogs.vue:11 frontend/src/pages/Contacts.vue:13
#: frontend/src/pages/Contacts.vue:60 frontend/src/pages/Deals.vue:13
#: frontend/src/pages/Deals.vue:236 frontend/src/pages/Leads.vue:13
#: frontend/src/pages/Leads.vue:262 frontend/src/pages/Notes.vue:7
#: frontend/src/pages/Notes.vue:93 frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:11
#: frontend/src/pages/Tasks.vue:185
msgid "Create"
msgstr "crwdns153003:0crwdne153003:0"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "crwdns153005:0crwdne153005:0"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "crwdns153009:0crwdne153009:0"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/SidePanelLayout.vue:137
msgid "Create New"
msgstr "crwdns153011:0crwdne153011:0"

#: frontend/src/components/Activities/Activities.vue:384
#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Create Note"
msgstr "crwdns153013:0crwdne153013:0"

#: frontend/src/components/Activities/Activities.vue:399
#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Create Task"
msgstr "crwdns153015:0crwdne153015:0"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "crwdns153017:0crwdne153017:0"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "crwdns153019:0crwdne153019:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:152
msgid "Create lead"
msgstr "crwdns153021:0crwdne153021:0"

#: frontend/src/components/Layouts/AppSidebar.vue:344
msgid "Create your first lead"
msgstr "crwdns156230:0crwdne156230:0"

#: frontend/src/components/Layouts/AppSidebar.vue:414
msgid "Create your first note"
msgstr "crwdns156232:0crwdne156232:0"

#: frontend/src/components/Layouts/AppSidebar.vue:394
msgid "Create your first task"
msgstr "crwdns156234:0crwdne156234:0"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:38
msgid "Currency"
msgstr "crwdns153023:0crwdne153023:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:9
msgid "Currency & Exchange rate provider"
msgstr "crwdns157254:0crwdne157254:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:188
msgid "Currency set as {0} successfully"
msgstr "crwdns156236:0{0}crwdne156236:0"

#: crm/api/dashboard.py:839
msgid "Current pipeline distribution"
msgstr "crwdns156238:0crwdne156238:0"

#: frontend/src/components/Layouts/AppSidebar.vue:586
msgid "Custom actions"
msgstr "crwdns156240:0crwdne156240:0"

#: frontend/src/components/Layouts/AppSidebar.vue:536
msgid "Custom branding"
msgstr "crwdns156242:0crwdne156242:0"

#: frontend/src/components/Layouts/AppSidebar.vue:585
msgid "Custom fields"
msgstr "crwdns156244:0crwdne156244:0"

#: frontend/src/components/Layouts/AppSidebar.vue:588
msgid "Custom list actions"
msgstr "crwdns156246:0crwdne156246:0"

#: frontend/src/components/Layouts/AppSidebar.vue:587
msgid "Custom statuses"
msgstr "crwdns156248:0crwdne156248:0"

#: frontend/src/pages/Deal.vue:486
msgid "Customer created successfully"
msgstr "crwdns153025:0crwdne153025:0"

#: frontend/src/components/Layouts/AppSidebar.vue:582
msgid "Customization"
msgstr "crwdns156250:0crwdne156250:0"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "crwdns153027:0crwdne153027:0"

#: crm/api/dashboard.py:599
msgid "Daily performance of leads, deals, and wins"
msgstr "crwdns156252:0crwdne156252:0"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:575
#: frontend/src/pages/Deal.vue:555 frontend/src/pages/Lead.vue:421
#: frontend/src/pages/MobileDeal.vue:447 frontend/src/pages/MobileLead.vue:354
msgid "Data"
msgstr "crwdns153029:0crwdne153029:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "crwdns153031:0crwdne153031:0"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:601 crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "Date"
msgstr "crwdns153033:0crwdne153033:0"

#: frontend/src/components/Layouts/AppSidebar.vue:546
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:62
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:62
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:129
msgid "Deal"
msgstr "crwdns153035:0crwdne153035:0"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "crwdns153037:0crwdne153037:0"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "crwdns153039:0crwdne153039:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "crwdns153041:0crwdne153041:0"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "crwdns156254:0crwdne156254:0"

#: crm/api/dashboard.py:977
msgid "Deal generation channel analysis"
msgstr "crwdns156258:0crwdne156258:0"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:531
#: frontend/src/pages/MobileOrganization.vue:475
#: frontend/src/pages/Organization.vue:484
msgid "Deal owner"
msgstr "crwdns153043:0crwdne153043:0"

#: crm/api/dashboard.py:1030 crm/api/dashboard.py:1087
msgid "Deal value"
msgstr "crwdns156260:0crwdne156260:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:500 frontend/src/pages/MobileContact.vue:291
#: frontend/src/pages/MobileDeal.vue:386
#: frontend/src/pages/MobileOrganization.vue:328
msgid "Deals"
msgstr "crwdns153047:0crwdne153047:0"

#: crm/api/dashboard.py:788
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "crwdns156262:0crwdne156262:0"

#: crm/api/dashboard.py:1076
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "crwdns156264:0crwdne156264:0"

#: crm/api/dashboard.py:976
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "crwdns156266:0crwdne156266:0"

#: crm/api/dashboard.py:838
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "crwdns156268:0crwdne156268:0"

#: crm/api/dashboard.py:1019
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "crwdns156270:0crwdne156270:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:115
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:115
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "crwdns153049:0{{ lead_name }}crwdnd153049:0{{ grand_total }}crwdne153049:0"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "crwdns153051:0crwdne153051:0"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "crwdns156272:0crwdne156272:0"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "crwdns156274:0crwdne156274:0"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "crwdns153053:0crwdne153053:0"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "crwdns156276:0crwdne156276:0"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "crwdns153055:0crwdne153055:0"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "crwdns156278:0crwdne156278:0"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "crwdns156280:0crwdne156280:0"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "crwdns153057:0{0}crwdne153057:0"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "crwdns153059:0crwdne153059:0"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "crwdns156282:0{0}crwdne156282:0"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "crwdns153061:0crwdne153061:0"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "crwdns153063:0crwdne153063:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "crwdns153065:0crwdne153065:0"

#: frontend/src/components/Activities/AttachmentArea.vue:142
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:316
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:89
#: frontend/src/components/Kanban/KanbanView.vue:225
#: frontend/src/components/ListBulkActions.vue:177
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:235
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:238
#: frontend/src/components/ViewControls.vue:1161
#: frontend/src/components/ViewControls.vue:1172
#: frontend/src/pages/Contact.vue:103 frontend/src/pages/Deal.vue:124
#: frontend/src/pages/Lead.vue:183 frontend/src/pages/MobileContact.vue:82
#: frontend/src/pages/MobileContact.vue:266
#: frontend/src/pages/MobileDeal.vue:517
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:267
#: frontend/src/pages/Notes.vue:40 frontend/src/pages/Organization.vue:83
#: frontend/src/pages/Tasks.vue:369
msgid "Delete"
msgstr "crwdns153069:0crwdne153069:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "crwdns153071:0crwdne153071:0"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "crwdns153073:0crwdne153073:0"

#: frontend/src/components/ViewControls.vue:1157
#: frontend/src/components/ViewControls.vue:1165
msgid "Delete View"
msgstr "crwdns153075:0crwdne153075:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:65
msgid "Delete all"
msgstr "crwdns156284:0crwdne156284:0"

#: frontend/src/components/Activities/AttachmentArea.vue:62
#: frontend/src/components/Activities/AttachmentArea.vue:138
msgid "Delete attachment"
msgstr "crwdns153077:0crwdne153077:0"

#: frontend/src/pages/MobileContact.vue:262
msgid "Delete contact"
msgstr "crwdns153079:0crwdne153079:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:229
msgid "Delete linked item"
msgstr "crwdns156286:0crwdne156286:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "crwdns156288:0crwdne156288:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "crwdns156290:0crwdne156290:0"

#: frontend/src/pages/MobileOrganization.vue:263
msgid "Delete organization"
msgstr "crwdns153081:0crwdne153081:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete {0} item(s)"
msgstr "crwdns156292:0{0}crwdne156292:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "crwdns156294:0{0}crwdne156294:0"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Modals/TaskModal.vue:49
msgid "Description"
msgstr "crwdns153085:0crwdne153085:0"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "crwdns153087:0crwdne153087:0"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:286
#: frontend/src/pages/MobileDeal.vue:426 frontend/src/pages/MobileLead.vue:333
#: frontend/src/pages/MobileOrganization.vue:323
msgid "Details"
msgstr "crwdns153089:0crwdne153089:0"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "crwdns153091:0crwdne153091:0"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "crwdns153093:0crwdne153093:0"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "crwdns156296:0crwdne156296:0"

#: frontend/src/components/CommentBox.vue:76
#: frontend/src/components/EmailEditor.vue:158
msgid "Discard"
msgstr "crwdns153095:0crwdne153095:0"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "crwdns156298:0crwdne156298:0"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "crwdns156300:0crwdne156300:0"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "crwdns153097:0crwdne153097:0"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "crwdns153101:0crwdne153101:0"

#: frontend/src/data/document.js:28
msgid "Document does not exist"
msgstr "crwdns157256:0crwdne157256:0"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "crwdns153103:0crwdne153103:0"

#: frontend/src/data/document.js:42
msgid "Document updated successfully"
msgstr "crwdns156302:0crwdne156302:0"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "crwdns156304:0crwdne156304:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "crwdns153105:0crwdne153105:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "crwdns156306:0crwdne156306:0"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "crwdns153107:0crwdne153107:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "crwdns153109:0crwdne153109:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "crwdns153111:0crwdne153111:0"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "crwdns153113:0crwdne153113:0"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "crwdns153115:0crwdne153115:0"

#: frontend/src/components/Modals/ViewModal.vue:15
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:225
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:228
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/components/ViewControls.vue:1113
msgid "Duplicate"
msgstr "crwdns153117:0crwdne153117:0"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "crwdns153119:0crwdne153119:0"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "crwdns156308:0crwdne156308:0"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "crwdns153121:0crwdne153121:0"

#: frontend/src/components/Layouts/AppSidebar.vue:599
#: frontend/src/components/Settings/Settings.vue:135
msgid "ERPNext"
msgstr "crwdns153123:0crwdne153123:0"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "crwdns153125:0crwdne153125:0"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "crwdns153131:0crwdne153131:0"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "crwdns153133:0crwdne153133:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "crwdns153135:0crwdne153135:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "crwdns153137:0crwdne153137:0"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "crwdns156310:0crwdne156310:0"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "crwdns156312:0crwdne156312:0"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:319
#: frontend/src/components/FieldLayoutEditor.vue:345
#: frontend/src/components/ListBulkActions.vue:170
#: frontend/src/components/ViewControls.vue:1131
#: frontend/src/pages/Dashboard.vue:19
msgid "Edit"
msgstr "crwdns153139:0crwdne153139:0"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "Edit Call Log"
msgstr "crwdns153141:0crwdne153141:0"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "crwdns153143:0crwdne153143:0"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "crwdns156314:0crwdne156314:0"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "crwdns153145:0crwdne153145:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "crwdns153147:0crwdne153147:0"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "crwdns153149:0crwdne153149:0"

#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Edit Note"
msgstr "crwdns153151:0crwdne153151:0"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "crwdns153153:0crwdne153153:0"

#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Edit Task"
msgstr "crwdns153155:0crwdne153155:0"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "crwdns153157:0crwdne153157:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "crwdns153159:0crwdne153159:0"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "crwdns153163:0crwdne153163:0"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "crwdns153165:0{0}crwdne153165:0"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:523
#: frontend/src/pages/MobileContact.vue:521
#: frontend/src/pages/MobileOrganization.vue:465
#: frontend/src/pages/MobileOrganization.vue:493
#: frontend/src/pages/Organization.vue:474
#: frontend/src/pages/Organization.vue:502
msgid "Email"
msgstr "crwdns153167:0crwdne153167:0"

#: frontend/src/components/Settings/Settings.vue:107
msgid "Email Accounts"
msgstr "crwdns156316:0crwdne156316:0"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "crwdns156318:0crwdne156318:0"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "crwdns153169:0crwdne153169:0"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:113
msgid "Email Templates"
msgstr "crwdns153171:0crwdne153171:0"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "crwdns156320:0crwdne156320:0"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "crwdns156322:0crwdne156322:0"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "crwdns156324:0crwdne156324:0"

#: frontend/src/components/Layouts/AppSidebar.vue:573
msgid "Email communication"
msgstr "crwdns156326:0crwdne156326:0"

#: frontend/src/components/EmailEditor.vue:206
msgid "Email from Lead"
msgstr "crwdns153173:0crwdne153173:0"

#: frontend/src/components/Layouts/AppSidebar.vue:552
msgid "Email template"
msgstr "crwdns156328:0crwdne156328:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "crwdns156330:0crwdne156330:0"

#: frontend/src/pages/Deal.vue:545 frontend/src/pages/Lead.vue:411
#: frontend/src/pages/MobileDeal.vue:437 frontend/src/pages/MobileLead.vue:344
msgid "Emails"
msgstr "crwdns153175:0crwdne153175:0"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "crwdns153177:0crwdne153177:0"

#: frontend/src/components/Filter.vue:124
msgid "Empty - Choose a field to filter by"
msgstr "crwdns153179:0crwdne153179:0"

#: frontend/src/components/SortBy.vue:134
msgid "Empty - Choose a field to sort by"
msgstr "crwdns153181:0crwdne153181:0"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "crwdns153183:0crwdne153183:0"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "crwdns156332:0crwdne156332:0"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "crwdns156334:0crwdne156334:0"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "crwdns156336:0crwdne156336:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:19
msgid "Enable forecasting"
msgstr "crwdns156338:0crwdne156338:0"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:33
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:33
msgid "Enabled"
msgstr "crwdns153185:0crwdne153185:0"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "crwdns153187:0crwdne153187:0"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "End Time"
msgstr "crwdns153189:0crwdne153189:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:122
msgid "Enter access key"
msgstr "crwdns157258:0crwdne157258:0"

#: frontend/src/components/FieldLayout/Field.vue:334
msgid "Enter {0}"
msgstr "crwdns153191:0{0}crwdne153191:0"

#: frontend/src/components/Filter.vue:67 frontend/src/components/Filter.vue:100
#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:310
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:332
#: frontend/src/components/Filter.vue:348
msgid "Equals"
msgstr "crwdns153193:0crwdne153193:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:185
msgid "Error converting to deal: {0}"
msgstr "crwdns156340:0{0}crwdne156340:0"

#: frontend/src/pages/Deal.vue:739 frontend/src/pages/Lead.vue:486
#: frontend/src/pages/MobileDeal.vue:614 frontend/src/pages/MobileLead.vue:415
msgid "Error updating field"
msgstr "crwdns157260:0crwdne157260:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:261
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "crwdns153207:0crwdne153207:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:173
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "crwdns153209:0crwdne153209:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "crwdns153211:0crwdne153211:0"

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "crwdns153213:0crwdne153213:0"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "crwdns156346:0crwdne156346:0"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "crwdns157262:0crwdne157262:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:67
msgid "Exchange rate provider"
msgstr "crwdns157264:0crwdne157264:0"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:597
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "crwdns153215:0crwdne153215:0"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "crwdns153217:0crwdne153217:0"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "crwdns153219:0crwdne153219:0"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "crwdns153221:0crwdne153221:0"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "crwdns153223:0{0}crwdne153223:0"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "crwdns153225:0crwdne153225:0"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "crwdns153227:0crwdne153227:0"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "crwdns153229:0crwdne153229:0"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "crwdns156348:0crwdne156348:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:161
msgid "Expected Closure Date is required."
msgstr "crwdns156788:0crwdne156788:0"

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "crwdns156350:0crwdne156350:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:159
msgid "Expected Deal Value is required."
msgstr "crwdns156790:0crwdne156790:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "crwdns153231:0crwdne153231:0"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "crwdns153233:0crwdne153233:0"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "crwdns153235:0{0}crwdne153235:0"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "crwdns153237:0crwdne153237:0"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "crwdns153239:0crwdne153239:0"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "crwdns153241:0crwdne153241:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "crwdns153243:0crwdne153243:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:109
msgid "Failed to add users"
msgstr "crwdns156352:0crwdne156352:0"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "crwdns153245:0crwdne153245:0"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "crwdns156354:0crwdne156354:0"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:182
msgid "Failed to create template"
msgstr "crwdns156356:0crwdne156356:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:217
msgid "Failed to delete template"
msgstr "crwdns156358:0crwdne156358:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "crwdns157266:0{0}crwdnd157266:0{1}crwdnd157266:0{2}crwdne157266:0"

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "crwdns156362:0{0}crwdne156362:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:246
msgid "Failed to rename template"
msgstr "crwdns156364:0crwdne156364:0"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "crwdns153247:0crwdne153247:0"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "crwdns156366:0crwdne156366:0"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "crwdns156368:0crwdne156368:0"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "crwdns156370:0crwdne156370:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:212
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:202
msgid "Failed to update template"
msgstr "crwdns156372:0crwdne156372:0"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:81
msgid "Favicon"
msgstr "crwdns153249:0crwdne153249:0"

#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "crwdns153251:0crwdne153251:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:51
msgid "Fields Order"
msgstr "crwdns153253:0crwdne153253:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "crwdns156374:0{0}crwdne156374:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "crwdns153255:0{0}crwdnd153255:0{1}crwdne153255:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "crwdns153257:0{0}crwdnd153257:0{1}crwdnd153257:0{2}crwdne153257:0"

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "crwdns153259:0crwdne153259:0"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "crwdns153261:0crwdne153261:0"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:112
#: frontend/src/components/Filter.vue:58 frontend/src/components/Filter.vue:89
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:106
#: frontend/src/components/SortBy.vue:140
msgid "First Name"
msgstr "crwdns153263:0crwdne153263:0"

#: frontend/src/components/Modals/LeadModal.vue:135
msgid "First Name is mandatory"
msgstr "crwdns153265:0crwdne153265:0"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "crwdns153267:0crwdne153267:0"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "crwdns153269:0crwdne153269:0"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "crwdns153271:0crwdne153271:0"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "crwdns153273:0crwdne153273:0"

#: frontend/src/components/Filter.vue:131
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "crwdns153275:0crwdne153275:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
msgid "For"
msgstr "crwdns156376:0crwdne156376:0"

#: crm/api/dashboard.py:666
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "crwdns156378:0crwdne156378:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:100
msgid "Forecasting disabled successfully"
msgstr "crwdns156380:0crwdne156380:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:99
msgid "Forecasting enabled successfully"
msgstr "crwdns156382:0crwdne156382:0"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "crwdns153277:0crwdne153277:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "crwdns153279:0crwdne153279:0"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "crwdns153281:0crwdne153281:0"

#: frontend/src/components/Layouts/AppSidebar.vue:603
msgid "Frappe CRM mobile"
msgstr "crwdns156384:0crwdne156384:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "crwdns153283:0crwdne153283:0"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "crwdns153285:0crwdne153285:0"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "crwdns153287:0crwdne153287:0"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "crwdns156386:0crwdne156386:0"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "crwdns153289:0crwdne153289:0"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "crwdns153291:0crwdne153291:0"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "crwdns153293:0crwdne153293:0"

#: crm/api/dashboard.py:728
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "crwdns156388:0crwdne156388:0"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "crwdns153295:0crwdne153295:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:89
msgid "General"
msgstr "crwdns153297:0crwdne153297:0"

#: crm/api/dashboard.py:1020
msgid "Geographic distribution of deals and revenue"
msgstr "crwdns156390:0crwdne156390:0"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "crwdns156392:0crwdne156392:0"

#: frontend/src/pages/Deal.vue:104 frontend/src/pages/Lead.vue:159
msgid "Go to website"
msgstr "crwdns153299:0crwdne153299:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "crwdns153301:0crwdne153301:0"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "crwdns153303:0crwdne153303:0"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "crwdns153305:0crwdne153305:0"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "crwdns153307:0crwdne153307:0"

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "crwdns156394:0crwdne156394:0"

#: frontend/src/components/CommunicationArea.vue:62
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "crwdns153309:0crwdne153309:0"

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "crwdns153311:0crwdne153311:0"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Hide"
msgstr "crwdns153313:0crwdne153313:0"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "crwdns156396:0crwdne156396:0"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Hide Recording"
msgstr "crwdns153315:0crwdne153315:0"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Hide border"
msgstr "crwdns153317:0crwdne153317:0"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Hide label"
msgstr "crwdns153319:0crwdne153319:0"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "crwdns153321:0crwdne153321:0"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "crwdns153323:0crwdne153323:0"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "crwdns153325:0crwdne153325:0"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "crwdns153327:0crwdne153327:0"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "crwdns153329:0crwdne153329:0"

#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/General/HomeActions.vue:9
msgid "Home actions"
msgstr "crwdns153331:0crwdne153331:0"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "ID"
msgstr "crwdns153333:0crwdne153333:0"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "crwdns153335:0crwdne153335:0"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "crwdns156398:0crwdne156398:0"

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "crwdns156400:0crwdne156400:0"

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "crwdns156402:0crwdne156402:0"

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "crwdns156404:0crwdne156404:0"

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "crwdns153337:0crwdne153337:0"

#: frontend/src/components/Filter.vue:276
#: frontend/src/components/Filter.vue:297
#: frontend/src/components/Filter.vue:312
#: frontend/src/components/Filter.vue:325
#: frontend/src/components/Filter.vue:339
msgid "In"
msgstr "crwdns153339:0crwdne153339:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "crwdns153341:0crwdne153341:0"

#: frontend/src/components/SLASection.vue:75
msgid "In less than a minute"
msgstr "crwdns153343:0crwdne153343:0"

#: frontend/src/components/Activities/CallArea.vue:35
msgid "Inbound Call"
msgstr "crwdns153345:0crwdne153345:0"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "crwdns156406:0crwdne156406:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "crwdns153347:0crwdne153347:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "crwdns153349:0crwdne153349:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "crwdns153351:0crwdne153351:0"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "crwdns153353:0crwdne153353:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "crwdns153355:0crwdne153355:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "crwdns153357:0crwdne153357:0"

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Integration"
msgstr "crwdns156408:0crwdne156408:0"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "crwdns153359:0crwdne153359:0"

#: frontend/src/components/Settings/Settings.vue:120
msgctxt "FCRM"
msgid "Integrations"
msgstr "crwdns156410:0crwdne156410:0"

#: frontend/src/components/Layouts/AppSidebar.vue:524
#: frontend/src/components/Layouts/AppSidebar.vue:527
msgid "Introduction"
msgstr "crwdns156412:0crwdne156412:0"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "crwdns153363:0crwdne153363:0"

#: frontend/src/components/Modals/DealModal.vue:213
#: frontend/src/components/Modals/LeadModal.vue:154
msgid "Invalid Email"
msgstr "crwdns153365:0crwdne153365:0"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "crwdns153367:0crwdne153367:0"

#: crm/api/dashboard.py:73
msgid "Invalid chart name"
msgstr "crwdns156414:0crwdne156414:0"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "crwdns153369:0crwdne153369:0"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "crwdns156416:0crwdne156416:0"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "crwdns156418:0crwdne156418:0"

#: frontend/src/components/Settings/Settings.vue:101
msgid "Invite User"
msgstr "crwdns156420:0crwdne156420:0"

#: frontend/src/components/Settings/InviteUserPage.vue:56
msgid "Invite as"
msgstr "crwdns153373:0crwdne153373:0"

#: frontend/src/components/Settings/InviteUserPage.vue:29
msgid "Invite by email"
msgstr "crwdns153375:0crwdne153375:0"

#: frontend/src/components/Layouts/AppSidebar.vue:538
msgid "Invite users"
msgstr "crwdns156422:0crwdne156422:0"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "crwdns156424:0crwdne156424:0"

#: frontend/src/components/Layouts/AppSidebar.vue:354
msgid "Invite your team"
msgstr "crwdns156426:0crwdne156426:0"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "crwdns153377:0crwdne153377:0"

#: frontend/src/components/Filter.vue:278
#: frontend/src/components/Filter.vue:287
#: frontend/src/components/Filter.vue:299
#: frontend/src/components/Filter.vue:314
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:341
#: frontend/src/components/Filter.vue:350
msgid "Is"
msgstr "crwdns153379:0crwdne153379:0"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "crwdns153381:0crwdne153381:0"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "crwdns153383:0crwdne153383:0"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "crwdns153385:0crwdne153385:0"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "crwdns153387:0crwdne153387:0"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "crwdns153389:0crwdne153389:0"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "crwdns156428:0crwdne156428:0"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "crwdns153391:0crwdne153391:0"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "crwdns153393:0crwdne153393:0"

#: frontend/src/components/Filter.vue:75 frontend/src/components/Filter.vue:108
#: frontend/src/components/Modals/AssignmentModal.vue:35
#: frontend/src/components/Modals/TaskModal.vue:76
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "crwdns153395:0crwdne153395:0"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "crwdns153397:0crwdne153397:0"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "crwdns153399:0crwdne153399:0"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "crwdns153401:0crwdne153401:0"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:11
msgid "Kanban Settings"
msgstr "crwdns153403:0crwdne153403:0"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Key"
msgstr "crwdns153405:0crwdne153405:0"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:109
msgid "Label"
msgstr "crwdns153407:0crwdne153407:0"

#: frontend/src/components/Filter.vue:620
msgid "Last 6 Months"
msgstr "crwdns153409:0crwdne153409:0"

#: frontend/src/components/Filter.vue:612
msgid "Last Month"
msgstr "crwdns153411:0crwdne153411:0"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "crwdns153413:0crwdne153413:0"

#: frontend/src/components/Filter.vue:616
msgid "Last Quarter"
msgstr "crwdns153415:0crwdne153415:0"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "crwdns153417:0crwdne153417:0"

#: frontend/src/components/Filter.vue:608
msgid "Last Week"
msgstr "crwdns153419:0crwdne153419:0"

#: frontend/src/components/Filter.vue:624
msgid "Last Year"
msgstr "crwdns153421:0crwdne153421:0"

#: frontend/src/pages/Contact.vue:538 frontend/src/pages/MobileContact.vue:536
#: frontend/src/pages/MobileOrganization.vue:480
#: frontend/src/pages/MobileOrganization.vue:508
#: frontend/src/pages/Organization.vue:489
#: frontend/src/pages/Organization.vue:517
msgid "Last modified"
msgstr "crwdns153423:0crwdne153423:0"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "crwdns156430:0crwdne156430:0"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "crwdns153425:0crwdne153425:0"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:545
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:58
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:58
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:130
msgid "Lead"
msgstr "crwdns153427:0crwdne153427:0"

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "crwdns153429:0crwdne153429:0"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "crwdns153431:0crwdne153431:0"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "crwdns153433:0crwdne153433:0"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:73
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "crwdns153435:0crwdne153435:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "crwdns153437:0crwdne153437:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "crwdns153439:0crwdne153439:0"

#: crm/api/dashboard.py:935
msgid "Lead generation channel analysis"
msgstr "crwdns156432:0crwdne156432:0"

#: crm/api/dashboard.py:729
msgid "Lead to deal conversion pipeline"
msgstr "crwdns156434:0crwdne156434:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Lead.vue:369 frontend/src/pages/MobileLead.vue:293
msgid "Leads"
msgstr "crwdns153443:0crwdne153443:0"

#: crm/api/dashboard.py:934
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "crwdns156438:0crwdne156438:0"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "crwdns153445:0crwdne153445:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "crwdns153447:0crwdne153447:0"

#: frontend/src/components/Filter.vue:274
#: frontend/src/components/Filter.vue:285
#: frontend/src/components/Filter.vue:295
#: frontend/src/components/Filter.vue:323
#: frontend/src/components/Filter.vue:337
msgid "Like"
msgstr "crwdns153449:0crwdne153449:0"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "crwdns153451:0crwdne153451:0"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "crwdns153453:0crwdne153453:0"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "crwdns153455:0crwdne153455:0"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Listen"
msgstr "crwdns153457:0crwdne153457:0"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "crwdns153459:0crwdne153459:0"

#: frontend/src/components/Kanban/KanbanView.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "crwdns153461:0crwdne153461:0"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:37
#: frontend/src/pages/Deal.vue:193 frontend/src/pages/MobileDeal.vue:119
msgid "Loading..."
msgstr "crwdns153463:0crwdne153463:0"

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "crwdns153465:0crwdne153465:0"

#: frontend/src/components/Activities/Activities.vue:803
#: frontend/src/components/Activities/ActivityHeader.vue:137
#: frontend/src/components/Activities/ActivityHeader.vue:180
msgid "Log a Call"
msgstr "crwdns156792:0crwdne156792:0"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "crwdns153467:0crwdne153467:0"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:47
msgid "Logo"
msgstr "crwdns153469:0crwdne153469:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "crwdns156440:0crwdne156440:0"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "crwdns156442:0crwdne156442:0"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "crwdns156444:0crwdne156444:0"

#: crm/api/dashboard.py:883
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "crwdns156446:0crwdne156446:0"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "crwdns156448:0crwdne156448:0"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "crwdns156450:0crwdne156450:0"

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "crwdns156452:0crwdne156452:0"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "crwdns156454:0crwdne156454:0"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "crwdns153473:0crwdne153473:0"

#: frontend/src/pages/Contact.vue:94 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "crwdns153475:0crwdne153475:0"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Private"
msgstr "crwdns153477:0crwdne153477:0"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Public"
msgstr "crwdns153479:0crwdne153479:0"

#: frontend/src/components/Activities/Activities.vue:807
#: frontend/src/components/Activities/ActivityHeader.vue:142
#: frontend/src/components/Activities/ActivityHeader.vue:185
#: frontend/src/pages/Deals.vue:504 frontend/src/pages/Leads.vue:531
msgid "Make a Call"
msgstr "crwdns153481:0crwdne153481:0"

#: frontend/src/pages/Deal.vue:86 frontend/src/pages/Lead.vue:128
msgid "Make a call"
msgstr "crwdns153483:0crwdne153483:0"

#: frontend/src/components/Activities/AttachmentArea.vue:109
msgid "Make attachment {0}"
msgstr "crwdns153485:0{0}crwdne153485:0"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "crwdns153487:0crwdne153487:0"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "crwdns153489:0crwdne153489:0"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "crwdns153491:0crwdne153491:0"

#: frontend/src/components/Activities/AttachmentArea.vue:118
msgid "Make {0}"
msgstr "crwdns153493:0{0}crwdne153493:0"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "crwdns153495:0{0}crwdne153495:0"

#: frontend/src/components/Settings/General/GeneralSettings.vue:23
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "crwdns157268:0crwdne157268:0"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "crwdns156458:0crwdne156458:0"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "crwdns156460:0crwdne156460:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:91
#: frontend/src/components/Settings/InviteUserPage.vue:171
#: frontend/src/components/Settings/InviteUserPage.vue:178
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:185
#: frontend/src/components/Settings/Users.vue:256
#: frontend/src/components/Settings/Users.vue:259
msgid "Manager"
msgstr "crwdns156462:0crwdne156462:0"

#: frontend/src/data/document.js:54
msgid "Mandatory field error: {0}"
msgstr "crwdns156464:0{0}crwdne156464:0"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "crwdns153499:0crwdne153499:0"

#: frontend/src/components/Notifications.vue:19
#: frontend/src/pages/MobileNotification.vue:11
#: frontend/src/pages/MobileNotification.vue:14
msgid "Mark all as read"
msgstr "crwdns153501:0crwdne153501:0"

#: frontend/src/components/Layouts/AppSidebar.vue:542
msgid "Masters"
msgstr "crwdns156466:0crwdne156466:0"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "crwdns153503:0crwdne153503:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "crwdns153505:0crwdne153505:0"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "crwdns153507:0crwdne153507:0"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "crwdns153509:0crwdne153509:0"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "crwdns153511:0crwdne153511:0"

#: frontend/src/components/Modals/DealModal.vue:209
#: frontend/src/components/Modals/LeadModal.vue:150
msgid "Mobile No should be a number"
msgstr "crwdns153513:0crwdne153513:0"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "crwdns153515:0crwdne153515:0"

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "crwdns153517:0crwdne153517:0"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "crwdns153519:0crwdne153519:0"

#: frontend/src/components/Layouts/AppSidebar.vue:606
msgid "Mobile app installation"
msgstr "crwdns156468:0crwdne156468:0"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:526
#: frontend/src/pages/MobileOrganization.vue:470
#: frontend/src/pages/Organization.vue:479
msgid "Mobile no"
msgstr "crwdns153521:0crwdne153521:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "crwdns153523:0crwdne153523:0"

#: crm/api/dashboard.py:669
msgid "Month"
msgstr "crwdns156470:0crwdne156470:0"

#: frontend/src/components/FieldLayoutEditor.vue:454
msgid "Move to next section"
msgstr "crwdns153525:0crwdne153525:0"

#: frontend/src/components/FieldLayoutEditor.vue:407
msgid "Move to next tab"
msgstr "crwdns153527:0crwdne153527:0"

#: frontend/src/components/FieldLayoutEditor.vue:464
msgid "Move to previous section"
msgstr "crwdns153529:0crwdne153529:0"

#: frontend/src/components/FieldLayoutEditor.vue:393
msgid "Move to previous tab"
msgstr "crwdns153531:0crwdne153531:0"

#: frontend/src/components/Modals/ViewModal.vue:40
msgid "My Open Deals"
msgstr "crwdns153533:0crwdne153533:0"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:42
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:42
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:488
#: frontend/src/pages/Organization.vue:497
msgid "Name"
msgstr "crwdns153535:0crwdne153535:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:157
msgid "Name is required"
msgstr "crwdns156472:0crwdne156472:0"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "crwdns153537:0crwdne153537:0"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "crwdns156474:0crwdne156474:0"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "crwdns156476:0crwdne156476:0"

#: frontend/src/components/Activities/ActivityHeader.vue:82
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "crwdns153539:0crwdne153539:0"

#: frontend/src/components/Modals/AddressModal.vue:94
msgid "New Address"
msgstr "crwdns153541:0crwdne153541:0"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "New Call Log"
msgstr "crwdns153543:0crwdne153543:0"

#: frontend/src/components/Activities/Activities.vue:394
#: frontend/src/components/Activities/ActivityHeader.vue:27
#: frontend/src/components/Activities/ActivityHeader.vue:132
msgid "New Comment"
msgstr "crwdns153545:0crwdne153545:0"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "crwdns153547:0crwdne153547:0"

#: frontend/src/components/Activities/Activities.vue:389
#: frontend/src/components/Activities/ActivityHeader.vue:17
#: frontend/src/components/Activities/ActivityHeader.vue:127
msgid "New Email"
msgstr "crwdns153549:0crwdne153549:0"

#: frontend/src/components/Activities/ActivityHeader.vue:73
msgid "New Message"
msgstr "crwdns153551:0crwdne153551:0"

#: frontend/src/components/Activities/ActivityHeader.vue:42
#: frontend/src/components/Activities/ActivityHeader.vue:148
#: frontend/src/pages/Deals.vue:510 frontend/src/pages/Leads.vue:537
msgid "New Note"
msgstr "crwdns153553:0crwdne153553:0"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "crwdns153555:0crwdne153555:0"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "crwdns156478:0crwdne156478:0"

#: frontend/src/components/FieldLayoutEditor.vue:203
#: frontend/src/components/SidePanelLayoutEditor.vue:133
msgid "New Section"
msgstr "crwdns153557:0crwdne153557:0"

#: frontend/src/components/FieldLayoutEditor.vue:299
#: frontend/src/components/FieldLayoutEditor.vue:304
msgid "New Tab"
msgstr "crwdns153559:0crwdne153559:0"

#: frontend/src/components/Activities/ActivityHeader.vue:52
#: frontend/src/components/Activities/ActivityHeader.vue:153
#: frontend/src/pages/Deals.vue:515 frontend/src/pages/Leads.vue:542
msgid "New Task"
msgstr "crwdns153561:0crwdne153561:0"

#: frontend/src/components/Activities/ActivityHeader.vue:163
msgid "New WhatsApp Message"
msgstr "crwdns153563:0crwdne153563:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:81
#: frontend/src/pages/MobileLead.vue:164
msgid "New contact will be created based on the person's details"
msgstr "crwdns153565:0crwdne153565:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:56
#: frontend/src/pages/MobileLead.vue:138
msgid "New organization will be created based on the data in details section"
msgstr "crwdns153567:0crwdne153567:0"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "crwdns156480:0crwdne156480:0"

#: frontend/src/components/Modals/CreateDocumentModal.vue:89
msgid "New {0}"
msgstr "crwdns156482:0{0}crwdne156482:0"

#: frontend/src/components/Filter.vue:668
msgid "Next 6 Months"
msgstr "crwdns153569:0crwdne153569:0"

#: frontend/src/components/Filter.vue:660
msgid "Next Month"
msgstr "crwdns153571:0crwdne153571:0"

#: frontend/src/components/Filter.vue:664
msgid "Next Quarter"
msgstr "crwdns153573:0crwdne153573:0"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "crwdns153575:0crwdne153575:0"

#: frontend/src/components/Filter.vue:656
msgid "Next Week"
msgstr "crwdns153577:0crwdne153577:0"

#: frontend/src/components/Filter.vue:672
msgid "Next Year"
msgstr "crwdns153579:0crwdne153579:0"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "crwdns153581:0crwdne153581:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "crwdns153583:0crwdne153583:0"

#: frontend/src/components/Controls/Grid.vue:309
msgid "No Data"
msgstr "crwdns153585:0crwdne153585:0"

#: frontend/src/components/Kanban/KanbanView.vue:102
#: frontend/src/pages/Deals.vue:106 frontend/src/pages/Leads.vue:122
#: frontend/src/pages/Tasks.vue:68
msgid "No Title"
msgstr "crwdns153587:0crwdne153587:0"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "crwdns156484:0crwdne156484:0"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:282 frontend/src/pages/MobileDeal.vue:207
msgid "No contacts added"
msgstr "crwdns153589:0crwdne153589:0"

#: frontend/src/pages/Deal.vue:97 frontend/src/pages/Lead.vue:150
msgid "No email set"
msgstr "crwdns153591:0crwdne153591:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "crwdns156486:0crwdne156486:0"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "crwdns153593:0crwdne153593:0"

#: frontend/src/pages/Deal.vue:705
msgid "No mobile number set"
msgstr "crwdns153595:0crwdne153595:0"

#: frontend/src/components/Notifications.vue:83
#: frontend/src/pages/MobileNotification.vue:67
msgid "No new notifications"
msgstr "crwdns153597:0crwdne153597:0"

#: frontend/src/pages/Lead.vue:135
msgid "No phone number set"
msgstr "crwdns153599:0crwdne153599:0"

#: frontend/src/pages/Deal.vue:700
msgid "No primary contact set"
msgstr "crwdns153601:0crwdne153601:0"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:72
#: frontend/src/components/Controls/MultiSelectUserInput.vue:72
msgid "No results found"
msgstr "crwdns156488:0crwdne156488:0"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "crwdns153603:0crwdne153603:0"

#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "crwdns156490:0crwdne156490:0"

#: frontend/src/pages/MobileOrganization.vue:284
#: frontend/src/pages/Organization.vue:300
msgid "No website found"
msgstr "crwdns156492:0crwdne156492:0"

#: frontend/src/pages/Deal.vue:110 frontend/src/pages/Lead.vue:165
msgid "No website set"
msgstr "crwdns153605:0crwdne153605:0"

#: frontend/src/components/SidePanelLayout.vue:128
msgid "No {0} Available"
msgstr "crwdns153607:0{0}crwdne153607:0"

#: frontend/src/pages/CallLogs.vue:56 frontend/src/pages/Contact.vue:159
#: frontend/src/pages/Contacts.vue:59 frontend/src/pages/Deals.vue:235
#: frontend/src/pages/Leads.vue:261 frontend/src/pages/MobileContact.vue:150
#: frontend/src/pages/MobileOrganization.vue:142
#: frontend/src/pages/Notes.vue:92 frontend/src/pages/Organization.vue:156
#: frontend/src/pages/Organizations.vue:59 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "crwdns153609:0{0}crwdne153609:0"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "crwdns153611:0crwdne153611:0"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "crwdns153613:0crwdne153613:0"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "crwdns156494:0crwdne156494:0"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:311
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:349
msgid "Not Equals"
msgstr "crwdns153615:0crwdne153615:0"

#: frontend/src/components/Filter.vue:277
#: frontend/src/components/Filter.vue:298
#: frontend/src/components/Filter.vue:313
#: frontend/src/components/Filter.vue:326
#: frontend/src/components/Filter.vue:340
msgid "Not In"
msgstr "crwdns153617:0crwdne153617:0"

#: frontend/src/components/Filter.vue:275
#: frontend/src/components/Filter.vue:286
#: frontend/src/components/Filter.vue:296
#: frontend/src/components/Filter.vue:324
#: frontend/src/components/Filter.vue:338
msgid "Not Like"
msgstr "crwdns153619:0crwdne153619:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/General/BrandSettings.vue:16
#: frontend/src/components/Settings/General/CurrencySettings.vue:16
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "crwdns153621:0crwdne153621:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:260
msgid "Not allowed to add contact to Deal"
msgstr "crwdns153623:0crwdne153623:0"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:408
msgid "Not allowed to convert Lead to Deal"
msgstr "crwdns153625:0crwdne153625:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:271
msgid "Not allowed to remove contact from Deal"
msgstr "crwdns153627:0crwdne153627:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:282
msgid "Not allowed to set primary contact for Deal"
msgstr "crwdns153629:0crwdne153629:0"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:549
msgid "Note"
msgstr "crwdns153631:0crwdne153631:0"

#: frontend/src/pages/Deal.vue:570 frontend/src/pages/Lead.vue:436
#: frontend/src/pages/MobileDeal.vue:463 frontend/src/pages/MobileLead.vue:370
msgid "Notes"
msgstr "crwdns153633:0crwdne153633:0"

#: frontend/src/pages/Notes.vue:20
msgid "Notes View"
msgstr "crwdns153635:0crwdne153635:0"

#: frontend/src/components/Activities/EmailArea.vue:13
#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Notification"
msgstr "crwdns153637:0crwdne153637:0"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "crwdns153639:0crwdne153639:0"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "crwdns153641:0crwdne153641:0"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "crwdns153643:0crwdne153643:0"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "crwdns153645:0crwdne153645:0"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "crwdns153647:0crwdne153647:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "crwdns156498:0crwdne156498:0"

#: crm/api/dashboard.py:1027 crm/api/dashboard.py:1084
msgid "Number of deals"
msgstr "crwdns156500:0crwdne156500:0"

#: crm/api/dashboard.py:1077
msgid "Number of deals and total value per salesperson"
msgstr "crwdns156502:0crwdne156502:0"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "crwdns153649:0crwdne153649:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "crwdns156504:0crwdne156504:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "crwdns156506:0crwdne156506:0"

#: crm/api/dashboard.py:181
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "crwdns156508:0crwdne156508:0"

#: frontend/src/utils/index.js:444
msgid "Only image files are allowed"
msgstr "crwdns156510:0crwdne156510:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "crwdns153653:0{0}crwdne153653:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "crwdns156512:0crwdne156512:0"

#: frontend/src/components/Modals/NoteModal.vue:18
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Open Deal"
msgstr "crwdns153655:0crwdne153655:0"

#: frontend/src/components/Modals/NoteModal.vue:19
#: frontend/src/components/Modals/TaskModal.vue:26
msgid "Open Lead"
msgstr "crwdns153657:0crwdne153657:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "crwdns153659:0crwdne153659:0"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "crwdns153661:0crwdne153661:0"

#: frontend/src/pages/Organization.vue:92
msgid "Open website"
msgstr "crwdns153663:0crwdne153663:0"

#: frontend/src/components/Kanban/KanbanView.vue:221
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "crwdns153665:0crwdne153665:0"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "crwdns156514:0crwdne156514:0"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "crwdns153667:0crwdne153667:0"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:548
#: frontend/src/components/Modals/ConvertToDealModal.vue:39
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:505
#: frontend/src/pages/MobileLead.vue:120
#: frontend/src/pages/MobileOrganization.vue:449
#: frontend/src/pages/MobileOrganization.vue:503
#: frontend/src/pages/Organization.vue:458
#: frontend/src/pages/Organization.vue:512
msgid "Organization"
msgstr "crwdns153669:0crwdne153669:0"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "crwdns153671:0crwdne153671:0"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "crwdns153673:0crwdne153673:0"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "crwdns153675:0crwdne153675:0"

#: frontend/src/pages/Deal.vue:69
msgid "Organization logo"
msgstr "crwdns153677:0crwdne153677:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:208
#: frontend/src/pages/Organization.vue:238
msgid "Organizations"
msgstr "crwdns153681:0crwdne153681:0"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Other features"
msgstr "crwdns156516:0crwdne156516:0"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "crwdns153683:0crwdne153683:0"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Outbound Call"
msgstr "crwdns153685:0crwdne153685:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "crwdns153687:0crwdne153687:0"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "crwdns153689:0crwdne153689:0"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "crwdns153691:0crwdne153691:0"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "crwdns156518:0crwdne156518:0"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "crwdns153693:0crwdne153693:0"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "crwdns156520:0crwdne156520:0"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "crwdns156522:0crwdne156522:0"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:41
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:41
msgid "Payment Reminder"
msgstr "crwdns153695:0crwdne153695:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:72
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:72
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "crwdns153697:0{{ name }}crwdne153697:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "crwdns153699:0crwdne153699:0"

#: frontend/src/components/Settings/InviteUserPage.vue:66
msgid "Pending Invites"
msgstr "crwdns153701:0crwdne153701:0"

#: frontend/src/pages/Dashboard.vue:79
msgid "Period"
msgstr "crwdns156524:0crwdne156524:0"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "crwdns153703:0crwdne153703:0"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:498
#: frontend/src/pages/Organization.vue:507
msgid "Phone"
msgstr "crwdns153705:0crwdne153705:0"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "crwdns153707:0crwdne153707:0"

#: frontend/src/components/ViewControls.vue:1138
msgid "Pin View"
msgstr "crwdns153709:0crwdne153709:0"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "crwdns153711:0crwdne153711:0"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "crwdns153713:0crwdne153713:0"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Pinned view"
msgstr "crwdns156526:0crwdne156526:0"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "crwdns153715:0crwdne153715:0"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "crwdns156528:0crwdne156528:0"

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "crwdns153717:0crwdne153717:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:168
msgid "Please enter a valid URL"
msgstr "crwdns153719:0crwdne153719:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:159
msgid "Please enter the Exchangerate Host access key."
msgstr "crwdns157270:0crwdne157270:0"

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "crwdns156530:0crwdne156530:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:152
msgid "Please select a currency before saving."
msgstr "crwdns157272:0crwdne157272:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:145
#: frontend/src/pages/MobileLead.vue:434
msgid "Please select an existing contact"
msgstr "crwdns153721:0crwdne153721:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:150
#: frontend/src/pages/MobileLead.vue:439
msgid "Please select an existing organization"
msgstr "crwdns153723:0crwdne153723:0"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "crwdns153725:0crwdne153725:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:169
msgid "Please specify a reason for losing the deal."
msgstr "crwdns156532:0crwdne156532:0"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Please specify the reason for losing the deal."
msgstr "crwdns156534:0crwdne156534:0"

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "crwdns153727:0crwdne153727:0"

#: frontend/src/pages/Deal.vue:222 frontend/src/pages/MobileDeal.vue:151
msgid "Primary"
msgstr "crwdns153729:0crwdne153729:0"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "crwdns156536:0crwdne156536:0"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "crwdns156538:0crwdne156538:0"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "crwdns156540:0crwdne156540:0"

#: frontend/src/pages/Deal.vue:677 frontend/src/pages/MobileDeal.vue:568
msgid "Primary contact set"
msgstr "crwdns153731:0crwdne153731:0"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "crwdns153733:0crwdne153733:0"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Priority"
msgstr "crwdns153735:0crwdne153735:0"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "crwdns153737:0crwdne153737:0"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "crwdns153739:0crwdne153739:0"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "crwdns156542:0crwdne156542:0"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "crwdns156544:0crwdne156544:0"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "crwdns156546:0crwdne156546:0"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "crwdns156548:0crwdne156548:0"

#: frontend/src/components/Layouts/AppSidebar.vue:535
#: frontend/src/components/Settings/Settings.vue:79
msgid "Profile"
msgstr "crwdns153741:0crwdne153741:0"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "crwdns153743:0crwdne153743:0"

#: crm/api/dashboard.py:667
msgid "Projected vs actual revenue based on deal probability"
msgstr "crwdns156550:0crwdne156550:0"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "crwdns153745:0crwdne153745:0"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "crwdns153747:0crwdne153747:0"

#: frontend/src/components/Layouts/AppSidebar.vue:565
msgid "Public view"
msgstr "crwdns156552:0crwdne156552:0"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "crwdns156554:0crwdne156554:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "crwdns153749:0crwdne153749:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "crwdns153751:0crwdne153751:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "crwdns153753:0crwdne153753:0"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "crwdns153755:0crwdne153755:0"

#: frontend/src/components/Layouts/AppSidebar.vue:589
msgid "Quick entry layout"
msgstr "crwdns156556:0crwdne156556:0"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "crwdns156558:0crwdne156558:0"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "crwdns153757:0crwdne153757:0"

#: crm/api/dashboard.py:886
msgid "Reason"
msgstr "crwdns156560:0crwdne156560:0"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "crwdns153759:0crwdne153759:0"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "crwdns153761:0crwdne153761:0"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "crwdns153763:0crwdne153763:0"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "crwdns153765:0crwdne153765:0"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "crwdns153767:0crwdne153767:0"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "crwdns153769:0crwdne153769:0"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "crwdns153771:0crwdne153771:0"

#: frontend/src/components/ViewControls.vue:25
#: frontend/src/components/ViewControls.vue:160
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "crwdns153773:0crwdne153773:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:104
msgid "Reject"
msgstr "crwdns153777:0crwdne153777:0"

#: frontend/src/components/Settings/Users.vue:210
#: frontend/src/components/Settings/Users.vue:213
#: frontend/src/pages/Deal.vue:626
msgid "Remove"
msgstr "crwdns153779:0crwdne153779:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "crwdns153781:0crwdne153781:0"

#: frontend/src/components/FieldLayoutEditor.vue:444
msgid "Remove and move fields to previous column"
msgstr "crwdns153783:0crwdne153783:0"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove column"
msgstr "crwdns153785:0crwdne153785:0"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:101
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:47
msgid "Remove image"
msgstr "crwdns153787:0crwdne153787:0"

#: frontend/src/components/FieldLayoutEditor.vue:365
msgid "Remove section"
msgstr "crwdns153789:0crwdne153789:0"

#: frontend/src/components/FieldLayoutEditor.vue:324
msgid "Remove tab"
msgstr "crwdns153791:0crwdne153791:0"

#: frontend/src/components/Activities/EmailArea.vue:31
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "crwdns153793:0crwdne153793:0"

#: frontend/src/components/Activities/EmailArea.vue:44
msgid "Reply All"
msgstr "crwdns153795:0crwdne153795:0"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "crwdns156562:0crwdne156562:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "crwdns153797:0crwdne153797:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:82
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "crwdns153799:0crwdne153799:0"

#: frontend/src/components/ColumnSettings.vue:82
msgid "Reset Changes"
msgstr "crwdns153801:0crwdne153801:0"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "crwdns153803:0crwdne153803:0"

#: frontend/src/components/ColumnSettings.vue:93
msgid "Reset to Default"
msgstr "crwdns153805:0crwdne153805:0"

#: frontend/src/pages/Dashboard.vue:34
msgid "Reset to default"
msgstr "crwdns156564:0crwdne156564:0"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "crwdns153807:0crwdne153807:0"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "crwdns153809:0crwdne153809:0"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "crwdns153811:0crwdne153811:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "crwdns153813:0crwdne153813:0"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "crwdns153815:0crwdne153815:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:54
msgid "Retake"
msgstr "crwdns153817:0crwdne153817:0"

#: crm/api/dashboard.py:675
msgid "Revenue"
msgstr "crwdns156566:0crwdne156566:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:84
msgid "Rich Text"
msgstr "crwdns153819:0crwdne153819:0"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "crwdns153821:0crwdne153821:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "crwdns153823:0crwdne153823:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Ringing..."
msgstr "crwdns153825:0crwdne153825:0"

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:44
msgid "Role"
msgstr "crwdns153827:0crwdne153827:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "crwdns153829:0crwdne153829:0"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "crwdns153831:0crwdne153831:0"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "crwdns153833:0crwdne153833:0"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "crwdns153835:0crwdne153835:0"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "crwdns153837:0crwdne153837:0"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "crwdns153839:0crwdne153839:0"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "crwdns153841:0crwdne153841:0"

#: frontend/src/components/EmailEditor.vue:82
msgid "SUBJECT"
msgstr "crwdns153843:0crwdne153843:0"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Sales Manager"
msgstr "crwdns153845:0crwdne153845:0"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:90
#: frontend/src/components/Settings/InviteUserPage.vue:170
#: frontend/src/components/Settings/InviteUserPage.vue:177
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:186
#: frontend/src/components/Settings/Users.vue:268
#: frontend/src/components/Settings/Users.vue:271
msgid "Sales User"
msgstr "crwdns153847:0crwdne153847:0"

#: crm/api/dashboard.py:598
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "crwdns156568:0crwdne156568:0"

#: frontend/src/pages/Dashboard.vue:106
msgid "Sales user"
msgstr "crwdns156570:0crwdne156570:0"

#: crm/api/dashboard.py:1079
msgid "Salesperson"
msgstr "crwdns156572:0crwdne156572:0"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "crwdns153849:0crwdne153849:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "crwdns153851:0crwdne153851:0"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:87
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/DropdownItem.vue:21
#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/Settings/General/CurrencySettings.vue:182
#: frontend/src/components/Telephony/ExotelCallUI.vue:231
#: frontend/src/components/ViewControls.vue:123
#: frontend/src/pages/Dashboard.vue:45
msgid "Save"
msgstr "crwdns153853:0crwdne153853:0"

#: frontend/src/components/Modals/ViewModal.vue:13
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:157
msgid "Save Changes"
msgstr "crwdns153855:0crwdne153855:0"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "crwdns153857:0crwdne153857:0"

#: frontend/src/components/Layouts/AppSidebar.vue:564
msgid "Saved view"
msgstr "crwdns156574:0crwdne156574:0"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "crwdns153859:0crwdne153859:0"

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "crwdns153861:0crwdne153861:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "crwdns156576:0crwdne156576:0"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "crwdns156578:0crwdne156578:0"

#: frontend/src/components/FieldLayoutEditor.vue:342
msgid "Section"
msgstr "crwdns153863:0crwdne153863:0"

#: frontend/src/pages/Dashboard.vue:59
msgid "Select Range"
msgstr "crwdns156580:0crwdne156580:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:58
msgid "Select currency"
msgstr "crwdns156582:0crwdne156582:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:82
msgid "Select provider"
msgstr "crwdns157274:0crwdne157274:0"

#: frontend/src/components/FieldLayout/Field.vue:332
msgid "Select {0}"
msgstr "crwdns153865:0{0}crwdne153865:0"

#: frontend/src/components/EmailEditor.vue:162
msgid "Send"
msgstr "crwdns153867:0crwdne153867:0"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "crwdns153869:0crwdne153869:0"

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "Send Template"
msgstr "crwdns153873:0crwdne153873:0"

#: frontend/src/pages/Deal.vue:93 frontend/src/pages/Lead.vue:144
msgid "Send an email"
msgstr "crwdns153875:0crwdne153875:0"

#: frontend/src/components/Layouts/AppSidebar.vue:455
msgid "Send email"
msgstr "crwdns156584:0crwdne156584:0"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "crwdns156586:0crwdne156586:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "crwdns153877:0crwdne153877:0"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "crwdns153879:0crwdne153879:0"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "crwdns157276:0crwdne157276:0"

#: frontend/src/components/Layouts/AppSidebar.vue:576
msgid "Service level agreement"
msgstr "crwdns156588:0crwdne156588:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:69
msgid "Set all as private"
msgstr "crwdns153881:0crwdne153881:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:62
msgid "Set all as public"
msgstr "crwdns153883:0crwdne153883:0"

#: frontend/src/pages/Deal.vue:80
msgid "Set an organization"
msgstr "crwdns153885:0crwdne153885:0"

#: frontend/src/pages/Deal.vue:634 frontend/src/pages/MobileDeal.vue:525
msgid "Set as Primary Contact"
msgstr "crwdns153887:0crwdne153887:0"

#: frontend/src/components/ViewControls.vue:1123
msgid "Set as default"
msgstr "crwdns153889:0crwdne153889:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:173
msgid "Set currency"
msgstr "crwdns156590:0crwdne156590:0"

#: frontend/src/pages/Lead.vue:122
msgid "Set first name"
msgstr "crwdns153891:0crwdne153891:0"

#: frontend/src/components/Layouts/AppSidebar.vue:528
msgid "Setting up"
msgstr "crwdns156592:0crwdne156592:0"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "crwdns156594:0crwdne156594:0"

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "crwdns156596:0crwdne156596:0"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "crwdns156598:0crwdne156598:0"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "crwdns156600:0crwdne156600:0"

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "crwdns156602:0crwdne156602:0"

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "crwdns156604:0crwdne156604:0"

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "crwdns156606:0crwdne156606:0"

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:532
#: frontend/src/components/Settings/Settings.vue:11
#: frontend/src/components/Settings/Settings.vue:75
msgid "Settings"
msgstr "crwdns153893:0crwdne153893:0"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "crwdns156608:0crwdne156608:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "crwdns157278:0{0}crwdnd157278:0{1}crwdne157278:0"

#: frontend/src/components/Layouts/AppSidebar.vue:334
msgid "Setup your password"
msgstr "crwdns156610:0crwdne156610:0"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Show"
msgstr "crwdns153895:0crwdne153895:0"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "crwdns156612:0crwdne156612:0"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Show border"
msgstr "crwdns153897:0crwdne153897:0"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Show label"
msgstr "crwdns153899:0crwdne153899:0"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "crwdns153901:0crwdne153901:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "crwdns153903:0crwdne153903:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "crwdns153905:0crwdne153905:0"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "crwdns153909:0crwdne153909:0"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:22
#: frontend/src/components/SortBy.vue:240
msgid "Sort"
msgstr "crwdns153911:0crwdne153911:0"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Modals/EditValueModal.vue:10
msgid "Source"
msgstr "crwdns153913:0crwdne153913:0"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "Source Name"
msgstr "crwdns153915:0crwdne153915:0"

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "crwdns156614:0crwdne156614:0"

#: crm/api/dashboard.py:731 crm/api/dashboard.py:790
msgid "Stage"
msgstr "crwdns156616:0crwdne156616:0"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "crwdns153917:0crwdne153917:0"

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "crwdns156618:0crwdne156618:0"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "crwdns153919:0crwdne153919:0"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "crwdns153921:0crwdne153921:0"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Start Time"
msgstr "crwdns153923:0crwdne153923:0"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "crwdns156620:0crwdne156620:0"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:516
#: frontend/src/pages/MobileOrganization.vue:460
#: frontend/src/pages/Organization.vue:469
msgid "Status"
msgstr "crwdns153925:0crwdne153925:0"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "crwdns153927:0crwdne153927:0"

#: frontend/src/components/Modals/DealModal.vue:217
#: frontend/src/components/Modals/LeadModal.vue:158
msgid "Status is required"
msgstr "crwdns153929:0crwdne153929:0"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "crwdns153931:0crwdne153931:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:71
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:71
msgid "Subject"
msgstr "crwdns153933:0crwdne153933:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:161
msgid "Subject is required"
msgstr "crwdns156622:0crwdne156622:0"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "crwdns153935:0{0}crwdne153935:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "crwdns153939:0crwdne153939:0"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "crwdns156624:0crwdne156624:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:49
msgid "Switch camera"
msgstr "crwdns153941:0crwdne153941:0"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "crwdns156626:0crwdne156626:0"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "System Manager"
msgstr "crwdns153943:0crwdne153943:0"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "crwdns153945:0crwdne153945:0"

#: frontend/src/components/Telephony/ExotelCallUI.vue:151
msgid "Take a note..."
msgstr "crwdns153947:0crwdne153947:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:550
msgid "Task"
msgstr "crwdns153949:0crwdne153949:0"

#: frontend/src/pages/Deal.vue:565 frontend/src/pages/Lead.vue:431
#: frontend/src/pages/MobileDeal.vue:458 frontend/src/pages/MobileLead.vue:365
msgid "Tasks"
msgstr "crwdns153951:0crwdne153951:0"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "crwdns156628:0crwdne156628:0"

#: frontend/src/components/Settings/Settings.vue:123
msgid "Telephony"
msgstr "crwdns153953:0crwdne153953:0"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "crwdns153955:0crwdne153955:0"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "crwdns156630:0crwdne156630:0"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:178
msgid "Template created successfully"
msgstr "crwdns156632:0crwdne156632:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:214
msgid "Template deleted successfully"
msgstr "crwdns156634:0crwdne156634:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:198
msgid "Template disabled successfully"
msgstr "crwdns156636:0crwdne156636:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template enabled successfully"
msgstr "crwdns156638:0crwdne156638:0"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "crwdns156640:0crwdne156640:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:243
msgid "Template renamed successfully"
msgstr "crwdns156642:0crwdne156642:0"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
msgid "Template updated successfully"
msgstr "crwdns156644:0crwdne156644:0"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "crwdns153959:0crwdne153959:0"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1022 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "crwdns153961:0crwdne153961:0"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "crwdns153963:0crwdne153963:0"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "crwdns153965:0crwdne153965:0"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "crwdns153967:0{0}crwdnd153967:0{1}crwdne153967:0"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "crwdns156646:0crwdne156646:0"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "crwdns156648:0crwdne156648:0"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "crwdns153969:0crwdne153969:0"

#: frontend/src/components/Modals/AddressModal.vue:129
#: frontend/src/components/Modals/CallLogModal.vue:132
msgid "These fields are required: {0}"
msgstr "crwdns156650:0{0}crwdne156650:0"

#: frontend/src/components/Filter.vue:644
msgid "This Month"
msgstr "crwdns153971:0crwdne153971:0"

#: frontend/src/components/Filter.vue:648
msgid "This Quarter"
msgstr "crwdns153973:0crwdne153973:0"

#: frontend/src/components/Filter.vue:640
msgid "This Week"
msgstr "crwdns153975:0crwdne153975:0"

#: frontend/src/components/Filter.vue:652
msgid "This Year"
msgstr "crwdns153977:0crwdne153977:0"

#: frontend/src/components/SidePanelLayoutEditor.vue:119
msgid "This section is not editable"
msgstr "crwdns153979:0crwdne153979:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "crwdns156652:0crwdne156652:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "crwdns156654:0crwdne156654:0"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "crwdns153981:0crwdne153981:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "crwdns153983:0crwdne153983:0"

#: frontend/src/components/Filter.vue:356
msgid "Timespan"
msgstr "crwdns153985:0crwdne153985:0"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:41
msgid "Title"
msgstr "crwdns153987:0crwdne153987:0"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:32
msgid "Title Field"
msgstr "crwdns153989:0crwdne153989:0"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:63
msgid "To"
msgstr "crwdns153991:0crwdne153991:0"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "crwdns153993:0crwdne153993:0"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "crwdns156656:0crwdne156656:0"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "crwdns153995:0crwdne153995:0"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "crwdns156658:0crwdne156658:0"

#: frontend/src/components/Filter.vue:632
msgid "Today"
msgstr "crwdns153997:0crwdne153997:0"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "crwdns153999:0crwdne153999:0"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "crwdns154001:0crwdne154001:0"

#: frontend/src/components/Filter.vue:636
msgid "Tomorrow"
msgstr "crwdns154003:0crwdne154003:0"

#: frontend/src/components/Modals/NoteModal.vue:37
#: frontend/src/components/Modals/TaskModal.vue:59
msgid "Took a call with John Doe and discussed the new project."
msgstr "crwdns154005:0crwdne154005:0"

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "crwdns156660:0crwdne156660:0"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "crwdns154007:0crwdne154007:0"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "crwdns156662:0crwdne156662:0"

#: crm/api/dashboard.py:123
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "crwdns156664:0crwdne156664:0"

#: crm/api/dashboard.py:124
msgid "Total number of leads"
msgstr "crwdns156666:0crwdne156666:0"

#: crm/api/dashboard.py:182
msgid "Total number of non won/lost deals"
msgstr "crwdns156668:0crwdne156668:0"

#: crm/api/dashboard.py:297
msgid "Total number of won deals based on its closure date"
msgstr "crwdns156670:0crwdne156670:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "crwdns154011:0crwdne154011:0"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "crwdns154013:0crwdne154013:0"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:596
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "crwdns154015:0crwdne154015:0"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "crwdns154017:0crwdne154017:0"

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "crwdns154019:0crwdne154019:0"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "crwdns154021:0crwdne154021:0"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "crwdns154023:0crwdne154023:0"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Type"
msgstr "crwdns154025:0crwdne154025:0"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:73
#: frontend/src/components/Controls/MultiSelectUserInput.vue:73
msgid "Type an email address to invite"
msgstr "crwdns156672:0crwdne156672:0"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "crwdns154027:0crwdne154027:0"

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "crwdns154029:0crwdne154029:0"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Uncollapsible"
msgstr "crwdns154031:0crwdne154031:0"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:130
msgid "Unknown"
msgstr "crwdns154033:0crwdne154033:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "crwdns156674:0crwdne156674:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:77
msgid "Unlink all"
msgstr "crwdns156676:0crwdne156676:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "crwdns156678:0crwdne156678:0"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "crwdns156680:0{0}crwdne156680:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:242
msgid "Unlink linked item"
msgstr "crwdns156682:0crwdne156682:0"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink {0} item(s)"
msgstr "crwdns156684:0{0}crwdne156684:0"

#: frontend/src/components/ViewControls.vue:1138
msgid "Unpin View"
msgstr "crwdns154035:0crwdne154035:0"

#: frontend/src/components/ViewControls.vue:975
msgid "Unsaved Changes"
msgstr "crwdns154037:0crwdne154037:0"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "crwdns154039:0crwdne154039:0"

#: frontend/src/components/ColumnSettings.vue:138
#: frontend/src/components/Modals/AssignmentModal.vue:17
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:17
#: frontend/src/components/Settings/General/BrandSettings.vue:23
#: frontend/src/components/Settings/General/CurrencySettings.vue:23
#: frontend/src/components/Settings/General/HomeActions.vue:17
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:219
#: frontend/src/components/ViewControls.vue:980
msgid "Update"
msgstr "crwdns154041:0crwdne154041:0"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "crwdns156686:0crwdne156686:0"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "crwdns154043:0{0}crwdne154043:0"

#: frontend/src/components/FilesUploader/FilesUploader.vue:86
msgid "Upload"
msgstr "crwdns154045:0crwdne154045:0"

#: frontend/src/components/Activities/Activities.vue:404
#: frontend/src/components/Activities/ActivityHeader.vue:62
#: frontend/src/components/Activities/ActivityHeader.vue:158
msgid "Upload Attachment"
msgstr "crwdns154047:0crwdne154047:0"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "crwdns154049:0crwdne154049:0"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "crwdns154051:0crwdne154051:0"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "crwdns154053:0crwdne154053:0"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:42 frontend/src/pages/Lead.vue:96
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:42
msgid "Upload image"
msgstr "crwdns154055:0crwdne154055:0"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "crwdns154057:0crwdne154057:0"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "crwdns154059:0crwdne154059:0"

#: frontend/src/components/Settings/Users.vue:301
msgid "User {0} has been removed"
msgstr "crwdns156688:0{0}crwdne156688:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:95
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "crwdns156690:0crwdne156690:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:103
msgid "Users added successfully"
msgstr "crwdns156692:0crwdne156692:0"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "crwdns154061:0crwdne154061:0"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "crwdns154063:0crwdne154063:0"

#: frontend/src/components/Modals/ViewModal.vue:25
msgid "View Name"
msgstr "crwdns154065:0crwdne154065:0"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Views"
msgstr "crwdns156694:0crwdne156694:0"

#: frontend/src/components/Layouts/AppSidebar.vue:558
msgid "Web form"
msgstr "crwdns156696:0crwdne156696:0"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "crwdns154067:0crwdne154067:0"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "crwdns154069:0crwdne154069:0"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "crwdns154073:0crwdne154073:0"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "crwdns154075:0crwdne154075:0"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "crwdns154077:0crwdne154077:0"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "crwdns156698:0{0}crwdne156698:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:598
#: frontend/src/components/Settings/Settings.vue:129
#: frontend/src/pages/Deal.vue:580 frontend/src/pages/Lead.vue:446
#: frontend/src/pages/MobileDeal.vue:473 frontend/src/pages/MobileLead.vue:380
msgid "WhatsApp"
msgstr "crwdns154079:0crwdne154079:0"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "crwdns154081:0crwdne154081:0"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "Where"
msgstr "crwdns154083:0crwdne154083:0"

#: frontend/src/components/ColumnSettings.vue:117
msgid "Width"
msgstr "crwdns154085:0crwdne154085:0"

#: frontend/src/components/ColumnSettings.vue:122
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "crwdns154087:0crwdne154087:0"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "crwdns156700:0crwdne156700:0"

#: crm/api/dashboard.py:296
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "crwdns156702:0crwdne156702:0"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "crwdns154089:0crwdne154089:0"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "crwdns154091:0crwdne154091:0"

#: frontend/src/components/Filter.vue:628
msgid "Yesterday"
msgstr "crwdns154093:0crwdne154093:0"

#: crm/api/whatsapp.py:36 crm/api/whatsapp.py:216 crm/api/whatsapp.py:230
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "crwdns154095:0crwdne154095:0"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "crwdns156704:0crwdne156704:0"

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "crwdns154097:0crwdne154097:0"

#: frontend/src/components/Settings/General/CurrencySettings.vue:107
msgid "You can get your access key from "
msgstr "crwdns157280:0crwdne157280:0"

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "crwdns154099:0crwdne154099:0"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "crwdns154101:0crwdne154101:0"

#: frontend/src/data/document.js:32
msgid "You do not have permission to access this document"
msgstr "crwdns157282:0crwdne157282:0"

#: frontend/src/components/ViewControls.vue:976
msgid "You have unsaved changes. Do you want to save them?"
msgstr "crwdns154103:0crwdne154103:0"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "crwdns154105:0crwdne154105:0"

#: crm/api/todo.py:111
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "crwdns154213:0{0}crwdnd154213:0{1}crwdne154213:0"

#: crm/api/todo.py:46 crm/api/todo.py:89
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "crwdns154107:0{0}crwdnd154107:0{1}crwdnd154107:0{2}crwdne154107:0"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "crwdns154109:0crwdne154109:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "crwdns154111:0crwdne154111:0"

#: crm/api/todo.py:120
msgid "assigned a new task {0} to you"
msgstr "crwdns154215:0{0}crwdne154215:0"

#: crm/api/todo.py:100
msgid "assigned a {0} {1} to you"
msgstr "crwdns154217:0{0}crwdnd154217:0{1}crwdne154217:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "crwdns154113:0crwdne154113:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "crwdns154115:0crwdne154115:0"

#: frontend/src/components/Activities/Activities.vue:232
msgid "changes from"
msgstr "crwdns154117:0crwdne154117:0"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "crwdns154119:0crwdne154119:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "crwdns154121:0crwdne154121:0"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:268
#: frontend/src/components/Controls/MultiSelectUserInput.vue:242
msgid "email already exists"
msgstr "crwdns156706:0crwdne156706:0"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:113
msgid "exchangerate.host"
msgstr "crwdns157284:0crwdne157284:0"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "crwdns157286:0crwdne157286:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "crwdns154123:0crwdne154123:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "crwdns154125:0crwdne154125:0"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "crwdns154127:0crwdne154127:0"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "crwdns154129:0crwdne154129:0"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "crwdns154131:0crwdne154131:0"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "crwdns156708:0crwdne156708:0"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "crwdns156710:0crwdne156710:0"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "crwdns156712:0crwdne156712:0"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "crwdns156714:0crwdne156714:0"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "crwdns156716:0{0}crwdne156716:0"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "crwdns156718:0{0}crwdne156718:0"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "crwdns156720:0{0}crwdne156720:0"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "crwdns156722:0{0}crwdne156722:0"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "crwdns156724:0{0}crwdne156724:0"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "crwdns156726:0{0}crwdne156726:0"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "crwdns156728:0{0}crwdne156728:0"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "crwdns156730:0{0}crwdne156730:0"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "crwdns156732:0{0}crwdne156732:0"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "crwdns156734:0{0}crwdne156734:0"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "crwdns156736:0{0}crwdne156736:0"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "crwdns156738:0{0}crwdne156738:0"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
#: frontend/src/components/Settings/InviteUserPage.vue:37
msgid "<EMAIL>"
msgstr "crwdns156740:0crwdne156740:0"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "crwdns156742:0crwdne156742:0"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "crwdns154133:0crwdne154133:0"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "crwdns154135:0crwdne154135:0"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "crwdns154137:0crwdne154137:0"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:65
#: frontend/src/pages/MobileNotification.vue:52
msgid "mentioned you in {0}"
msgstr "crwdns154139:0{0}crwdne154139:0"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "next"
msgstr "crwdns154141:0crwdne154141:0"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "crwdns156744:0crwdne156744:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "crwdns154143:0crwdne154143:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "crwdns154145:0crwdne154145:0"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "previous"
msgstr "crwdns154147:0crwdne154147:0"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "private"
msgstr "crwdns154149:0crwdne154149:0"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "public"
msgstr "crwdns154151:0crwdne154151:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "crwdns154153:0crwdne154153:0"

#: crm/api/whatsapp.py:37
msgid "received a whatsapp message in {0}"
msgstr "crwdns154219:0{0}crwdne154219:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "crwdns154155:0crwdne154155:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "crwdns154157:0crwdne154157:0"

#: frontend/src/components/Activities/Activities.vue:274
#: frontend/src/components/Activities/Activities.vue:337
msgid "to"
msgstr "crwdns154159:0crwdne154159:0"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "crwdns156746:0crwdne156746:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "crwdns154161:0crwdne154161:0"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "crwdns154163:0crwdne154163:0"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "crwdns156748:0crwdne156748:0"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "crwdns156750:0{0}crwdne156750:0"

#: crm/api/todo.py:50
msgid "{0} assigned a {1} {2} to you"
msgstr "crwdns154165:0{0}crwdnd154165:0{1}crwdnd154165:0{2}crwdne154165:0"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "crwdns156752:0{0}crwdne156752:0"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "crwdns156754:0{0}crwdne156754:0"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "crwdns156756:0{0}crwdne156756:0"

#: frontend/src/components/Settings/Users.vue:291
msgid "{0} has been granted {1} access"
msgstr "crwdns156758:0{0}crwdnd156758:0{1}crwdne156758:0"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "crwdns156760:0{0}crwdne156760:0"

#: frontend/src/components/EmailEditor.vue:29
#: frontend/src/components/EmailEditor.vue:64
#: frontend/src/components/EmailEditor.vue:77
#: frontend/src/components/Modals/AddExistingUserModal.vue:36
#: frontend/src/components/Settings/InviteUserPage.vue:41
msgid "{0} is an invalid email address"
msgstr "crwdns154169:0{0}crwdne154169:0"

#: frontend/src/components/Modals/ConvertToDealModal.vue:181
msgid "{0} is required"
msgstr "crwdns156762:0{0}crwdne156762:0"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "crwdns156764:0{0}crwdne156764:0"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "crwdns156766:0{0}crwdne156766:0"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "crwdns156768:0{0}crwdne156768:0"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "crwdns156770:0{0}crwdne156770:0"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "crwdns156772:0{0}crwdne156772:0"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "crwdns156774:0{0}crwdne156774:0"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "crwdns156776:0{0}crwdne156776:0"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "crwdns156778:0crwdne156778:0"

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "crwdns156780:0{0}crwdne156780:0"

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "crwdns156782:0{0}crwdne156782:0"

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "crwdns156784:0{0}crwdne156784:0"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "crwdns156786:0{0}crwdnd156786:0{1}crwdne156786:0"

