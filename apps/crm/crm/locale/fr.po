msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-08-03 09:38+0000\n"
"PO-Revision-Date: 2025-08-04 08:34\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: French\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: fr_FR\n"

#: frontend/src/components/ViewControls.vue:1217
msgid " (New)"
msgstr ""

#: frontend/src/components/Modals/TaskModal.vue:99
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr ""

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "Il y a 1 heure"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "Il y a 1 minute"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "Il y a 1 mois"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "Il ya 1 semaine"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "Il y a 1 an"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr ""

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:98
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:98
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr ""

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr ""

#: frontend/src/components/CommunicationArea.vue:85
msgid "@John, can you please check this?"
msgstr ""

#: crm/fcrm/doctype/crm_lead/crm_lead.py:56
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Un responsable requiert le nom d'une personne ou le nom d'une organisation"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Key"
msgstr "Clé API"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr ""

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Secret"
msgstr "Secret API"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr ""

#: frontend/src/components/Telephony/TwilioCallUI.vue:92
msgid "Accept"
msgstr ""

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Accepté.e"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr ""

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Clé d'accès"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "La clé d'accès est requise pour le fournisseur de service : {0}"

#: frontend/src/components/Settings/General/CurrencySettings.vue:97
msgid "Access key"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:101
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Nom du Compte"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr ""

#: frontend/src/components/CustomActions.vue:73
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1109
msgid "Actions"
msgstr "Actions"

#: frontend/src/pages/Deal.vue:540 frontend/src/pages/Lead.vue:406
#: frontend/src/pages/MobileDeal.vue:432 frontend/src/pages/MobileLead.vue:339
msgid "Activity"
msgstr "Historique"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:53
msgid "Add"
msgstr "Ajouter"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:69
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Ajouter une Colonne"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:58
#: frontend/src/components/FieldLayoutEditor.vue:173
#: frontend/src/components/Kanban/KanbanSettings.vue:84
#: frontend/src/components/SidePanelLayoutEditor.vue:98
msgid "Add Field"
msgstr ""

#: frontend/src/components/Filter.vue:138
msgid "Add Filter"
msgstr ""

#: frontend/src/components/Controls/Grid.vue:321
msgid "Add Row"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:200
#: frontend/src/components/SidePanelLayoutEditor.vue:130
msgid "Add Section"
msgstr ""

#: frontend/src/components/SortBy.vue:148
msgid "Add Sort"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr ""

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Ajouter des vacances hebdomadaires"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:426
msgid "Add column"
msgstr ""

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr ""

#: frontend/src/components/ViewControls.vue:104
msgid "Add filter"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr ""

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr ""

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr ""

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Ajouter aux vacances"

#: frontend/src/components/Layouts/AppSidebar.vue:434
msgid "Add your first comment"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Adresse"

#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:172
#: frontend/src/components/Settings/InviteUserPage.vue:179
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:184
#: frontend/src/components/Settings/Users.vue:244
#: frontend/src/components/Settings/Users.vue:247
msgid "Admin"
msgstr "Administrateur"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr ""

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Tout"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:512 frontend/src/pages/MobileContact.vue:510
#: frontend/src/pages/MobileOrganization.vue:454
#: frontend/src/pages/Organization.vue:463
msgid "Amount"
msgstr "Montant"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr ""

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr ""

#: frontend/src/data/document.js:63
msgid "An error occurred while updating the document"
msgstr ""

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "Un fichier d'icône avec l’extension .ico. Devrait être 16 x 16 px. Générer en utilisant un générateur de favicon. [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr ""

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "And"
msgstr ""

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "CA annuel"

#: frontend/src/components/Modals/DealModal.vue:201
#: frontend/src/components/Modals/LeadModal.vue:142
msgid "Annual Revenue should be a number"
msgstr ""

#: frontend/src/components/Settings/General/BrandSettings.vue:69
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr ""

#: frontend/src/components/Settings/General/BrandSettings.vue:103
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr ""

#: frontend/src/components/Kanban/KanbanSettings.vue:107
#: frontend/src/components/Kanban/KanbanView.vue:45
msgid "Apply"
msgstr ""

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Appliquer Sur"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Appliquer à"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:139
msgid "Are you sure you want to delete this attachment?"
msgstr ""

#: frontend/src/pages/MobileContact.vue:263
msgid "Are you sure you want to delete this contact?"
msgstr ""

#: frontend/src/pages/MobileOrganization.vue:264
msgid "Are you sure you want to delete this organization?"
msgstr ""

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr ""

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Êtes-vous sûr de vouloir vous connecter à votre tableau de bord Frappe Cloud ?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:174
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr ""

#: frontend/src/components/ListBulkActions.vue:184
#: frontend/src/components/Modals/AssignmentModal.vue:5
msgid "Assign To"
msgstr "Attribuer À"

#: frontend/src/components/AssignTo.vue:9
msgid "Assign to"
msgstr ""

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Assigné À"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Affectation"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Règle d&#39;assignation"

#: frontend/src/components/ListBulkActions.vue:152
msgid "Assignment cleared successfully"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:577
msgid "Assignment rule"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:176
msgid "At least one field is required"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:76
msgid "Attach"
msgstr "Attacher"

#: frontend/src/pages/Deal.vue:117 frontend/src/pages/Lead.vue:174
msgid "Attach a file"
msgstr ""

#: frontend/src/pages/Deal.vue:575 frontend/src/pages/Lead.vue:441
#: frontend/src/pages/MobileDeal.vue:468 frontend/src/pages/MobileLead.vue:375
msgid "Attachments"
msgstr "Pièces jointes"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr ""

#: crm/api/dashboard.py:238
msgid "Average deal value of non won/lost deals"
msgstr ""

#: crm/api/dashboard.py:411
msgid "Average deal value of ongoing & won deals"
msgstr ""

#: crm/api/dashboard.py:354
msgid "Average deal value of won deals"
msgstr ""

#: crm/api/dashboard.py:518
msgid "Average time taken from deal creation to deal closure"
msgstr ""

#: crm/api/dashboard.py:464
msgid "Average time taken from lead creation to deal closure"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr ""

#: crm/api/dashboard.py:410
msgid "Avg. deal value"
msgstr ""

#: crm/api/dashboard.py:237
msgid "Avg. ongoing deal value"
msgstr ""

#: crm/api/dashboard.py:517
msgid "Avg. time to close a deal"
msgstr ""

#: crm/api/dashboard.py:463
msgid "Avg. time to close a lead"
msgstr ""

#: crm/api/dashboard.py:353
msgid "Avg. won deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr ""

#: frontend/src/components/Activities/EmailArea.vue:72
#: frontend/src/components/EmailEditor.vue:44
#: frontend/src/components/EmailEditor.vue:69
msgid "BCC"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr ""

#: frontend/src/components/Filter.vue:355
msgid "Between"
msgstr "Entre"

#: frontend/src/components/Settings/General/BrandSettings.vue:40
msgid "Brand name"
msgstr ""

#: frontend/src/components/Settings/General/BrandSettings.vue:9
msgid "Brand settings"
msgstr ""

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Occupé"

#: frontend/src/components/Activities/EmailArea.vue:67
#: frontend/src/components/EmailEditor.vue:34
#: frontend/src/components/EmailEditor.vue:56
msgid "CC"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:318
msgid "CRM Dashboard"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:42
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr ""

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Détails de l'appel"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr ""

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:551
msgid "Call log"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr ""

#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:43
msgid "Call with John Doe"
msgstr ""

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr ""

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Calling..."
msgstr ""

#: frontend/src/pages/Deal.vue:560 frontend/src/pages/Lead.vue:426
#: frontend/src/pages/MobileDeal.vue:452 frontend/src/pages/MobileLead.vue:359
msgid "Calls"
msgstr "Appels"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Caméra"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/ColumnSettings.vue:132
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:114
#: frontend/src/components/Modals/AssignmentModal.vue:9
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:77
#: frontend/src/components/ViewControls.vue:56
#: frontend/src/components/ViewControls.vue:156
#: frontend/src/pages/Dashboard.vue:41
msgid "Cancel"
msgstr "Annuler"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Annulé"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:94
msgid "Capture"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Capturing leads"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:485
msgid "Change"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Changez le Mot de Passe"

#: frontend/src/components/Activities/TaskArea.vue:44
msgid "Change Status"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:476
#: frontend/src/components/Layouts/AppSidebar.vue:484
msgid "Change deal status"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:41 frontend/src/pages/Lead.vue:95
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:41
msgid "Change image"
msgstr ""

#: frontend/src/pages/Dashboard.vue:28
msgid "Chart"
msgstr "Graphique"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Type de graphique"

#: frontend/src/components/Modals/ConvertToDealModal.vue:43
#: frontend/src/components/Modals/ConvertToDealModal.vue:69
#: frontend/src/pages/MobileLead.vue:124 frontend/src/pages/MobileLead.vue:151
msgid "Choose Existing"
msgstr ""

#: frontend/src/components/Modals/DealModal.vue:45
msgid "Choose Existing Contact"
msgstr ""

#: frontend/src/components/Modals/DealModal.vue:38
msgid "Choose Existing Organization"
msgstr ""

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr ""

#: frontend/src/components/Controls/Link.vue:62
msgid "Clear"
msgstr "Nettoyer"

#: frontend/src/components/ListBulkActions.vue:134
#: frontend/src/components/ListBulkActions.vue:142
#: frontend/src/components/ListBulkActions.vue:188
msgid "Clear Assignment"
msgstr ""

#: frontend/src/components/SortBy.vue:160
msgid "Clear Sort"
msgstr ""

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Effacer le tableau"

#: frontend/src/components/Filter.vue:18 frontend/src/components/Filter.vue:150
msgid "Clear all Filter"
msgstr ""

#: frontend/src/components/Notifications.vue:28
msgid "Close"
msgstr "Fermer"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Réduire"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Collapsible"
msgstr "Réductible"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Couleur"

#: frontend/src/components/FieldLayoutEditor.vue:423
msgid "Column"
msgstr ""

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:15
msgid "Column Field"
msgstr ""

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Colonnes"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:80
#: frontend/src/components/CommunicationArea.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:574
msgid "Comment"
msgstr "Commentaire"

#: frontend/src/pages/Deal.vue:550 frontend/src/pages/Lead.vue:416
#: frontend/src/pages/MobileDeal.vue:442 frontend/src/pages/MobileLead.vue:349
msgid "Comments"
msgstr "Commentaires"

#: crm/api/dashboard.py:884
msgid "Common reasons for losing deals"
msgstr ""

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr ""

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Terminé"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Ordinateur"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Conditions"

#: frontend/src/components/Settings/General/GeneralSettings.vue:8
msgid "Configure general settings for your CRM"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:70
msgid "Configure the exchange rate provider for your CRM"
msgstr ""

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Confirmer"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:250
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:253
msgid "Confirm Delete"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr ""

#: frontend/src/components/Settings/Users.vue:225
#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr ""

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr ""

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:547
#: frontend/src/components/Modals/ConvertToDealModal.vue:65
#: frontend/src/pages/MobileLead.vue:147
msgid "Contact"
msgstr "Contact"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:212
msgid "Contact Already Exists"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr ""

#: frontend/src/pages/Deal.vue:655 frontend/src/pages/MobileDeal.vue:546
msgid "Contact added"
msgstr ""

#: frontend/src/pages/Deal.vue:645 frontend/src/pages/MobileDeal.vue:536
msgid "Contact already added"
msgstr ""

#: crm/fcrm/doctype/crm_lead/crm_lead.py:211
msgid "Contact already exists with {0}"
msgstr ""

#: frontend/src/pages/Contact.vue:282 frontend/src/pages/MobileContact.vue:255
msgid "Contact image updated"
msgstr ""

#: frontend/src/pages/Deal.vue:666 frontend/src/pages/MobileDeal.vue:557
msgid "Contact removed"
msgstr ""

#: frontend/src/pages/Contact.vue:437 frontend/src/pages/Contact.vue:450
#: frontend/src/pages/Contact.vue:463 frontend/src/pages/Contact.vue:473
#: frontend/src/pages/MobileContact.vue:435
#: frontend/src/pages/MobileContact.vue:448
#: frontend/src/pages/MobileContact.vue:461
#: frontend/src/pages/MobileContact.vue:471
msgid "Contact updated"
msgstr ""

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:237 frontend/src/pages/MobileContact.vue:215
#: frontend/src/pages/MobileOrganization.vue:334
msgid "Contacts"
msgstr "Contacts"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:34
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:105
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:105
msgid "Content"
msgstr "Contenu"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Content Type"
msgstr "Type de Contenu"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:167
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:165
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:169
msgid "Content is required"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:375
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:8
#: frontend/src/pages/MobileLead.vue:56 frontend/src/pages/MobileLead.vue:110
msgid "Convert"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:366
#: frontend/src/components/Layouts/AppSidebar.vue:374
msgid "Convert lead to deal"
msgstr ""

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:195
#: frontend/src/components/Modals/ConvertToDealModal.vue:19
#: frontend/src/pages/Lead.vue:45 frontend/src/pages/MobileLead.vue:106
msgid "Convert to Deal"
msgstr ""

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Converti"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr ""

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Copié dans le presse-papier"

#: crm/api/dashboard.py:607 crm/api/dashboard.py:736 crm/api/dashboard.py:794
#: crm/api/dashboard.py:891
msgid "Count"
msgstr "Compter"

#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/ContactModal.vue:41
#: frontend/src/components/Modals/CreateDocumentModal.vue:93
#: frontend/src/components/Modals/DealModal.vue:67
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/LeadModal.vue:38
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/OrganizationModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Modals/ViewModal.vue:16
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/pages/CallLogs.vue:11 frontend/src/pages/Contacts.vue:13
#: frontend/src/pages/Contacts.vue:60 frontend/src/pages/Deals.vue:13
#: frontend/src/pages/Deals.vue:236 frontend/src/pages/Leads.vue:13
#: frontend/src/pages/Leads.vue:262 frontend/src/pages/Notes.vue:7
#: frontend/src/pages/Notes.vue:93 frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:11
#: frontend/src/pages/Tasks.vue:185
msgid "Create"
msgstr "Créer"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr ""

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Créer un Lead"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/SidePanelLayout.vue:137
msgid "Create New"
msgstr "Créer Nouveau(elle)"

#: frontend/src/components/Activities/Activities.vue:384
#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Create Note"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:399
#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Create Task"
msgstr ""

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr ""

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:152
msgid "Create lead"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:344
msgid "Create your first lead"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:414
msgid "Create your first note"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:394
msgid "Create your first task"
msgstr ""

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:38
msgid "Currency"
msgstr "Devise"

#: frontend/src/components/Settings/General/CurrencySettings.vue:9
msgid "Currency & Exchange rate provider"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:188
msgid "Currency set as {0} successfully"
msgstr ""

#: crm/api/dashboard.py:839
msgid "Current pipeline distribution"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:586
msgid "Custom actions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:536
msgid "Custom branding"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:585
msgid "Custom fields"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:588
msgid "Custom list actions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:587
msgid "Custom statuses"
msgstr ""

#: frontend/src/pages/Deal.vue:486
msgid "Customer created successfully"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:582
msgid "Customization"
msgstr "Personnalisation"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr ""

#: crm/api/dashboard.py:599
msgid "Daily performance of leads, deals, and wins"
msgstr ""

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:575
#: frontend/src/pages/Deal.vue:555 frontend/src/pages/Lead.vue:421
#: frontend/src/pages/MobileDeal.vue:447 frontend/src/pages/MobileLead.vue:354
msgid "Data"
msgstr "Données"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr ""

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:601 crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "Date"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:546
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:62
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:62
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:129
msgid "Deal"
msgstr ""

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Resp. de l'opportunité"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr ""

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr ""

#: crm/api/dashboard.py:977
msgid "Deal generation channel analysis"
msgstr ""

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:531
#: frontend/src/pages/MobileOrganization.vue:475
#: frontend/src/pages/Organization.vue:484
msgid "Deal owner"
msgstr ""

#: crm/api/dashboard.py:1030 crm/api/dashboard.py:1087
msgid "Deal value"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:500 frontend/src/pages/MobileContact.vue:291
#: frontend/src/pages/MobileDeal.vue:386
#: frontend/src/pages/MobileOrganization.vue:328
msgid "Deals"
msgstr ""

#: crm/api/dashboard.py:788
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr ""

#: crm/api/dashboard.py:1076
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr ""

#: crm/api/dashboard.py:976
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr ""

#: crm/api/dashboard.py:838
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr ""

#: crm/api/dashboard.py:1019
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:115
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:115
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr ""

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Par Défaut"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Boîte de Réception par Défaut"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Entrant par Défaut"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Sortant par Défaut"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Priorité par défaut"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Envoi par Défaut"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Boîte d’Envois et de Réception par Défaut"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:142
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:316
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:89
#: frontend/src/components/Kanban/KanbanView.vue:225
#: frontend/src/components/ListBulkActions.vue:177
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:235
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:238
#: frontend/src/components/ViewControls.vue:1161
#: frontend/src/components/ViewControls.vue:1172
#: frontend/src/pages/Contact.vue:103 frontend/src/pages/Deal.vue:124
#: frontend/src/pages/Lead.vue:183 frontend/src/pages/MobileContact.vue:82
#: frontend/src/pages/MobileContact.vue:266
#: frontend/src/pages/MobileDeal.vue:517
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:267
#: frontend/src/pages/Notes.vue:40 frontend/src/pages/Organization.vue:83
#: frontend/src/pages/Tasks.vue:369
msgid "Delete"
msgstr "Supprimer"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr ""

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr ""

#: frontend/src/components/ViewControls.vue:1157
#: frontend/src/components/ViewControls.vue:1165
msgid "Delete View"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:65
msgid "Delete all"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:62
#: frontend/src/components/Activities/AttachmentArea.vue:138
msgid "Delete attachment"
msgstr ""

#: frontend/src/pages/MobileContact.vue:262
msgid "Delete contact"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:229
msgid "Delete linked item"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr ""

#: frontend/src/pages/MobileOrganization.vue:263
msgid "Delete organization"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete {0} item(s)"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr ""

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Modals/TaskModal.vue:49
msgid "Description"
msgstr "Description"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Bureau"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:286
#: frontend/src/pages/MobileDeal.vue:426 frontend/src/pages/MobileLead.vue:333
#: frontend/src/pages/MobileOrganization.vue:323
msgid "Details"
msgstr "Détails"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr ""

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Désactiver"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Desactivé"

#: frontend/src/components/CommentBox.vue:76
#: frontend/src/components/EmailEditor.vue:158
msgid "Discard"
msgstr "Ignorer"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Remise %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Remise"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr ""

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "Type de Document"

#: frontend/src/data/document.js:28
msgid "Document does not exist"
msgstr ""

#: crm/api/activities.py:19
msgid "Document not found"
msgstr ""

#: frontend/src/data/document.js:42
msgid "Document updated successfully"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr ""

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Télécharger"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr ""

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr ""

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Date d'Échéance"

#: frontend/src/components/Modals/ViewModal.vue:15
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:225
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:228
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/components/ViewControls.vue:1113
msgid "Duplicate"
msgstr "Dupliquer"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr ""

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Durée"

#: frontend/src/components/Layouts/AppSidebar.vue:599
#: frontend/src/components/Settings/Settings.vue:135
msgid "ERPNext"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr ""

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr ""

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr ""

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr ""

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:319
#: frontend/src/components/FieldLayoutEditor.vue:345
#: frontend/src/components/ListBulkActions.vue:170
#: frontend/src/components/ViewControls.vue:1131
#: frontend/src/pages/Dashboard.vue:19
msgid "Edit"
msgstr "modifier"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "Edit Call Log"
msgstr ""

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr ""

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr ""

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr ""

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr ""

#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Edit Note"
msgstr ""

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr ""

#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Edit Task"
msgstr ""

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr ""

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr ""

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:523
#: frontend/src/pages/MobileContact.vue:521
#: frontend/src/pages/MobileOrganization.vue:465
#: frontend/src/pages/MobileOrganization.vue:493
#: frontend/src/pages/Organization.vue:474
#: frontend/src/pages/Organization.vue:502
msgid "Email"
msgstr "Courriel"

#: frontend/src/components/Settings/Settings.vue:107
msgid "Email Accounts"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr ""

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:113
msgid "Email Templates"
msgstr "Modèles d’Email"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr ""

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr ""

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:573
msgid "Email communication"
msgstr ""

#: frontend/src/components/EmailEditor.vue:206
msgid "Email from Lead"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:552
msgid "Email template"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr ""

#: frontend/src/pages/Deal.vue:545 frontend/src/pages/Lead.vue:411
#: frontend/src/pages/MobileDeal.vue:437 frontend/src/pages/MobileLead.vue:344
msgid "Emails"
msgstr ""

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Vide"

#: frontend/src/components/Filter.vue:124
msgid "Empty - Choose a field to filter by"
msgstr ""

#: frontend/src/components/SortBy.vue:134
msgid "Empty - Choose a field to sort by"
msgstr ""

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Activer"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Activer Entrant"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Activer Sortant"

#: frontend/src/components/Settings/General/GeneralSettings.vue:19
msgid "Enable forecasting"
msgstr ""

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:33
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:33
msgid "Enabled"
msgstr "Activé"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Date de Fin"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "End Time"
msgstr "Heure de Fin"

#: frontend/src/components/Settings/General/CurrencySettings.vue:122
msgid "Enter access key"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:334
msgid "Enter {0}"
msgstr "Entrez {0}"

#: frontend/src/components/Filter.vue:67 frontend/src/components/Filter.vue:100
#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:310
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:332
#: frontend/src/components/Filter.vue:348
msgid "Equals"
msgstr "Égal à"

#: frontend/src/components/Modals/ConvertToDealModal.vue:185
msgid "Error converting to deal: {0}"
msgstr ""

#: frontend/src/pages/Deal.vue:739 frontend/src/pages/Lead.vue:486
#: frontend/src/pages/MobileDeal.vue:614 frontend/src/pages/MobileLead.vue:415
msgid "Error updating field"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:261
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:173
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr ""

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr ""

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Taux de Change"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:67
msgid "Exchange rate provider"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:597
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr ""

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr ""

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr ""

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr ""

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Développer"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:161
msgid "Expected Closure Date is required."
msgstr ""

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:159
msgid "Expected Deal Value is required."
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Expiré"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Exporter"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr ""

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Type d'Exportation"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Échoué"

#: frontend/src/components/Modals/AddExistingUserModal.vue:109
msgid "Failed to add users"
msgstr ""

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr ""

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:182
msgid "Failed to create template"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:217
msgid "Failed to delete template"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr ""

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:246
msgid "Failed to rename template"
msgstr ""

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr ""

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:212
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:202
msgid "Failed to update template"
msgstr ""

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:81
msgid "Favicon"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Champ"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:51
msgid "Fields Order"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr ""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Filtre"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Filtres"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:112
#: frontend/src/components/Filter.vue:58 frontend/src/components/Filter.vue:89
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:106
#: frontend/src/components/SortBy.vue:140
msgid "First Name"
msgstr "Prénom"

#: frontend/src/components/Modals/LeadModal.vue:135
msgid "First Name is mandatory"
msgstr ""

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Première Réponse Le"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr ""

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr ""

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Temps de première réponse"

#: frontend/src/components/Filter.vue:131
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
msgid "For"
msgstr "Pour"

#: crm/api/dashboard.py:666
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr ""

#: frontend/src/components/Settings/General/GeneralSettings.vue:100
msgid "Forecasting disabled successfully"
msgstr ""

#: frontend/src/components/Settings/General/GeneralSettings.vue:99
msgid "Forecasting enabled successfully"
msgstr ""

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr ""

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:603
msgid "Frappe CRM mobile"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Vendredi"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "À partir de"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "A partir du"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr ""

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "De l&#39;utilisateur"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Complété"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Nom Complet"

#: crm/api/dashboard.py:728
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr ""

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Sexe"

#: frontend/src/components/Settings/General/GeneralSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:89
msgid "General"
msgstr "Général"

#: crm/api/dashboard.py:1020
msgid "Geographic distribution of deals and revenue"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr ""

#: frontend/src/pages/Deal.vue:104 frontend/src/pages/Lead.vue:159
msgid "Go to website"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr ""

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Grouper par"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr ""

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Grouper par: "

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Aidez-moi"

#: frontend/src/components/CommunicationArea.vue:62
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr ""

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Caché"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Hide"
msgstr "Cacher"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr ""

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Hide Recording"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Hide border"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Hide label"
msgstr ""

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr ""

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Haut"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Liste de vacances"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Nom de la Liste de Vacances"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Jours Fériés"

#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/General/HomeActions.vue:9
msgid "Home actions"
msgstr ""

#. Label of the id (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "ID"
msgstr ""

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Icône"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr ""

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Image"

#: frontend/src/components/Filter.vue:276
#: frontend/src/components/Filter.vue:297
#: frontend/src/components/Filter.vue:312
#: frontend/src/components/Filter.vue:325
#: frontend/src/components/Filter.vue:339
msgid "In"
msgstr "Dans"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "En cours"

#: frontend/src/components/SLASection.vue:75
msgid "In less than a minute"
msgstr ""

#: frontend/src/components/Activities/CallArea.vue:35
msgid "Inbound Call"
msgstr ""

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Boîte de réception"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Entrant"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr ""

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Industrie"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Initié"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Integration"
msgstr ""

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:120
msgctxt "FCRM"
msgid "Integrations"
msgstr "Intégrations"

#: frontend/src/components/Layouts/AppSidebar.vue:524
#: frontend/src/components/Layouts/AppSidebar.vue:527
msgid "Introduction"
msgstr ""

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr ""

#: frontend/src/components/Modals/DealModal.vue:213
#: frontend/src/components/Modals/LeadModal.vue:154
msgid "Invalid Email"
msgstr ""

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr ""

#: crm/api/dashboard.py:73
msgid "Invalid chart name"
msgstr ""

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr ""

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:101
msgid "Invite User"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:56
msgid "Invite as"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:29
msgid "Invite by email"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:538
msgid "Invite users"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:354
msgid "Invite your team"
msgstr ""

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr ""

#: frontend/src/components/Filter.vue:278
#: frontend/src/components/Filter.vue:287
#: frontend/src/components/Filter.vue:299
#: frontend/src/components/Filter.vue:314
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:341
#: frontend/src/components/Filter.vue:350
msgid "Is"
msgstr "Est"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Est Défaut"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr ""

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Est un Groupe"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Est primaire"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Est Standard"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr ""

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr ""

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Titre de l'Emploi"

#: frontend/src/components/Filter.vue:75 frontend/src/components/Filter.vue:108
#: frontend/src/components/Modals/AssignmentModal.vue:35
#: frontend/src/components/Modals/TaskModal.vue:76
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr ""

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Kanban"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr ""

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr ""

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:11
msgid "Kanban Settings"
msgstr ""

#. Label of the key (Data) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Key"
msgstr "Clé"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:109
msgid "Label"
msgstr "Étiquette"

#: frontend/src/components/Filter.vue:620
msgid "Last 6 Months"
msgstr ""

#: frontend/src/components/Filter.vue:612
msgid "Last Month"
msgstr "Le mois dernier"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Nom de Famille"

#: frontend/src/components/Filter.vue:616
msgid "Last Quarter"
msgstr "Le dernier quart"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr ""

#: frontend/src/components/Filter.vue:608
msgid "Last Week"
msgstr "La semaine dernière"

#: frontend/src/components/Filter.vue:624
msgid "Last Year"
msgstr "L&#39;année dernière"

#: frontend/src/pages/Contact.vue:538 frontend/src/pages/MobileContact.vue:536
#: frontend/src/pages/MobileOrganization.vue:480
#: frontend/src/pages/MobileOrganization.vue:508
#: frontend/src/pages/Organization.vue:489
#: frontend/src/pages/Organization.vue:517
msgid "Last modified"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr ""

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr ""

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:545
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:58
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:58
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:130
msgid "Lead"
msgstr "Lead"

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Détails du Lead"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Nom du Lead"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Responsable du Prospect"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:73
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr ""

#: crm/api/dashboard.py:935
msgid "Lead generation channel analysis"
msgstr ""

#: crm/api/dashboard.py:729
msgid "Lead to deal conversion pipeline"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Lead.vue:369 frontend/src/pages/MobileLead.vue:293
msgid "Leads"
msgstr ""

#: crm/api/dashboard.py:934
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr ""

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Parti"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr ""

#: frontend/src/components/Filter.vue:274
#: frontend/src/components/Filter.vue:285
#: frontend/src/components/Filter.vue:295
#: frontend/src/components/Filter.vue:323
#: frontend/src/components/Filter.vue:337
msgid "Like"
msgstr "Comme"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Lien"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Liens"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Liste"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Listen"
msgstr ""

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr ""

#: frontend/src/components/Kanban/KanbanView.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Charger plus"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:37
#: frontend/src/pages/Deal.vue:193 frontend/src/pages/MobileDeal.vue:119
msgid "Loading..."
msgstr "Chargement en Cours ..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Journal"

#: frontend/src/components/Activities/Activities.vue:803
#: frontend/src/components/Activities/ActivityHeader.vue:137
#: frontend/src/components/Activities/ActivityHeader.vue:180
msgid "Log a Call"
msgstr ""

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Se connecter à Frappe Cloud ?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:47
msgid "Logo"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Perdu"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr ""

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Raison de la Perte"

#: crm/api/dashboard.py:883
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr ""

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Bas"

#: frontend/src/pages/Contact.vue:94 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr ""

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Private"
msgstr ""

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Public"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:807
#: frontend/src/components/Activities/ActivityHeader.vue:142
#: frontend/src/components/Activities/ActivityHeader.vue:185
#: frontend/src/pages/Deals.vue:504 frontend/src/pages/Leads.vue:531
msgid "Make a Call"
msgstr ""

#: frontend/src/pages/Deal.vue:86 frontend/src/pages/Lead.vue:128
msgid "Make a call"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:109
msgid "Make attachment {0}"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:118
msgid "Make {0}"
msgstr "Faire {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr ""

#: frontend/src/components/Settings/General/GeneralSettings.vue:23
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr ""

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr ""

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:91
#: frontend/src/components/Settings/InviteUserPage.vue:171
#: frontend/src/components/Settings/InviteUserPage.vue:178
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:185
#: frontend/src/components/Settings/Users.vue:256
#: frontend/src/components/Settings/Users.vue:259
msgid "Manager"
msgstr ""

#: frontend/src/data/document.js:54
msgid "Mandatory field error: {0}"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Manuel"

#: frontend/src/components/Notifications.vue:19
#: frontend/src/pages/MobileNotification.vue:11
#: frontend/src/pages/MobileNotification.vue:14
msgid "Mark all as read"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:542
msgid "Masters"
msgstr "Données de Base"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Moyen"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr ""

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Message"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Deuxième Nom"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "N° Mobile"

#: frontend/src/components/Modals/DealModal.vue:209
#: frontend/src/components/Modals/LeadModal.vue:150
msgid "Mobile No should be a number"
msgstr ""

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "N° Mobile."

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr ""

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:606
msgid "Mobile app installation"
msgstr ""

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:526
#: frontend/src/pages/MobileOrganization.vue:470
#: frontend/src/pages/Organization.vue:479
msgid "Mobile no"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Lundi"

#: crm/api/dashboard.py:669
msgid "Month"
msgstr "Mois"

#: frontend/src/components/FieldLayoutEditor.vue:454
msgid "Move to next section"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:407
msgid "Move to next tab"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:464
msgid "Move to previous section"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:393
msgid "Move to previous tab"
msgstr ""

#: frontend/src/components/Modals/ViewModal.vue:40
msgid "My Open Deals"
msgstr ""

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:42
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:42
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:488
#: frontend/src/pages/Organization.vue:497
msgid "Name"
msgstr "Nom"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:157
msgid "Name is required"
msgstr ""

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Masque de numérotation"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Montant Net"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Total net"

#: frontend/src/components/Activities/ActivityHeader.vue:82
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Nouveau"

#: frontend/src/components/Modals/AddressModal.vue:94
msgid "New Address"
msgstr ""

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "New Call Log"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:394
#: frontend/src/components/Activities/ActivityHeader.vue:27
#: frontend/src/components/Activities/ActivityHeader.vue:132
msgid "New Comment"
msgstr ""

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:389
#: frontend/src/components/Activities/ActivityHeader.vue:17
#: frontend/src/components/Activities/ActivityHeader.vue:127
msgid "New Email"
msgstr "nouveau courriel"

#: frontend/src/components/Activities/ActivityHeader.vue:73
msgid "New Message"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:42
#: frontend/src/components/Activities/ActivityHeader.vue:148
#: frontend/src/pages/Deals.vue:510 frontend/src/pages/Leads.vue:537
msgid "New Note"
msgstr ""

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Nouveau Mot de Passe"

#: frontend/src/components/FieldLayoutEditor.vue:203
#: frontend/src/components/SidePanelLayoutEditor.vue:133
msgid "New Section"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:299
#: frontend/src/components/FieldLayoutEditor.vue:304
msgid "New Tab"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:52
#: frontend/src/components/Activities/ActivityHeader.vue:153
#: frontend/src/pages/Deals.vue:515 frontend/src/pages/Leads.vue:542
msgid "New Task"
msgstr "Nv. Tâche à faire"

#: frontend/src/components/Activities/ActivityHeader.vue:163
msgid "New WhatsApp Message"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:81
#: frontend/src/pages/MobileLead.vue:164
msgid "New contact will be created based on the person's details"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:56
#: frontend/src/pages/MobileLead.vue:138
msgid "New organization will be created based on the data in details section"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr ""

#: frontend/src/components/Modals/CreateDocumentModal.vue:89
msgid "New {0}"
msgstr "Nouveau(elle) {0}"

#: frontend/src/components/Filter.vue:668
msgid "Next 6 Months"
msgstr ""

#: frontend/src/components/Filter.vue:660
msgid "Next Month"
msgstr ""

#: frontend/src/components/Filter.vue:664
msgid "Next Quarter"
msgstr ""

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr ""

#: frontend/src/components/Filter.vue:656
msgid "Next Week"
msgstr ""

#: frontend/src/components/Filter.vue:672
msgid "Next Year"
msgstr ""

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Non"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr ""

#: frontend/src/components/Controls/Grid.vue:309
msgid "No Data"
msgstr "Aucune Donnée"

#: frontend/src/components/Kanban/KanbanView.vue:102
#: frontend/src/pages/Deals.vue:106 frontend/src/pages/Leads.vue:122
#: frontend/src/pages/Tasks.vue:68
msgid "No Title"
msgstr ""

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr ""

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:282 frontend/src/pages/MobileDeal.vue:207
msgid "No contacts added"
msgstr ""

#: frontend/src/pages/Deal.vue:97 frontend/src/pages/Lead.vue:150
msgid "No email set"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr ""

#: frontend/src/pages/Deal.vue:705
msgid "No mobile number set"
msgstr ""

#: frontend/src/components/Notifications.vue:83
#: frontend/src/pages/MobileNotification.vue:67
msgid "No new notifications"
msgstr ""

#: frontend/src/pages/Lead.vue:135
msgid "No phone number set"
msgstr ""

#: frontend/src/pages/Deal.vue:700
msgid "No primary contact set"
msgstr ""

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:72
#: frontend/src/components/Controls/MultiSelectUserInput.vue:72
msgid "No results found"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr ""

#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr ""

#: frontend/src/pages/MobileOrganization.vue:284
#: frontend/src/pages/Organization.vue:300
msgid "No website found"
msgstr ""

#: frontend/src/pages/Deal.vue:110 frontend/src/pages/Lead.vue:165
msgid "No website set"
msgstr ""

#: frontend/src/components/SidePanelLayout.vue:128
msgid "No {0} Available"
msgstr ""

#: frontend/src/pages/CallLogs.vue:56 frontend/src/pages/Contact.vue:159
#: frontend/src/pages/Contacts.vue:59 frontend/src/pages/Deals.vue:235
#: frontend/src/pages/Leads.vue:261 frontend/src/pages/MobileContact.vue:150
#: frontend/src/pages/MobileOrganization.vue:142
#: frontend/src/pages/Notes.vue:92 frontend/src/pages/Organization.vue:156
#: frontend/src/pages/Organizations.vue:59 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr ""

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Nb de salarié(e)s"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr ""

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Non Autorisé"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:311
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:349
msgid "Not Equals"
msgstr "Non égaux"

#: frontend/src/components/Filter.vue:277
#: frontend/src/components/Filter.vue:298
#: frontend/src/components/Filter.vue:313
#: frontend/src/components/Filter.vue:326
#: frontend/src/components/Filter.vue:340
msgid "Not In"
msgstr "Non Inclus"

#: frontend/src/components/Filter.vue:275
#: frontend/src/components/Filter.vue:286
#: frontend/src/components/Filter.vue:296
#: frontend/src/components/Filter.vue:324
#: frontend/src/components/Filter.vue:338
msgid "Not Like"
msgstr "Pas Comme"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/General/BrandSettings.vue:16
#: frontend/src/components/Settings/General/CurrencySettings.vue:16
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Non Sauvegardé"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:260
msgid "Not allowed to add contact to Deal"
msgstr ""

#: crm/fcrm/doctype/crm_lead/crm_lead.py:408
msgid "Not allowed to convert Lead to Deal"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:271
msgid "Not allowed to remove contact from Deal"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:282
msgid "Not allowed to set primary contact for Deal"
msgstr ""

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:549
msgid "Note"
msgstr "Note"

#: frontend/src/pages/Deal.vue:570 frontend/src/pages/Lead.vue:436
#: frontend/src/pages/MobileDeal.vue:463 frontend/src/pages/MobileLead.vue:370
msgid "Notes"
msgstr "Remarques"

#: frontend/src/pages/Notes.vue:20
msgid "Notes View"
msgstr ""

#: frontend/src/components/Activities/EmailArea.vue:13
#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Notification"
msgstr ""

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr ""

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr ""

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr ""

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Nombre"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr ""

#: crm/api/dashboard.py:1027 crm/api/dashboard.py:1084
msgid "Number of deals"
msgstr ""

#: crm/api/dashboard.py:1077
msgid "Number of deals and total value per salesperson"
msgstr ""

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Grand Parent"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "En attente"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr ""

#: crm/api/dashboard.py:181
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr ""

#: frontend/src/utils/index.js:444
msgid "Only image files are allowed"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Un seul {0} peut être défini comme primaire."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Ouvert"

#: frontend/src/components/Modals/NoteModal.vue:18
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Open Deal"
msgstr ""

#: frontend/src/components/Modals/NoteModal.vue:19
#: frontend/src/components/Modals/TaskModal.vue:26
msgid "Open Lead"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr ""

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr ""

#: frontend/src/pages/Organization.vue:92
msgid "Open website"
msgstr ""

#: frontend/src/components/Kanban/KanbanView.vue:221
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr ""

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr ""

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Commandé par"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:548
#: frontend/src/components/Modals/ConvertToDealModal.vue:39
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:505
#: frontend/src/pages/MobileLead.vue:120
#: frontend/src/pages/MobileOrganization.vue:449
#: frontend/src/pages/MobileOrganization.vue:503
#: frontend/src/pages/Organization.vue:458
#: frontend/src/pages/Organization.vue:512
msgid "Organization"
msgstr "Organisation"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr ""

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr ""

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Nom de l'Organisation"

#: frontend/src/pages/Deal.vue:69
msgid "Organization logo"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:208
#: frontend/src/pages/Organization.vue:238
msgid "Organizations"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Other features"
msgstr ""

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Autres"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Outbound Call"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Sortant"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Responsable"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Mot de Passe"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:41
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:41
msgid "Payment Reminder"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:72
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:72
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "En Attente"

#: frontend/src/components/Settings/InviteUserPage.vue:66
msgid "Pending Invites"
msgstr ""

#: frontend/src/pages/Dashboard.vue:79
msgid "Period"
msgstr "Période"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr ""

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:498
#: frontend/src/pages/Organization.vue:507
msgid "Phone"
msgstr "Téléphone"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr ""

#: frontend/src/components/ViewControls.vue:1138
msgid "Pin View"
msgstr ""

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr ""

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Pinned view"
msgstr ""

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr ""

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:168
msgid "Please enter a valid URL"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:159
msgid "Please enter the Exchangerate Host access key."
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:152
msgid "Please select a currency before saving."
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:145
#: frontend/src/pages/MobileLead.vue:434
msgid "Please select an existing contact"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:150
#: frontend/src/pages/MobileLead.vue:439
msgid "Please select an existing organization"
msgstr ""

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:169
msgid "Please specify a reason for losing the deal."
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Please specify the reason for losing the deal."
msgstr ""

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr ""

#: frontend/src/pages/Deal.vue:222 frontend/src/pages/MobileDeal.vue:151
msgid "Primary"
msgstr "Primaire"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr ""

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr ""

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr ""

#: frontend/src/pages/Deal.vue:677 frontend/src/pages/MobileDeal.vue:568
msgid "Primary contact set"
msgstr ""

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Les priorités"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Priority"
msgstr "Priorité"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Privé"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Probabilité"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Produit"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr ""

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr ""

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Produits"

#: frontend/src/components/Layouts/AppSidebar.vue:535
#: frontend/src/components/Settings/Settings.vue:79
msgid "Profile"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr ""

#: crm/api/dashboard.py:667
msgid "Projected vs actual revenue based on deal probability"
msgstr ""

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr ""

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:565
msgid "Public view"
msgstr ""

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Quantité"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "Dans la file d'attente"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Écriture rapide"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Filtres rapides"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:589
msgid "Quick entry layout"
msgstr ""

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Prix"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Lire"

#: crm/api/dashboard.py:886
msgid "Reason"
msgstr "Raison"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr ""

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr ""

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "URL d'enregistrement"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr ""

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "DocType de la Référence"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Type du document de référence"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Nom de référence"

#: frontend/src/components/ViewControls.vue:25
#: frontend/src/components/ViewControls.vue:160
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Actualiser"

#: frontend/src/components/Telephony/TwilioCallUI.vue:104
msgid "Reject"
msgstr ""

#: frontend/src/components/Settings/Users.vue:210
#: frontend/src/components/Settings/Users.vue:213
#: frontend/src/pages/Deal.vue:626
msgid "Remove"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:444
msgid "Remove and move fields to previous column"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove column"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:101
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:47
msgid "Remove image"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:365
msgid "Remove section"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:324
msgid "Remove tab"
msgstr ""

#: frontend/src/components/Activities/EmailArea.vue:31
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Répondre"

#: frontend/src/components/Activities/EmailArea.vue:44
msgid "Reply All"
msgstr "Répondre à Tous"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Signaler un problème"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:82
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Réinitialiser"

#: frontend/src/components/ColumnSettings.vue:82
msgid "Reset Changes"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:93
msgid "Reset to Default"
msgstr ""

#: frontend/src/pages/Dashboard.vue:34
msgid "Reset to default"
msgstr ""

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Réponse de"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Détails de la réponse"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Restaurer"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:54
msgid "Retake"
msgstr ""

#: crm/api/dashboard.py:675
msgid "Revenue"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:84
msgid "Rich Text"
msgstr ""

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Droit"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Sonnerie"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Ringing..."
msgstr ""

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:44
msgid "Role"
msgstr "Rôle"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr ""

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr ""

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr ""

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr ""

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr ""

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr ""

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr ""

#: frontend/src/components/EmailEditor.vue:82
msgid "SUBJECT"
msgstr ""

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Sales Manager"
msgstr "Responsable des Ventes"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:90
#: frontend/src/components/Settings/InviteUserPage.vue:170
#: frontend/src/components/Settings/InviteUserPage.vue:177
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:186
#: frontend/src/components/Settings/Users.vue:268
#: frontend/src/components/Settings/Users.vue:271
msgid "Sales User"
msgstr "Chargé de Ventes"

#: crm/api/dashboard.py:598
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr ""

#: frontend/src/pages/Dashboard.vue:106
msgid "Sales user"
msgstr ""

#: crm/api/dashboard.py:1079
msgid "Salesperson"
msgstr ""

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Civilité"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Samedi"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:87
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/DropdownItem.vue:21
#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/Settings/General/CurrencySettings.vue:182
#: frontend/src/components/Telephony/ExotelCallUI.vue:231
#: frontend/src/components/ViewControls.vue:123
#: frontend/src/pages/Dashboard.vue:45
msgid "Save"
msgstr "Sauvegarder"

#: frontend/src/components/Modals/ViewModal.vue:13
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:157
msgid "Save Changes"
msgstr ""

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:564
msgid "Saved view"
msgstr ""

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr ""

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr ""

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:342
msgid "Section"
msgstr "Section"

#: frontend/src/pages/Dashboard.vue:59
msgid "Select Range"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:58
msgid "Select currency"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:82
msgid "Select provider"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:332
msgid "Select {0}"
msgstr "Sélectionner {0}"

#: frontend/src/components/EmailEditor.vue:162
msgid "Send"
msgstr "Envoyer"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "Send Template"
msgstr ""

#: frontend/src/pages/Deal.vue:93 frontend/src/pages/Lead.vue:144
msgid "Send an email"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:455
msgid "Send email"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Séparateur"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Séries"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:576
msgid "Service level agreement"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:69
msgid "Set all as private"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:62
msgid "Set all as public"
msgstr ""

#: frontend/src/pages/Deal.vue:80
msgid "Set an organization"
msgstr ""

#: frontend/src/pages/Deal.vue:634 frontend/src/pages/MobileDeal.vue:525
msgid "Set as Primary Contact"
msgstr ""

#: frontend/src/components/ViewControls.vue:1123
msgid "Set as default"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:173
msgid "Set currency"
msgstr ""

#: frontend/src/pages/Lead.vue:122
msgid "Set first name"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:528
msgid "Setting up"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:532
#: frontend/src/components/Settings/Settings.vue:11
#: frontend/src/components/Settings/Settings.vue:75
msgid "Settings"
msgstr "Paramètres"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:334
msgid "Setup your password"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:230
msgid "Show"
msgstr "Afficher"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Show border"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Show label"
msgstr ""

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Articles de la Barre Latérale"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr ""

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:22
#: frontend/src/components/SortBy.vue:240
msgid "Sort"
msgstr ""

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Modals/EditValueModal.vue:10
msgid "Source"
msgstr ""

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "Source Name"
msgstr "Nom de la Source"

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr ""

#: crm/api/dashboard.py:731 crm/api/dashboard.py:790
msgid "Stage"
msgstr "Etape"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr ""

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Prix de Vente Standard"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr ""

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Date de Début"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Start Time"
msgstr "Heure de Début"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr ""

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:516
#: frontend/src/pages/MobileOrganization.vue:460
#: frontend/src/pages/Organization.vue:469
msgid "Status"
msgstr "Statut"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr ""

#: frontend/src/components/Modals/DealModal.vue:217
#: frontend/src/components/Modals/LeadModal.vue:158
msgid "Status is required"
msgstr ""

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Sous-domaine"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:71
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:71
msgid "Subject"
msgstr "Sujet"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:161
msgid "Subject is required"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Dimanche"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:49
msgid "Switch camera"
msgstr ""

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr ""

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "System Manager"
msgstr "Responsable Système"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr ""

#: frontend/src/components/Telephony/ExotelCallUI.vue:151
msgid "Take a note..."
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:550
msgid "Task"
msgstr "Tâche"

#: frontend/src/pages/Deal.vue:565 frontend/src/pages/Lead.vue:431
#: frontend/src/pages/MobileDeal.vue:458 frontend/src/pages/MobileLead.vue:365
msgid "Tasks"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:123
msgid "Telephony"
msgstr ""

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:178
msgid "Template created successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:214
msgid "Template deleted successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:198
msgid "Template disabled successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template enabled successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:243
msgid "Template renamed successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
msgid "Template updated successfully"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr ""

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1022 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Région"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Responsable Régional"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Nom de la Région"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr ""

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr ""

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr ""

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr ""

#: frontend/src/components/Modals/AddressModal.vue:129
#: frontend/src/components/Modals/CallLogModal.vue:132
msgid "These fields are required: {0}"
msgstr ""

#: frontend/src/components/Filter.vue:644
msgid "This Month"
msgstr ""

#: frontend/src/components/Filter.vue:648
msgid "This Quarter"
msgstr ""

#: frontend/src/components/Filter.vue:640
msgid "This Week"
msgstr ""

#: frontend/src/components/Filter.vue:652
msgid "This Year"
msgstr ""

#: frontend/src/components/SidePanelLayoutEditor.vue:119
msgid "This section is not editable"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Jeudi"

#: frontend/src/components/Filter.vue:356
msgid "Timespan"
msgstr "Période"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:41
msgid "Title"
msgstr "Titre"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:32
msgid "Title Field"
msgstr "Champ Titre"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:63
msgid "To"
msgstr "À"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Jusqu'au"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr ""

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "À l&#39;utilisateur"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr ""

#: frontend/src/components/Filter.vue:632
msgid "Today"
msgstr "Aujourd'hui"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr ""

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr ""

#: frontend/src/components/Filter.vue:636
msgid "Tomorrow"
msgstr ""

#: frontend/src/components/Modals/NoteModal.vue:37
#: frontend/src/components/Modals/TaskModal.vue:59
msgid "Took a call with John Doe and discussed the new project."
msgstr ""

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr ""

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Total des vacances"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr ""

#: crm/api/dashboard.py:123
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr ""

#: crm/api/dashboard.py:124
msgid "Total number of leads"
msgstr ""

#: crm/api/dashboard.py:182
msgid "Total number of non won/lost deals"
msgstr ""

#: crm/api/dashboard.py:297
msgid "Total number of won deals based on its closure date"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Mardi"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:596
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr ""

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr ""

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr ""

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Type"
msgstr ""

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:73
#: frontend/src/components/Controls/MultiSelectUserInput.vue:73
msgid "Type an email address to invite"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr ""

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Uncollapsible"
msgstr ""

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:130
msgid "Unknown"
msgstr "Inconnu"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:77
msgid "Unlink all"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:242
msgid "Unlink linked item"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink {0} item(s)"
msgstr ""

#: frontend/src/components/ViewControls.vue:1138
msgid "Unpin View"
msgstr ""

#: frontend/src/components/ViewControls.vue:975
msgid "Unsaved Changes"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:138
#: frontend/src/components/Modals/AssignmentModal.vue:17
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:17
#: frontend/src/components/Settings/General/BrandSettings.vue:23
#: frontend/src/components/Settings/General/CurrencySettings.vue:23
#: frontend/src/components/Settings/General/HomeActions.vue:17
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:219
#: frontend/src/components/ViewControls.vue:980
msgid "Update"
msgstr "Mettre à Jour"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:86
msgid "Upload"
msgstr "Charger"

#: frontend/src/components/Activities/Activities.vue:404
#: frontend/src/components/Activities/ActivityHeader.vue:62
#: frontend/src/components/Activities/ActivityHeader.vue:158
msgid "Upload Attachment"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:42 frontend/src/pages/Lead.vue:96
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:42
msgid "Upload image"
msgstr ""

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Utilisateur"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Nom d&#39;utilisateur"

#: frontend/src/components/Settings/Users.vue:301
msgid "User {0} has been removed"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:95
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Utilisateurs"

#: frontend/src/components/Modals/AddExistingUserModal.vue:103
msgid "Users added successfully"
msgstr ""

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Validité"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Valeur"

#: frontend/src/components/Modals/ViewModal.vue:25
msgid "View Name"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Views"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:558
msgid "Web form"
msgstr ""

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr ""

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Site Web"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Mercredi"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Jours de Congé Hebdomadaire"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr ""

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:598
#: frontend/src/components/Settings/Settings.vue:129
#: frontend/src/pages/Deal.vue:580 frontend/src/pages/Lead.vue:446
#: frontend/src/pages/MobileDeal.vue:473 frontend/src/pages/MobileLead.vue:380
msgid "WhatsApp"
msgstr ""

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr ""

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "Where"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:117
msgid "Width"
msgstr "Largeur"

#: frontend/src/components/ColumnSettings.vue:122
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr ""

#: crm/api/dashboard.py:296
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr ""

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Journée de travail"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Heures de travail"

#: frontend/src/components/Filter.vue:628
msgid "Yesterday"
msgstr ""

#: crm/api/whatsapp.py:36 crm/api/whatsapp.py:216 crm/api/whatsapp.py:230
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Vous"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:107
msgid "You can get your access key from "
msgstr ""

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr ""

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr ""

#: frontend/src/data/document.js:32
msgid "You do not have permission to access this document"
msgstr ""

#: frontend/src/components/ViewControls.vue:976
msgid "You have unsaved changes. Do you want to save them?"
msgstr ""

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr ""

#: crm/api/todo.py:111
msgid "Your assignment on task {0} has been removed by {1}"
msgstr ""

#: crm/api/todo.py:46 crm/api/todo.py:89
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Votre devoir sur {0} {1} a été supprimé par {2}"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr ""

#: crm/api/todo.py:120
msgid "assigned a new task {0} to you"
msgstr ""

#: crm/api/todo.py:100
msgid "assigned a {0} {1} to you"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "noir"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "bleu"

#: frontend/src/components/Activities/Activities.vue:232
msgid "changes from"
msgstr ""

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "Commenter"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "cyan"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:268
#: frontend/src/components/Controls/MultiSelectUserInput.vue:242
msgid "email already exists"
msgstr ""

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:113
msgid "exchangerate.host"
msgstr ""

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "vert"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr ""

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr ""

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr ""

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr ""

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr ""

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr ""

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr ""

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr ""

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr ""

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr ""

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr ""

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr ""

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr ""

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr ""

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr ""

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr ""

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr ""

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr ""

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
#: frontend/src/components/Settings/InviteUserPage.vue:37
msgid "<EMAIL>"
msgstr ""

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "juste maintenant"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "kanban"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "liste"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:65
#: frontend/src/pages/MobileNotification.vue:52
msgid "mentioned you in {0}"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "next"
msgstr "suivant"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "maintenant"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "Orange"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "rose"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "previous"
msgstr "précedent"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "private"
msgstr "privé"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "public"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "violet"

#: crm/api/whatsapp.py:37
msgid "received a whatsapp message in {0}"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "rouge"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:274
#: frontend/src/components/Activities/Activities.vue:337
msgid "to"
msgstr "à"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "demain"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "Jaune"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "hier"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr ""

#: crm/api/todo.py:50
msgid "{0} assigned a {1} {2} to you"
msgstr ""

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} j"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "Il y a {0} jours"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr ""

#: frontend/src/components/Settings/Users.vue:291
msgid "{0} has been granted {1} access"
msgstr ""

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "Il y a {0} heures"

#: frontend/src/components/EmailEditor.vue:29
#: frontend/src/components/EmailEditor.vue:64
#: frontend/src/components/EmailEditor.vue:77
#: frontend/src/components/Modals/AddExistingUserModal.vue:36
#: frontend/src/components/Settings/InviteUserPage.vue:41
msgid "{0} is an invalid email address"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:181
msgid "{0} is required"
msgstr "{0} est nécessaire"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr ""

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "Il y a {0} minutes"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "Il y a {0} mois"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr ""

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "Il y a {0} semaines"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr ""

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "Il y a {0} ans"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr ""

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr ""

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr ""

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr ""

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr ""

