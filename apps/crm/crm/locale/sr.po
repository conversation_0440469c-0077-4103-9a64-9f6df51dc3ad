msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-08-03 09:38+0000\n"
"PO-Revision-Date: 2025-08-07 10:07\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Serbian (Cyrillic)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sr\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: sr_SP\n"

#: frontend/src/components/ViewControls.vue:1217
msgid " (New)"
msgstr " (Ново)"

#: frontend/src/components/Modals/TaskModal.vue:99
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "04.01.2024. 23:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "пре 1 сата"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "пре 1 минут"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "пре 1 месец"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "пре 1 недељу"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "пре 1 годину"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>МЕТА</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>ПРЕЧИЦЕ</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:98
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:98
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "<p>Здраво {{ lead_name }},</p>\\n\\n<p>Ово је подсетник за плаћање {{ grand_total }}.</p>\\n\\n<p>Хвала,</p>\\n<p>Frappé</p>"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>ПОРТАЛ</b></span>"

#: frontend/src/components/CommunicationArea.vue:85
msgid "@John, can you please check this?"
msgstr "@Џон, можеш ли молим те проверити ово?"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:56
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Потенцијални клијент захтева или име особе или назив организације"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Key"
msgstr "API кључ"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "API кључ је обавезан"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Secret"
msgstr "API тајна"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API токен"

#: frontend/src/components/Telephony/TwilioCallUI.vue:92
msgid "Accept"
msgstr "Прихвати"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "Прихвати позивницу"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Прихваћено"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "Прихваћено"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Кључ за приступ"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "Кључ за приступ је обавезан за пружаоца услуга: {0}"

#: frontend/src/components/Settings/General/CurrencySettings.vue:97
msgid "Access key"
msgstr "Кључ за приступ"

#: frontend/src/components/Settings/General/CurrencySettings.vue:101
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Кључ за приступ Exchangerate Host. Неопходан за преузимање девизних курсева."

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Назив налога"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "SID налога"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "Назив налога је обавезан"

#: frontend/src/components/CustomActions.vue:73
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1109
msgid "Actions"
msgstr "Радње"

#: frontend/src/pages/Deal.vue:540 frontend/src/pages/Lead.vue:406
#: frontend/src/pages/MobileDeal.vue:432 frontend/src/pages/MobileLead.vue:339
msgid "Activity"
msgstr "Активност"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:53
msgid "Add"
msgstr "Додај"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "Додај налог"

#: frontend/src/components/ColumnSettings.vue:69
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Додај колону"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "Додај постојећег корисника"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:58
#: frontend/src/components/FieldLayoutEditor.vue:173
#: frontend/src/components/Kanban/KanbanSettings.vue:84
#: frontend/src/components/SidePanelLayoutEditor.vue:98
msgid "Add Field"
msgstr "Додај поље"

#: frontend/src/components/Filter.vue:138
msgid "Add Filter"
msgstr "Додај филтер"

#: frontend/src/components/Controls/Grid.vue:321
msgid "Add Row"
msgstr "Додај ред"

#: frontend/src/components/FieldLayoutEditor.vue:200
#: frontend/src/components/SidePanelLayoutEditor.vue:130
msgid "Add Section"
msgstr "Додај одељак"

#: frontend/src/components/SortBy.vue:148
msgid "Add Sort"
msgstr "Додај сортирање"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "Додај картицу"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Додај недељне празнике"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "Додај дијаграм"

#: frontend/src/components/FieldLayoutEditor.vue:426
msgid "Add column"
msgstr "Додај колону"

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "Додај опис..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "Додај постојеће системске кориснике у овај CRM. Додели им улогу како би им омогућио приступ са постојећим креденцијалима."

#: frontend/src/components/ViewControls.vue:104
msgid "Add filter"
msgstr "Додај филтер"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "Додај напомену"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "Додај пробне податке"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "Додај svg код или користи feather иконице, нпр. 'подешавање'"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "Додај задатак"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Додај у празнике"

#: frontend/src/components/Layouts/AppSidebar.vue:434
msgid "Add your first comment"
msgstr "Додајте свој први коментар"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "Додајте, уредите и управљајте имејл шаблонима из различите CRM комуникације"

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Адреса"

#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:172
#: frontend/src/components/Settings/InviteUserPage.vue:179
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:184
#: frontend/src/components/Settings/Users.vue:244
#: frontend/src/components/Settings/Users.vue:247
msgid "Admin"
msgstr "Администратор"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "Агент тренутно није доступан да прими позив, молимо Вас да покушате касније."

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Све"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:512 frontend/src/pages/MobileContact.vue:510
#: frontend/src/pages/MobileOrganization.vue:454
#: frontend/src/pages/Organization.vue:463
msgid "Amount"
msgstr "Износ"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "Износ након попуста"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "Догодила се грешка"

#: frontend/src/data/document.js:63
msgid "An error occurred while updating the document"
msgstr "Дошло је до грешке приликом ажурирања документа"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "Иконица са екстензијом .ico. Требало би да буде размере 16 х 16 пиксела. Генерисано помоћу favicon генератора [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "Препоручује се слика у размери 1:1 и 2:1"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "And"
msgstr "и"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "Годишњи приход"

#: frontend/src/components/Modals/DealModal.vue:201
#: frontend/src/components/Modals/LeadModal.vue:142
msgid "Annual Revenue should be a number"
msgstr "Годишњи приход треба да буде број"

#: frontend/src/components/Settings/General/BrandSettings.vue:69
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "Појављује се у левој бочној траци. Препоручена величина је 32 х 32 пиксела у PNG или SVG формату"

#: frontend/src/components/Settings/General/BrandSettings.vue:103
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "Појављује се поред наслова у картици интернет претраживача. Препоручена величина је 32 х 32 пиксела у PNG или ICO формату"

#: frontend/src/components/Kanban/KanbanSettings.vue:107
#: frontend/src/components/Kanban/KanbanView.vue:45
msgid "Apply"
msgstr "Примени"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Примени на"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Примени на"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "Апликације"

#: frontend/src/components/Activities/AttachmentArea.vue:139
msgid "Are you sure you want to delete this attachment?"
msgstr "Да ли сте сигурни да желите да обришете ове прилоге?"

#: frontend/src/pages/MobileContact.vue:263
msgid "Are you sure you want to delete this contact?"
msgstr "Да ли сте сигурни да желите да обришете овај контакт?"

#: frontend/src/pages/MobileOrganization.vue:264
msgid "Are you sure you want to delete this organization?"
msgstr "Да ли сте сигурни да желите да обришете ову организацију?"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "Да ли сте сигурни да желите да обришете овај задатак?"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "Да ли сте сигурни да желите да обришете {0} повезаних ставки?"

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Да ли сте сигурни да желите да се пријавите на своју Frappe Cloud контролну таблу?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "Да ли сте сигурни да желите да ресетујете скрипту обрасца 'Креирај понуду из ЦРМ пословне прилике'?"

#: frontend/src/components/Settings/General/CurrencySettings.vue:174
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "Да ли сте сигурни да желите да поставите валуту као {0}? Ово се не може променити касније."

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "Да ли сте сигурни да желите да поништите повезивање {0} повезаних ставки?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "Затражите од свог менаџера да постави провајдера девизних курсева, јер подразумевани провајдер не подржава конверзију валута из {0} у {1}."

#: frontend/src/components/ListBulkActions.vue:184
#: frontend/src/components/Modals/AssignmentModal.vue:5
msgid "Assign To"
msgstr "Додели"

#: frontend/src/components/AssignTo.vue:9
msgid "Assign to"
msgstr "Додели"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Додељено"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Задатак"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Правило доделе задатка"

#: frontend/src/components/ListBulkActions.vue:152
msgid "Assignment cleared successfully"
msgstr "Задатак је успешно уклоњен"

#: frontend/src/components/Layouts/AppSidebar.vue:577
msgid "Assignment rule"
msgstr "Правило доделе задатка"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:176
msgid "At least one field is required"
msgstr "Барем једно поље је обавезно"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:76
msgid "Attach"
msgstr "Приложи"

#: frontend/src/pages/Deal.vue:117 frontend/src/pages/Lead.vue:174
msgid "Attach a file"
msgstr "Приложи фајл"

#: frontend/src/pages/Deal.vue:575 frontend/src/pages/Lead.vue:441
#: frontend/src/pages/MobileDeal.vue:468 frontend/src/pages/MobileLead.vue:375
msgid "Attachments"
msgstr "Прилози"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "Аутентификациони токен"

#: crm/api/dashboard.py:238
msgid "Average deal value of non won/lost deals"
msgstr "Просечна вредност пословне прилике која није ни добијена ни изгубљена"

#: crm/api/dashboard.py:411
msgid "Average deal value of ongoing & won deals"
msgstr "Просечна вредност текућих и добијених пословних прилика"

#: crm/api/dashboard.py:354
msgid "Average deal value of won deals"
msgstr "Просечна вредност добијених пословних прилика"

#: crm/api/dashboard.py:518
msgid "Average time taken from deal creation to deal closure"
msgstr "Просечно време од креирања до затварања пословне прилике"

#: crm/api/dashboard.py:464
msgid "Average time taken from lead creation to deal closure"
msgstr "Просечно време од креирања потенцијалног клијента до затварања пословне прилике"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "Просечна вредност пословне прилике"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "Просечна вредност текуће пословне прилике"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "Просечно време за затварање пословне прилике"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "Просечно време за затварање потенцијалног клијента"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "Просечна вредност добијене пословне прилике"

#: crm/api/dashboard.py:410
msgid "Avg. deal value"
msgstr "Просечна вредност пословне прилике"

#: crm/api/dashboard.py:237
msgid "Avg. ongoing deal value"
msgstr "Просечна вредност текуће пословне прилике"

#: crm/api/dashboard.py:517
msgid "Avg. time to close a deal"
msgstr "Просечно време за затварање пословне прилике"

#: crm/api/dashboard.py:463
msgid "Avg. time to close a lead"
msgstr "Просечно време за затварање потенцијалног клијента"

#: crm/api/dashboard.py:353
msgid "Avg. won deal value"
msgstr "Просечна вредност добијене пословне прилике"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "Дијаграм са осама"

#: frontend/src/components/Activities/EmailArea.vue:72
#: frontend/src/components/EmailEditor.vue:44
#: frontend/src/components/EmailEditor.vue:69
msgid "BCC"
msgstr "BCC"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "Назад"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "Назад на отпремање фајлова"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "Закашњење"

#: frontend/src/components/Filter.vue:355
msgid "Between"
msgstr "Између"

#: frontend/src/components/Settings/General/BrandSettings.vue:40
msgid "Brand name"
msgstr "Назив бренда"

#: frontend/src/components/Settings/General/BrandSettings.vue:9
msgid "Brand settings"
msgstr "Подешавање бренда"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "Брендирање"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "Масовно уређивање"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Заузет"

#: frontend/src/components/Activities/EmailArea.vue:67
#: frontend/src/components/EmailEditor.vue:34
#: frontend/src/components/EmailEditor.vue:56
msgid "CC"
msgstr "CC"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "CRM евиденција позива"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "CRM статус комуникације"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "CRM контакти"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:318
msgid "CRM Dashboard"
msgstr "CRM контролна табла"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "CRM пословна прилика"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "CRM статус пословне прилике"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "CRM ставка падајућег менија"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "CRM Exotel подешавања"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "CRM распоред поља"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "CRM скрипта обрасца"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "CRM глобална подешавања"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "CRM празник"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "CRM листа празника"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "CRM индустрија"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "CRM позивница"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "CRM потенцијални клијент"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "CRM извор потенцијалног клијента"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "CRM статус потенцијалног клијента"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "CRM разлог губитка"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "CRM обавештење"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "CRM организација"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "CRM страница портала"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "CRM производ"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "CRM производи"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "CRM дан пружања услуге"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "CRM споразум о нивоу услуге"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "CRM приоритет нивоа услуге"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "CRM евиденција промена статуса"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "CRM задатак"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "CRM агент телефоније"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "CRM број телефоније"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "CRM територија"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "CRM Twilio подешавања"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "CRM подешавање приказа"

#: frontend/src/components/Settings/General/CurrencySettings.vue:42
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "CRM валута за све новчане вредности. Једном када се постави, не може се мењати."

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Детаљи позива"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "Позив примио"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "Трајање позива у секундама"

#: frontend/src/components/Layouts/AppSidebar.vue:551
msgid "Call log"
msgstr "Евиденција позива"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "Позив користећи {0}"

#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:43
msgid "Call with John Doe"
msgstr "Позив са Петром Петровићем"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "Позивалац"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "Средство за позивање"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Calling..."
msgstr "Позивање..."

#: frontend/src/pages/Deal.vue:560 frontend/src/pages/Lead.vue:426
#: frontend/src/pages/MobileDeal.vue:452 frontend/src/pages/MobileLead.vue:359
msgid "Calls"
msgstr "Позиви"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Камера"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/ColumnSettings.vue:132
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:114
#: frontend/src/components/Modals/AssignmentModal.vue:9
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:77
#: frontend/src/components/ViewControls.vue:56
#: frontend/src/components/ViewControls.vue:156
#: frontend/src/pages/Dashboard.vue:41
msgid "Cancel"
msgstr "Откажи"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Отказано"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "Није могуће променити улогу кориснику са администраторским приступом"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "Није могуће обрисати стандардну ставку {0}"

#: frontend/src/components/FilesUploader/FilesUploader.vue:94
msgid "Capture"
msgstr "Забележи"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Capturing leads"
msgstr "Бележење потенцијалних клијената"

#: frontend/src/components/Layouts/AppSidebar.vue:485
msgid "Change"
msgstr "Промена"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Промени лозинку"

#: frontend/src/components/Activities/TaskArea.vue:44
msgid "Change Status"
msgstr "Промени статус"

#: frontend/src/components/Layouts/AppSidebar.vue:476
#: frontend/src/components/Layouts/AppSidebar.vue:484
msgid "Change deal status"
msgstr "Промени статус пословне прилике"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:41 frontend/src/pages/Lead.vue:95
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:41
msgid "Change image"
msgstr "Промени слику"

#: frontend/src/pages/Dashboard.vue:28
msgid "Chart"
msgstr "Дијаграм"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Врста дијаграма"

#: frontend/src/components/Modals/ConvertToDealModal.vue:43
#: frontend/src/components/Modals/ConvertToDealModal.vue:69
#: frontend/src/pages/MobileLead.vue:124 frontend/src/pages/MobileLead.vue:151
msgid "Choose Existing"
msgstr "Изабери постојеће"

#: frontend/src/components/Modals/DealModal.vue:45
msgid "Choose Existing Contact"
msgstr "Изабери постојећи контакт"

#: frontend/src/components/Modals/DealModal.vue:38
msgid "Choose Existing Organization"
msgstr "Изабери постојећу организацију"

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "Изабери провајдера имејл услуге кога желиш да конфигуришеш."

#: frontend/src/components/Controls/Link.vue:62
msgid "Clear"
msgstr "Очисти"

#: frontend/src/components/ListBulkActions.vue:134
#: frontend/src/components/ListBulkActions.vue:142
#: frontend/src/components/ListBulkActions.vue:188
msgid "Clear Assignment"
msgstr "Очисти додељене задатке"

#: frontend/src/components/SortBy.vue:160
msgid "Clear Sort"
msgstr "Очисти сортирање"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Очисти табелу"

#: frontend/src/components/Filter.vue:18 frontend/src/components/Filter.vue:150
msgid "Clear all Filter"
msgstr "Очисти све филтере"

#: frontend/src/components/Notifications.vue:28
msgid "Close"
msgstr "Затвори"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "Датум затварања"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Сажми"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Collapsible"
msgstr "Може се сажети"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Боја"

#: frontend/src/components/FieldLayoutEditor.vue:423
msgid "Column"
msgstr "Колона"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:15
msgid "Column Field"
msgstr "Поље колоне"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Колоне"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:80
#: frontend/src/components/CommunicationArea.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:574
msgid "Comment"
msgstr "Коментар"

#: frontend/src/pages/Deal.vue:550 frontend/src/pages/Lead.vue:416
#: frontend/src/pages/MobileDeal.vue:442 frontend/src/pages/MobileLead.vue:349
msgid "Comments"
msgstr "Коментари"

#: crm/api/dashboard.py:884
msgid "Common reasons for losing deals"
msgstr "Уобичајени разлози за губитак пословних прилика"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "Статус комуникације"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "Статуси комуникације"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "Компанија на ERPNext веб-сајту"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Завршено"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Рачунар"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Услов"

#: frontend/src/components/Settings/General/GeneralSettings.vue:8
msgid "Configure general settings for your CRM"
msgstr "Конфигуришите општа подешавања Вашег CRM"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "Конфигуришите подешавања телефоније за Ваш CRM"

#: frontend/src/components/Settings/General/CurrencySettings.vue:70
msgid "Configure the exchange rate provider for your CRM"
msgstr "Конфигуришите провајдера девизних курсева за Ваш CRM"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Потврди"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:250
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:253
msgid "Confirm Delete"
msgstr "Потврди брисање"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "Потврди лозинку"

#: frontend/src/components/Settings/Users.vue:225
#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "Потврди уклањање"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "Повежите Ваш имејл"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:547
#: frontend/src/components/Modals/ConvertToDealModal.vue:65
#: frontend/src/pages/MobileLead.vue:147
msgid "Contact"
msgstr "Контакт"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:212
msgid "Contact Already Exists"
msgstr "Контакт већ постоји"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "Контактирајте подршку"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "Контактирајте нас"

#: frontend/src/pages/Deal.vue:655 frontend/src/pages/MobileDeal.vue:546
msgid "Contact added"
msgstr "Контакт је додат"

#: frontend/src/pages/Deal.vue:645 frontend/src/pages/MobileDeal.vue:536
msgid "Contact already added"
msgstr "Контакт је већ додат"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:211
msgid "Contact already exists with {0}"
msgstr "Контакт већ постоји са {0}"

#: frontend/src/pages/Contact.vue:282 frontend/src/pages/MobileContact.vue:255
msgid "Contact image updated"
msgstr "Слика контакта је ажурирана"

#: frontend/src/pages/Deal.vue:666 frontend/src/pages/MobileDeal.vue:557
msgid "Contact removed"
msgstr "Контакт је уклоњен"

#: frontend/src/pages/Contact.vue:437 frontend/src/pages/Contact.vue:450
#: frontend/src/pages/Contact.vue:463 frontend/src/pages/Contact.vue:473
#: frontend/src/pages/MobileContact.vue:435
#: frontend/src/pages/MobileContact.vue:448
#: frontend/src/pages/MobileContact.vue:461
#: frontend/src/pages/MobileContact.vue:471
msgid "Contact updated"
msgstr "Контакт је ажуриран"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:237 frontend/src/pages/MobileContact.vue:215
#: frontend/src/pages/MobileOrganization.vue:334
msgid "Contacts"
msgstr "Контакти"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:34
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:105
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:105
msgid "Content"
msgstr "Садржај"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Content Type"
msgstr "Врста садржаја"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:167
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:165
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:169
msgid "Content is required"
msgstr "Садржај је обавезан"

#: frontend/src/components/Layouts/AppSidebar.vue:375
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:8
#: frontend/src/pages/MobileLead.vue:56 frontend/src/pages/MobileLead.vue:110
msgid "Convert"
msgstr "Пребаци"

#: frontend/src/components/Layouts/AppSidebar.vue:366
#: frontend/src/components/Layouts/AppSidebar.vue:374
msgid "Convert lead to deal"
msgstr "Пребаци потенцијалног клијента у пословну прилику"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:195
#: frontend/src/components/Modals/ConvertToDealModal.vue:19
#: frontend/src/pages/Lead.vue:45 frontend/src/pages/MobileLead.vue:106
msgid "Convert to Deal"
msgstr "Пребаци у пословну прилику"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Пребачено"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "Успешно пребачено"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Копирано у међуспремник"

#: crm/api/dashboard.py:607 crm/api/dashboard.py:736 crm/api/dashboard.py:794
#: crm/api/dashboard.py:891
msgid "Count"
msgstr "Бројчано"

#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/ContactModal.vue:41
#: frontend/src/components/Modals/CreateDocumentModal.vue:93
#: frontend/src/components/Modals/DealModal.vue:67
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/LeadModal.vue:38
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/OrganizationModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Modals/ViewModal.vue:16
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/pages/CallLogs.vue:11 frontend/src/pages/Contacts.vue:13
#: frontend/src/pages/Contacts.vue:60 frontend/src/pages/Deals.vue:13
#: frontend/src/pages/Deals.vue:236 frontend/src/pages/Leads.vue:13
#: frontend/src/pages/Leads.vue:262 frontend/src/pages/Notes.vue:7
#: frontend/src/pages/Notes.vue:93 frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:11
#: frontend/src/pages/Tasks.vue:185
msgid "Create"
msgstr "Креирај"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "Креирај пословну прилику"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Креирај потенцијалног клијента"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/SidePanelLayout.vue:137
msgid "Create New"
msgstr "Креирај нови"

#: frontend/src/components/Activities/Activities.vue:384
#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Create Note"
msgstr "Креирај белешку"

#: frontend/src/components/Activities/Activities.vue:399
#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Create Task"
msgstr "Креирај задатак"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "Креирај приказ"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "Креирај клијента при промени статуса"

#: frontend/src/components/Modals/CallLogDetailModal.vue:152
msgid "Create lead"
msgstr "Креирај потенцијалног клијента"

#: frontend/src/components/Layouts/AppSidebar.vue:344
msgid "Create your first lead"
msgstr "Креирај свог првог потенцијалног клијента"

#: frontend/src/components/Layouts/AppSidebar.vue:414
msgid "Create your first note"
msgstr "Креирај своју прву белешку"

#: frontend/src/components/Layouts/AppSidebar.vue:394
msgid "Create your first task"
msgstr "Креирај свој први задатак"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:38
msgid "Currency"
msgstr "Валута"

#: frontend/src/components/Settings/General/CurrencySettings.vue:9
msgid "Currency & Exchange rate provider"
msgstr "Валута и провајдер девизних курсева"

#: frontend/src/components/Settings/General/CurrencySettings.vue:188
msgid "Currency set as {0} successfully"
msgstr "Валута је успешно постављена као {0}"

#: crm/api/dashboard.py:839
msgid "Current pipeline distribution"
msgstr "Тренутна расподела продајног тока"

#: frontend/src/components/Layouts/AppSidebar.vue:586
msgid "Custom actions"
msgstr "Прилагођене радње"

#: frontend/src/components/Layouts/AppSidebar.vue:536
msgid "Custom branding"
msgstr "Прилагођено брендирање"

#: frontend/src/components/Layouts/AppSidebar.vue:585
msgid "Custom fields"
msgstr "Прилагођена поља"

#: frontend/src/components/Layouts/AppSidebar.vue:588
msgid "Custom list actions"
msgstr "Прилагођене радње на листи"

#: frontend/src/components/Layouts/AppSidebar.vue:587
msgid "Custom statuses"
msgstr "Прилагођени статуси"

#: frontend/src/pages/Deal.vue:486
msgid "Customer created successfully"
msgstr "Клијент је успешно креиран"

#: frontend/src/components/Layouts/AppSidebar.vue:582
msgid "Customization"
msgstr "Прилагођавање"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "Прилагоди брзе филтере"

#: crm/api/dashboard.py:599
msgid "Daily performance of leads, deals, and wins"
msgstr "Дневне перформансе по потенцијалним клијентима, пословним приликама и добијеним пословима"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:575
#: frontend/src/pages/Deal.vue:555 frontend/src/pages/Lead.vue:421
#: frontend/src/pages/MobileDeal.vue:447 frontend/src/pages/MobileLead.vue:354
msgid "Data"
msgstr "Подаци"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "Поља за податак"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:601 crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "Date"
msgstr "Датум"

#: frontend/src/components/Layouts/AppSidebar.vue:546
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:62
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:62
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:129
msgid "Deal"
msgstr "Пословна прилика"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Власник пословне прилике"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "Статус пословне прилике"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "Статуси пословне прилике"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "Вредност пословне прилике"

#: crm/api/dashboard.py:977
msgid "Deal generation channel analysis"
msgstr "Анализа канала за генерисање пословних прилика"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:531
#: frontend/src/pages/MobileOrganization.vue:475
#: frontend/src/pages/Organization.vue:484
msgid "Deal owner"
msgstr "Власник пословне прилике"

#: crm/api/dashboard.py:1030 crm/api/dashboard.py:1087
msgid "Deal value"
msgstr "Вредност пословне прилике"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:500 frontend/src/pages/MobileContact.vue:291
#: frontend/src/pages/MobileDeal.vue:386
#: frontend/src/pages/MobileOrganization.vue:328
msgid "Deals"
msgstr "Пословне прилике"

#: crm/api/dashboard.py:788
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "Пословне прилике по текућој и добијеној фази"

#: crm/api/dashboard.py:1076
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "Пословне прилике по продавцу"

#: crm/api/dashboard.py:976
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "Пословне прилике по извору"

#: crm/api/dashboard.py:838
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "Пословне прилике по фази"

#: crm/api/dashboard.py:1019
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "Пословне прилике по територији"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:115
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:115
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "Здраво {{ lead_name }}, \\n\\nОво је подсетник за износ одf {{ grand_total }}. \\n\\nХвала, \\nFrappé"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Подразумевано"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Подразумевана пријемна пошта"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Подразумевани улазни"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "Подразумевано средство"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Подразумевани излазни"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Подразумевани приоритет"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Подразумевано слање"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Подразумевано слање и пријемна пошта"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "Подразумевани споразум о нивоу услуге већ постоји за {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "Подразумевано средство за позивање за пријављене кориснике"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "Подразумевано средство за позивање је успешно постављено на {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "Подразумевано средство за позивање је успешно ажурирано"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "Подразумевано средство"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "Подразумевани статуси, прилагођена поља и распореди су успешно враћени."

#: frontend/src/components/Activities/AttachmentArea.vue:142
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:316
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:89
#: frontend/src/components/Kanban/KanbanView.vue:225
#: frontend/src/components/ListBulkActions.vue:177
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:235
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:238
#: frontend/src/components/ViewControls.vue:1161
#: frontend/src/components/ViewControls.vue:1172
#: frontend/src/pages/Contact.vue:103 frontend/src/pages/Deal.vue:124
#: frontend/src/pages/Lead.vue:183 frontend/src/pages/MobileContact.vue:82
#: frontend/src/pages/MobileContact.vue:266
#: frontend/src/pages/MobileDeal.vue:517
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:267
#: frontend/src/pages/Notes.vue:40 frontend/src/pages/Organization.vue:83
#: frontend/src/pages/Tasks.vue:369
msgid "Delete"
msgstr "Обриши"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "Обриши и врати"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "Обриши задатак"

#: frontend/src/components/ViewControls.vue:1157
#: frontend/src/components/ViewControls.vue:1165
msgid "Delete View"
msgstr "Обриши приказ"

#: frontend/src/components/DeleteLinkedDocModal.vue:65
msgid "Delete all"
msgstr "Обриши све"

#: frontend/src/components/Activities/AttachmentArea.vue:62
#: frontend/src/components/Activities/AttachmentArea.vue:138
msgid "Delete attachment"
msgstr "Обриши прилог"

#: frontend/src/pages/MobileContact.vue:262
msgid "Delete contact"
msgstr "Обриши контакт"

#: frontend/src/components/DeleteLinkedDocModal.vue:229
msgid "Delete linked item"
msgstr "Обриши повезану ставку"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "Обриши или поништи повезивање повезаних докумената"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "Обришите или поништите повезивање ових докумената пре него што обришете овај документ"

#: frontend/src/pages/MobileOrganization.vue:263
msgid "Delete organization"
msgstr "Обриши организацију"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete {0} item(s)"
msgstr "Обриши {0} ставку"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "Обриши {0} ставки"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Modals/TaskModal.vue:49
msgid "Description"
msgstr "Опис"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Радна површина"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:286
#: frontend/src/pages/MobileDeal.vue:426 frontend/src/pages/MobileLead.vue:333
#: frontend/src/pages/MobileOrganization.vue:323
msgid "Details"
msgstr "Детаљи"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "Уређај"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Онемогући"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Онемогућено"

#: frontend/src/components/CommentBox.vue:76
#: frontend/src/components/EmailEditor.vue:158
msgid "Discard"
msgstr "Одбаци"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Попуст %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Износ попуста"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "DocType"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "Врста документа"

#: frontend/src/data/document.js:28
msgid "Document does not exist"
msgstr "Документ не постоји"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "Документ није пронађен"

#: frontend/src/data/document.js:42
msgid "Document updated successfully"
msgstr "Документ је успешно ажуриран"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "Документација"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "Завршено"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "Прстенасти дијаграм"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Преузми"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "Превуци и отпусти фајлове овде или их отпреми из"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "Отпусти фајлове овде"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "Ставке падајућег менија"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Датум доспећа"

#: frontend/src/components/Modals/ViewModal.vue:15
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:225
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:228
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/components/ViewControls.vue:1113
msgid "Duplicate"
msgstr "Дупликат"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "Дупликат приказа"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "Дупликат шаблона"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Трајање"

#: frontend/src/components/Layouts/AppSidebar.vue:599
#: frontend/src/components/Settings/Settings.vue:135
msgid "ERPNext"
msgstr "ERPNext"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "ERPNext CRM подешавања"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "ERPNext API сајта"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "ERPNext URL сајта"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "ERPNext није инсталиран на тренутном сајту"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "ERPNext није интегрисан са CRM-ом"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "ERPNext подешавања"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "ERPNext подешавања су ажурирана"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:319
#: frontend/src/components/FieldLayoutEditor.vue:345
#: frontend/src/components/ListBulkActions.vue:170
#: frontend/src/components/ViewControls.vue:1131
#: frontend/src/pages/Dashboard.vue:19
msgid "Edit"
msgstr "Уреди"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "Edit Call Log"
msgstr "Уреди евиденцију позива"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "Уреди распоред поља са подацима"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "Уреди имејл"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "Уреди распоред поља"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "Уреди распоред поља у табели"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "Уреди распоред поља у реду табеле"

#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Edit Note"
msgstr "Уреди белешку"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "Уреди распоред за брзи унос"

#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Edit Task"
msgstr "Уреди задатак"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "Уреди приказ"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "Уреди белешку"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "Уреди задатак"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "Уређивање реда {0}"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:523
#: frontend/src/pages/MobileContact.vue:521
#: frontend/src/pages/MobileOrganization.vue:465
#: frontend/src/pages/MobileOrganization.vue:493
#: frontend/src/pages/Organization.vue:474
#: frontend/src/pages/Organization.vue:502
msgid "Email"
msgstr "Имејл"

#: frontend/src/components/Settings/Settings.vue:107
msgid "Email Accounts"
msgstr "Имејл налози"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "Имејл ИД је обавезан"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "Имејл послат у"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:113
msgid "Email Templates"
msgstr "Имејл шаблони"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "Имејл налог је успешно креиран"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "Имејл налог је успешно ажуриран"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "Имејл налози"

#: frontend/src/components/Layouts/AppSidebar.vue:573
msgid "Email communication"
msgstr "Имејл комуникација"

#: frontend/src/components/EmailEditor.vue:206
msgid "Email from Lead"
msgstr "Имејл од потенцијалног клијента"

#: frontend/src/components/Layouts/AppSidebar.vue:552
msgid "Email template"
msgstr "Имејл шаблон"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "Имејл шаблони"

#: frontend/src/pages/Deal.vue:545 frontend/src/pages/Lead.vue:411
#: frontend/src/pages/MobileDeal.vue:437 frontend/src/pages/MobileLead.vue:344
msgid "Emails"
msgstr "Имејлови"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Празно"

#: frontend/src/components/Filter.vue:124
msgid "Empty - Choose a field to filter by"
msgstr "Празно - Изаберите поље по којем желите да филтирате"

#: frontend/src/components/SortBy.vue:134
msgid "Empty - Choose a field to sort by"
msgstr "Празно - Изаберите поље по којем желите да сортирате"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Омогући"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "Омогући прогнозирање"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Омогући улазни"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Омогући излазну"

#: frontend/src/components/Settings/General/GeneralSettings.vue:19
msgid "Enable forecasting"
msgstr "Омогући прогнозирање"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:33
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:33
msgid "Enabled"
msgstr "Омогућено"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Датум завршетка"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "End Time"
msgstr "Време завршетка"

#: frontend/src/components/Settings/General/CurrencySettings.vue:122
msgid "Enter access key"
msgstr "Унесите кључ за приступ"

#: frontend/src/components/FieldLayout/Field.vue:334
msgid "Enter {0}"
msgstr "Унесите {0}"

#: frontend/src/components/Filter.vue:67 frontend/src/components/Filter.vue:100
#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:310
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:332
#: frontend/src/components/Filter.vue:348
msgid "Equals"
msgstr "Једнако"

#: frontend/src/components/Modals/ConvertToDealModal.vue:185
msgid "Error converting to deal: {0}"
msgstr "Грешка приликом пребацивања у пословну прилику: {0}"

#: frontend/src/pages/Deal.vue:739 frontend/src/pages/Lead.vue:486
#: frontend/src/pages/MobileDeal.vue:614 frontend/src/pages/MobileLead.vue:415
msgid "Error updating field"
msgstr "Грешка приликом ажурирања поља"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:261
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "Грешка приликом креирања клијента у ERPNext-у, молимо Вас да проверите евиденцију грешака за више детаља"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:173
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "Грешка приликом креирања потенцијалног купца у ERPNext-у, молимо Вас да проверите евиденцију грешака за више детаља"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "Грешка приликом преузимања података о клијентима у ERPNext-у, молимо Вас да проверите евиденцију грешака за више детаља"

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Девизни курс"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "Провајдер девизних курсева"

#: frontend/src/components/Settings/General/CurrencySettings.vue:67
msgid "Exchange rate provider"
msgstr "Провајдер девизних курсева"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:597
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel изузетак"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel број"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Exotel број недостаје"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel број {0} није важећи"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel није омогућен"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel подешавања су успешно ажурирана"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Прошири"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "Очекивани датум затварања"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:161
msgid "Expected Closure Date is required."
msgstr "Очекивани датум затварања је обавезан."

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "Очекивана вредност пословне прилике"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:159
msgid "Expected Deal Value is required."
msgstr "Очекивана вредност пословне прилике је обавезна."

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Истекло"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Извоз"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "Извоз свих {0} записа"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Врста извоза"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "FCRM белешка"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "FCRM подешавања"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Неуспешно"

#: frontend/src/components/Modals/AddExistingUserModal.vue:109
msgid "Failed to add users"
msgstr "Неуспешно додавање корисника"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "Неуспешно снимање путем Twilio"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "Неуспешно креирање имејл налога, неважећи креденцијали"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:182
msgid "Failed to create template"
msgstr "Неуспешно креирање шаблона"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:217
msgid "Failed to delete template"
msgstr "Неуспешно брисање шаблона"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "Неуспешно преузимање девизног курса из {0} у {1} на {2}. Молимо Вас да проверите своју интернет конекцију или покушајте поново касније."

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "Неуспешно учитавање form controller-а: {0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:246
msgid "Failed to rename template"
msgstr "Неуспешно преименовање шаблона"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "Неуспешно ажурирање статуса позива на Twilio"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "Неуспешно ажурирање имејл налога, неважећи креденцијали"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "Неуспешно ажурирање лозинке"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "Неуспешно ажурирање профила"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:212
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:202
msgid "Failed to update template"
msgstr "Неуспешно ажурирање шаблона"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:81
msgid "Favicon"
msgstr "Favicon"

#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Поље"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:51
msgid "Fields Order"
msgstr "Редослед поља"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "Фајл \"{0}\" је прескочен због неважеће врсте фајла"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "Фајл \"{0}\" је прескочен јер је дозвољено само {1} отпремања"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "Фајл \"{0}\" је прескочен јер је дозвољено само {1} отпремања за DocType \"{2}\""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Филтер"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Филтери"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:112
#: frontend/src/components/Filter.vue:58 frontend/src/components/Filter.vue:89
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:106
#: frontend/src/components/SortBy.vue:140
msgid "First Name"
msgstr "Име"

#: frontend/src/components/Modals/LeadModal.vue:135
msgid "First Name is mandatory"
msgstr "Име је обавезно"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Први одговор дат на"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "Рок за први одговор"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "Време за први одговор"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Време за први одговор"

#: frontend/src/components/Filter.vue:131
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "Име"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
msgid "For"
msgstr "За"

#: crm/api/dashboard.py:666
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "Прогноза прихода"

#: frontend/src/components/Settings/General/GeneralSettings.vue:100
msgid "Forecasting disabled successfully"
msgstr "Прогнозирање је успешно онемогућено"

#: frontend/src/components/Settings/General/GeneralSettings.vue:99
msgid "Forecasting enabled successfully"
msgstr "Прогнозирање је успешно омогућено"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "Образац"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "Скрипта обрасца је успешно ажурирана"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:603
msgid "Frappe CRM mobile"
msgstr "Frappe CRM мобилна апликација"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Петак"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "Од"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "Датум почетка"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "Од врсте"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "Од стране корисника"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Испуњено"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Име и презиме"

#: crm/api/dashboard.py:728
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "Конверзија у продајном левку"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Род"

#: frontend/src/components/Settings/General/GeneralSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:89
msgid "General"
msgstr "Опште"

#: crm/api/dashboard.py:1020
msgid "Geographic distribution of deals and revenue"
msgstr "Географска расподела пословних прилика и прихода"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "GitHub репозиторијум"

#: frontend/src/pages/Deal.vue:104 frontend/src/pages/Lead.vue:159
msgid "Go to website"
msgstr "Иди на веб-сајт"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "Ред у табели"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Груписано по"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "Груписано по пољу"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Груписано по: "

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Помоћ"

#: frontend/src/components/CommunicationArea.vue:62
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "Здраво Петре, \\n\\nМожете ли молим Вас да пружите више детаља о овоме..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Сакривено"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Hide"
msgstr "Сакриј"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "Сакриј лозинку"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Hide Recording"
msgstr "Сакриј снимак"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Hide border"
msgstr "Сакриј ивицу"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Hide label"
msgstr "Сакриј ознаку"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "Сакриј приказ"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Висок"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Листа празника"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Назив листе празника"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Празници"

#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/General/HomeActions.vue:9
msgid "Home actions"
msgstr "Радње на почетној страници"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "ID"
msgstr "ИД"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Иконица"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "Уколико је омогућено, сви одлазни имејлови ће се слати са овог налога. Напомена: Искључиво један налог може бити подразумевани за одлазне поруке."

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "Уколико је омогућено, сви одговори ка Вашој компанији (нпр. odgovori@vašakompanija.com) долазиће на овај налог. Напомена: Искључиво један налог може бити подразумевани за долазне поруке."

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "Уколико је омогућено, одлазни имејлови могу бити послати са овог налога."

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "Уколико је омогућено, записи могу бити креирани из долазних имејлова на овом налогу."

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Слика"

#: frontend/src/components/Filter.vue:276
#: frontend/src/components/Filter.vue:297
#: frontend/src/components/Filter.vue:312
#: frontend/src/components/Filter.vue:325
#: frontend/src/components/Filter.vue:339
msgid "In"
msgstr "У"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "У току"

#: frontend/src/components/SLASection.vue:75
msgid "In less than a minute"
msgstr "За мање од једног минута"

#: frontend/src/components/Activities/CallArea.vue:35
msgid "Inbound Call"
msgstr "Долазни позив"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Пријемна пошта"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Долазни"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "Долазни позив..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "Индустрије"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Индустрија"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Започето"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "Започињање позива..."

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Integration"
msgstr "Интеграција"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "Интеграција није омогућена"

#: frontend/src/components/Settings/Settings.vue:120
msgctxt "FCRM"
msgid "Integrations"
msgstr "Интеграције"

#: frontend/src/components/Layouts/AppSidebar.vue:524
#: frontend/src/components/Layouts/AppSidebar.vue:527
msgid "Introduction"
msgstr "Увод"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "Неважећи SID налога или аутентификациони токен."

#: frontend/src/components/Modals/DealModal.vue:213
#: frontend/src/components/Modals/LeadModal.vue:154
msgid "Invalid Email"
msgstr "Неважећи имејл"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "Неважећи Exotel број"

#: crm/api/dashboard.py:73
msgid "Invalid chart name"
msgstr "Неважећи назив дијаграма"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "Неважећи креденцијали"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "Неважећи имејл ИД"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "Позовите новог корисника"

#: frontend/src/components/Settings/Settings.vue:101
msgid "Invite User"
msgstr "Позовите корисника"

#: frontend/src/components/Settings/InviteUserPage.vue:56
msgid "Invite as"
msgstr "Позовите као"

#: frontend/src/components/Settings/InviteUserPage.vue:29
msgid "Invite by email"
msgstr "Позовите путем имејла"

#: frontend/src/components/Layouts/AppSidebar.vue:538
msgid "Invite users"
msgstr "Позовите кориснике"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "Позовите кориснике да приступе CRM. Наведите њихове улоге ради контроле приступа и дозвола"

#: frontend/src/components/Layouts/AppSidebar.vue:354
msgid "Invite your team"
msgstr "Позовите свој тим"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "Позван од стране"

#: frontend/src/components/Filter.vue:278
#: frontend/src/components/Filter.vue:287
#: frontend/src/components/Filter.vue:299
#: frontend/src/components/Filter.vue:314
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:341
#: frontend/src/components/Filter.vue:350
msgid "Is"
msgstr "Јесте"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Подразумевано"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "Да ли је ERPNext инсталиран на другом сајту?"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Група"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Примарно"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Стандардно"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "Учиниће да су \"Очекивани датум затварања\" и \"Очекивана вредност пословне прилике\" обавезни за прецизно добијање увида у прогнозу"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Радно место"

#: frontend/src/components/Filter.vue:75 frontend/src/components/Filter.vue:108
#: frontend/src/components/Modals/AssignmentModal.vue:35
#: frontend/src/components/Modals/TaskModal.vue:76
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "Петар Петровић"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Канбан"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "Канбан колоне"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "Канбан поља"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:11
msgid "Kanban Settings"
msgstr "Канбан подешавање"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Key"
msgstr "Кључ"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:109
msgid "Label"
msgstr "Ознака"

#: frontend/src/components/Filter.vue:620
msgid "Last 6 Months"
msgstr "Последњих 6 месеци"

#: frontend/src/components/Filter.vue:612
msgid "Last Month"
msgstr "Прошли месец"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Презиме"

#: frontend/src/components/Filter.vue:616
msgid "Last Quarter"
msgstr "Прошли квартал"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "Евиденција последње промене статуса"

#: frontend/src/components/Filter.vue:608
msgid "Last Week"
msgstr "Прошла недеља"

#: frontend/src/components/Filter.vue:624
msgid "Last Year"
msgstr "Прошла година"

#: frontend/src/pages/Contact.vue:538 frontend/src/pages/MobileContact.vue:536
#: frontend/src/pages/MobileOrganization.vue:480
#: frontend/src/pages/MobileOrganization.vue:508
#: frontend/src/pages/Organization.vue:489
#: frontend/src/pages/Organization.vue:517
msgid "Last modified"
msgstr "Последњи пут измењено"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "Презиме"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "Распоред"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:545
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:58
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:58
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:130
msgid "Lead"
msgstr "Потенцијални клијент"

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Детаљи потенцијалног клијената"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Назив потенцијалног клијента"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Власник потенцијалног клијента"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:73
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "Власник потенцијалног клијента не може бити исти као имејл адреса потенцијалног клијента"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "Извори потенцијалног клијента"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "Статуси потенцијалног клијента"

#: crm/api/dashboard.py:935
msgid "Lead generation channel analysis"
msgstr "Анализа канала за генерисање потенцијалних клијената"

#: crm/api/dashboard.py:729
msgid "Lead to deal conversion pipeline"
msgstr "Ток пребацивања потенцијалних клијената у пословне прилике"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Lead.vue:369 frontend/src/pages/MobileLead.vue:293
msgid "Leads"
msgstr "Потенцијални клијенти"

#: crm/api/dashboard.py:934
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "Потенцијални клијенти по извору"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Лево"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "Библиотека"

#: frontend/src/components/Filter.vue:274
#: frontend/src/components/Filter.vue:285
#: frontend/src/components/Filter.vue:295
#: frontend/src/components/Filter.vue:323
#: frontend/src/components/Filter.vue:337
msgid "Like"
msgstr "Лајк"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Линк"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Линкови"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Листа"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Listen"
msgstr "Слушај"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "Учитај подразумеване колоне"

#: frontend/src/components/Kanban/KanbanView.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Учитај више"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:37
#: frontend/src/pages/Deal.vue:193 frontend/src/pages/MobileDeal.vue:119
msgid "Loading..."
msgstr "Учитавање..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Евиденција"

#: frontend/src/components/Activities/Activities.vue:803
#: frontend/src/components/Activities/ActivityHeader.vue:137
#: frontend/src/components/Activities/ActivityHeader.vue:180
msgid "Log a Call"
msgstr "Евидентирај позив"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Пријављивање на Frappe Cloud?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:47
msgid "Logo"
msgstr "Лого"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Изгубљено"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "Белешке о губицима"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Разлог губитка"

#: crm/api/dashboard.py:883
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "Разлози губитака пословних прилика"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "Белешке о губицима"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "Белешке о губицима су обавезне када је разлог губитка \"Остало\""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "Разлог губитка"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "Разлог губитка је обавезан"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Низак"

#: frontend/src/pages/Contact.vue:94 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "Позови"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Private"
msgstr "Постави као приватно"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Public"
msgstr "Постави као јавно"

#: frontend/src/components/Activities/Activities.vue:807
#: frontend/src/components/Activities/ActivityHeader.vue:142
#: frontend/src/components/Activities/ActivityHeader.vue:185
#: frontend/src/pages/Deals.vue:504 frontend/src/pages/Leads.vue:531
msgid "Make a Call"
msgstr "Позови"

#: frontend/src/pages/Deal.vue:86 frontend/src/pages/Lead.vue:128
msgid "Make a call"
msgstr "Позови"

#: frontend/src/components/Activities/AttachmentArea.vue:109
msgid "Make attachment {0}"
msgstr "Додај прилог {0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "Позови"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "Постави као приватно"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "Постави као јавно"

#: frontend/src/components/Activities/AttachmentArea.vue:118
msgid "Make {0}"
msgstr "Направите {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "Постави {0} као подразумевано средство за позивање"

#: frontend/src/components/Settings/General/GeneralSettings.vue:23
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "Поставља \"Очекивани датум затварања\" и \"Очекивана вредност пословне прилике\" као обавезна поља за прогнозирање вредности пословне прилике"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "Управљајте CRM корисницима додавањем или позивањем, и додељивањем улога како бисте контролисали њихов приступ и овлашћења"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "Управљајте својим имејл налозима за слање и пријем порука директно из CRM. Можете додати више налога и поставити један као подразумевани за долазне и одлазне имејлове."

#: frontend/src/components/Modals/AddExistingUserModal.vue:91
#: frontend/src/components/Settings/InviteUserPage.vue:171
#: frontend/src/components/Settings/InviteUserPage.vue:178
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:185
#: frontend/src/components/Settings/Users.vue:256
#: frontend/src/components/Settings/Users.vue:259
msgid "Manager"
msgstr "Менаџер"

#: frontend/src/data/document.js:54
msgid "Mandatory field error: {0}"
msgstr "Грешка у обавезном пољу: {0}"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Ручно"

#: frontend/src/components/Notifications.vue:19
#: frontend/src/pages/MobileNotification.vue:11
#: frontend/src/pages/MobileNotification.vue:14
msgid "Mark all as read"
msgstr "Означи све као прочитано"

#: frontend/src/components/Layouts/AppSidebar.vue:542
msgid "Masters"
msgstr "Мастер подаци"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Средње"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "Помињање"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Порука"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Средње име"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "Број мобилног телефона"

#: frontend/src/components/Modals/DealModal.vue:209
#: frontend/src/components/Modals/LeadModal.vue:150
msgid "Mobile No should be a number"
msgstr "Број мобилног телефона треба да буде број"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "Број мобилног телефона."

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "Број мобилног телефона"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "Број мобилног телефона недостаје"

#: frontend/src/components/Layouts/AppSidebar.vue:606
msgid "Mobile app installation"
msgstr "Инсталација мобилне апликације"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:526
#: frontend/src/pages/MobileOrganization.vue:470
#: frontend/src/pages/Organization.vue:479
msgid "Mobile no"
msgstr "Број мобилног телефона"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Понедељак"

#: crm/api/dashboard.py:669
msgid "Month"
msgstr "Месец"

#: frontend/src/components/FieldLayoutEditor.vue:454
msgid "Move to next section"
msgstr "Иди на следећи одељак"

#: frontend/src/components/FieldLayoutEditor.vue:407
msgid "Move to next tab"
msgstr "Иди на следећу картицу"

#: frontend/src/components/FieldLayoutEditor.vue:464
msgid "Move to previous section"
msgstr "Иди на претходни одељак"

#: frontend/src/components/FieldLayoutEditor.vue:393
msgid "Move to previous tab"
msgstr "Иди на претходну картицу"

#: frontend/src/components/Modals/ViewModal.vue:40
msgid "My Open Deals"
msgstr "Моје отворене пословне прилике"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:42
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:42
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:488
#: frontend/src/pages/Organization.vue:497
msgid "Name"
msgstr "Назив"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:157
msgid "Name is required"
msgstr "Назив је обавезан"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Серије именовања"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Нето износ"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Нето укупно"

#: frontend/src/components/Activities/ActivityHeader.vue:82
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Нови"

#: frontend/src/components/Modals/AddressModal.vue:94
msgid "New Address"
msgstr "Нова адреса"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "New Call Log"
msgstr "Нова евиденција позива"

#: frontend/src/components/Activities/Activities.vue:394
#: frontend/src/components/Activities/ActivityHeader.vue:27
#: frontend/src/components/Activities/ActivityHeader.vue:132
msgid "New Comment"
msgstr "Нови коментар"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "Нови контакт"

#: frontend/src/components/Activities/Activities.vue:389
#: frontend/src/components/Activities/ActivityHeader.vue:17
#: frontend/src/components/Activities/ActivityHeader.vue:127
msgid "New Email"
msgstr "Нови имејл"

#: frontend/src/components/Activities/ActivityHeader.vue:73
msgid "New Message"
msgstr "Нова порука"

#: frontend/src/components/Activities/ActivityHeader.vue:42
#: frontend/src/components/Activities/ActivityHeader.vue:148
#: frontend/src/pages/Deals.vue:510 frontend/src/pages/Leads.vue:537
msgid "New Note"
msgstr "Нова белешка"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "Нова организација"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Нова лозинка"

#: frontend/src/components/FieldLayoutEditor.vue:203
#: frontend/src/components/SidePanelLayoutEditor.vue:133
msgid "New Section"
msgstr "Нови одељак"

#: frontend/src/components/FieldLayoutEditor.vue:299
#: frontend/src/components/FieldLayoutEditor.vue:304
msgid "New Tab"
msgstr "Нова картица"

#: frontend/src/components/Activities/ActivityHeader.vue:52
#: frontend/src/components/Activities/ActivityHeader.vue:153
#: frontend/src/pages/Deals.vue:515 frontend/src/pages/Leads.vue:542
msgid "New Task"
msgstr "Нови задатак"

#: frontend/src/components/Activities/ActivityHeader.vue:163
msgid "New WhatsApp Message"
msgstr "Нова WhatsApp порука"

#: frontend/src/components/Modals/ConvertToDealModal.vue:81
#: frontend/src/pages/MobileLead.vue:164
msgid "New contact will be created based on the person's details"
msgstr "Нови контакт ће бити креиран на основу података о особи"

#: frontend/src/components/Modals/ConvertToDealModal.vue:56
#: frontend/src/pages/MobileLead.vue:138
msgid "New organization will be created based on the data in details section"
msgstr "Нова организација ће бити креирана на основу података у одељку детаљи"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "Нови шаблон"

#: frontend/src/components/Modals/CreateDocumentModal.vue:89
msgid "New {0}"
msgstr "Нови {0}"

#: frontend/src/components/Filter.vue:668
msgid "Next 6 Months"
msgstr "Следећих 6 месеци"

#: frontend/src/components/Filter.vue:660
msgid "Next Month"
msgstr "Следећи месец"

#: frontend/src/components/Filter.vue:664
msgid "Next Quarter"
msgstr "Следећи квартал"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "Следећи корак"

#: frontend/src/components/Filter.vue:656
msgid "Next Week"
msgstr "Следећа недеља"

#: frontend/src/components/Filter.vue:672
msgid "Next Year"
msgstr "Следећа година"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Не"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "Без одговора"

#: frontend/src/components/Controls/Grid.vue:309
msgid "No Data"
msgstr "Без података"

#: frontend/src/components/Kanban/KanbanView.vue:102
#: frontend/src/pages/Deals.vue:106 frontend/src/pages/Leads.vue:122
#: frontend/src/pages/Tasks.vue:68
msgid "No Title"
msgstr "Без наслова"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "Није извршена ниједна промена"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:282 frontend/src/pages/MobileDeal.vue:207
msgid "No contacts added"
msgstr "Нема додатних контаката"

#: frontend/src/pages/Deal.vue:97 frontend/src/pages/Lead.vue:150
msgid "No email set"
msgstr "Није постављен имејла"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "Нису пронађени шаблони имејлова"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "Без ознаке"

#: frontend/src/pages/Deal.vue:705
msgid "No mobile number set"
msgstr "Није постављен број мобилног телефона"

#: frontend/src/components/Notifications.vue:83
#: frontend/src/pages/MobileNotification.vue:67
msgid "No new notifications"
msgstr "Нема нових обавештења"

#: frontend/src/pages/Lead.vue:135
msgid "No phone number set"
msgstr "Није постављен број телефона"

#: frontend/src/pages/Deal.vue:700
msgid "No primary contact set"
msgstr "Није постављен примарни контакт"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:72
#: frontend/src/components/Controls/MultiSelectUserInput.vue:72
msgid "No results found"
msgstr "Нема резултата"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "Ниједан образац није пронађен"

#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "Нису пронађени корисници"

#: frontend/src/pages/MobileOrganization.vue:284
#: frontend/src/pages/Organization.vue:300
msgid "No website found"
msgstr "Није пронађен веб-сајт"

#: frontend/src/pages/Deal.vue:110 frontend/src/pages/Lead.vue:165
msgid "No website set"
msgstr "Није постављен веб-сајт"

#: frontend/src/components/SidePanelLayout.vue:128
msgid "No {0} Available"
msgstr "Нема доступних {0}"

#: frontend/src/pages/CallLogs.vue:56 frontend/src/pages/Contact.vue:159
#: frontend/src/pages/Contacts.vue:59 frontend/src/pages/Deals.vue:235
#: frontend/src/pages/Leads.vue:261 frontend/src/pages/MobileContact.vue:150
#: frontend/src/pages/MobileOrganization.vue:142
#: frontend/src/pages/Notes.vue:92 frontend/src/pages/Organization.vue:156
#: frontend/src/pages/Organizations.vue:59 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "Није пронађен ниједан {0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Број запослених лица"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "Нормално"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Није дозвољено"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:311
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:349
msgid "Not Equals"
msgstr "Није једнако"

#: frontend/src/components/Filter.vue:277
#: frontend/src/components/Filter.vue:298
#: frontend/src/components/Filter.vue:313
#: frontend/src/components/Filter.vue:326
#: frontend/src/components/Filter.vue:340
msgid "Not In"
msgstr "Није у"

#: frontend/src/components/Filter.vue:275
#: frontend/src/components/Filter.vue:286
#: frontend/src/components/Filter.vue:296
#: frontend/src/components/Filter.vue:324
#: frontend/src/components/Filter.vue:338
msgid "Not Like"
msgstr "Није слично"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/General/BrandSettings.vue:16
#: frontend/src/components/Settings/General/CurrencySettings.vue:16
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Није сачувано"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:260
msgid "Not allowed to add contact to Deal"
msgstr "Није дозвољено додати контакт у пословну прилику"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:408
msgid "Not allowed to convert Lead to Deal"
msgstr "Није дозвољено пребацити потенцијалног клијента у пословну прилику"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:271
msgid "Not allowed to remove contact from Deal"
msgstr "Није дозвољено уклонити контакт из пословне прилике"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:282
msgid "Not allowed to set primary contact for Deal"
msgstr "Није дозвољено поставити примарни контакт за пословну прилику"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:549
msgid "Note"
msgstr "Белешка"

#: frontend/src/pages/Deal.vue:570 frontend/src/pages/Lead.vue:436
#: frontend/src/pages/MobileDeal.vue:463 frontend/src/pages/MobileLead.vue:370
msgid "Notes"
msgstr "Белешке"

#: frontend/src/pages/Notes.vue:20
msgid "Notes View"
msgstr "Приказ белешки"

#: frontend/src/components/Activities/EmailArea.vue:13
#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Notification"
msgstr "Обавештење"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "Текст обавештења"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "Врста обавештења документа"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "Врста обавештења DocType"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "Обавештења"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Број"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "Бројчани дијаграм"

#: crm/api/dashboard.py:1027 crm/api/dashboard.py:1084
msgid "Number of deals"
msgstr "Број пословних прилика"

#: crm/api/dashboard.py:1077
msgid "Number of deals and total value per salesperson"
msgstr "Број пословних прилика и укупна вредност по продавцу"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Претходна матична"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "На чекању"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "Текуће"

#: crm/api/dashboard.py:181
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "Текуће пословне прилике"

#: frontend/src/utils/index.js:444
msgid "Only image files are allowed"
msgstr "Дозвољени су само фајлови у формату слике"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Може бити постављен само један {0} као примарни."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Отворено"

#: frontend/src/components/Modals/NoteModal.vue:18
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Open Deal"
msgstr "Отворене пословне прилике"

#: frontend/src/components/Modals/NoteModal.vue:19
#: frontend/src/components/Modals/TaskModal.vue:26
msgid "Open Lead"
msgstr "Отворени потенцијални клијент"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "Отвори у порталу"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "Отвори у новом прозору"

#: frontend/src/pages/Organization.vue:92
msgid "Open website"
msgstr "Отвори веб-сајт"

#: frontend/src/components/Kanban/KanbanView.vue:221
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "Опције"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "Или креирајте потенцијалне клијенте ручно"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Наруџбина од"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:548
#: frontend/src/components/Modals/ConvertToDealModal.vue:39
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:505
#: frontend/src/pages/MobileLead.vue:120
#: frontend/src/pages/MobileOrganization.vue:449
#: frontend/src/pages/MobileOrganization.vue:503
#: frontend/src/pages/Organization.vue:458
#: frontend/src/pages/Organization.vue:512
msgid "Organization"
msgstr "Организација"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "Детаљи организације"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "Лого организације"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Назив организације"

#: frontend/src/pages/Deal.vue:69
msgid "Organization logo"
msgstr "Лого организације"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:208
#: frontend/src/pages/Organization.vue:238
msgid "Organizations"
msgstr "Организације"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Other features"
msgstr "Остале функционалности"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Остало"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Outbound Call"
msgstr "Одлазни позив"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Одлазни"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Власник"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "Матична CRM територија"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Лозинка"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "Лозинка не може бити ресетована од стране демо корисника {}"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "Лозинка је обавезна"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "Лозинка је успешно ажурирана"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:41
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:41
msgid "Payment Reminder"
msgstr "Подсетник за плаћање"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:72
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:72
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Подсетник за плаћање од Frappé - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "На чекању"

#: frontend/src/components/Settings/InviteUserPage.vue:66
msgid "Pending Invites"
msgstr "Позиви на чекању"

#: frontend/src/pages/Dashboard.vue:79
msgid "Period"
msgstr "Период"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "Особа"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:498
#: frontend/src/pages/Organization.vue:507
msgid "Phone"
msgstr "Телефон"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "Бројеви телефона"

#: frontend/src/components/ViewControls.vue:1138
msgid "Pin View"
msgstr "Фиксирај приказ"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "Фиксирано"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "Фиксирани прикази"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Pinned view"
msgstr "Фиксирани приказ"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "Брзина репродукције"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "Молимо Вас да додате имејл налог како бисте наставили."

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "Молимо Вас да омогућите Twilio подешавања пре него што позовете."

#: frontend/src/components/FilesUploader/FilesUploader.vue:168
msgid "Please enter a valid URL"
msgstr "Молимо Вас да унесете важећи URL"

#: frontend/src/components/Settings/General/CurrencySettings.vue:159
msgid "Please enter the Exchangerate Host access key."
msgstr "Молимо Вас да унесете Exchangerate Host кључ за приступ."

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "Молимо Вас да наведете разлог означавања ове пословне прилике као изгубљене"

#: frontend/src/components/Settings/General/CurrencySettings.vue:152
msgid "Please select a currency before saving."
msgstr "Молимо Вас да изаберете валуту пре чувања."

#: frontend/src/components/Modals/ConvertToDealModal.vue:145
#: frontend/src/pages/MobileLead.vue:434
msgid "Please select an existing contact"
msgstr "Молимо Вас да изаберете постојећи контакт"

#: frontend/src/components/Modals/ConvertToDealModal.vue:150
#: frontend/src/pages/MobileLead.vue:439
msgid "Please select an existing organization"
msgstr "Молимо Вас да изаберете постојећу организацију"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "Молимо Вас да поставите Exotel интеграцију"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:169
msgid "Please specify a reason for losing the deal."
msgstr "Молимо Вас да наведете разлог губитка пословне прилике."

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Please specify the reason for losing the deal."
msgstr "Молимо Вас да прецизирате разлог губитка пословне прилике."

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "Позиција"

#: frontend/src/pages/Deal.vue:222 frontend/src/pages/MobileDeal.vue:151
msgid "Primary"
msgstr "Примарна"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "Примарни имејл"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "Примарни број мобилног телефона"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "Примарни телефон"

#: frontend/src/pages/Deal.vue:677 frontend/src/pages/MobileDeal.vue:568
msgid "Primary contact set"
msgstr "Примарни контакт је постављен"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Приоритети"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Priority"
msgstr "Приоритет"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Приватно"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Вероватноћа"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Производ"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "Шифра производа"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "Назив производа"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Производи"

#: frontend/src/components/Layouts/AppSidebar.vue:535
#: frontend/src/components/Settings/Settings.vue:79
msgid "Profile"
msgstr "Профил"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "Профил је успешно ажуриран"

#: crm/api/dashboard.py:667
msgid "Projected vs actual revenue based on deal probability"
msgstr "Очекивани наспрам стварних прихода на основу вероватноће пословне прилике"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "Јавни"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "Јавни прикази"

#: frontend/src/components/Layouts/AppSidebar.vue:565
msgid "Public view"
msgstr "Јавни приказ"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Количина"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "У реду"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Брзи унос"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Брзи филтери"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "Брзи филтери су успешно ажурирани"

#: frontend/src/components/Layouts/AppSidebar.vue:589
msgid "Quick entry layout"
msgstr "Распоред за брзи унос"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Јединична цена"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Прочитано"

#: crm/api/dashboard.py:886
msgid "Reason"
msgstr "Разлог"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "Забележи позиве"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "Забележи одлазне позиве"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "Забележити URL"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "Референца документа"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "DocType референца"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Врста референтног документа"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Назив референце"

#: frontend/src/components/ViewControls.vue:25
#: frontend/src/components/ViewControls.vue:160
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Освежи"

#: frontend/src/components/Telephony/TwilioCallUI.vue:104
msgid "Reject"
msgstr "Одбиј"

#: frontend/src/components/Settings/Users.vue:210
#: frontend/src/components/Settings/Users.vue:213
#: frontend/src/pages/Deal.vue:626
msgid "Remove"
msgstr "Уклони"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "Уклони све"

#: frontend/src/components/FieldLayoutEditor.vue:444
msgid "Remove and move fields to previous column"
msgstr "Уклони и премести у поља у претходну колону"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove column"
msgstr "Уклони колону"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:101
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:47
msgid "Remove image"
msgstr "Уклони слику"

#: frontend/src/components/FieldLayoutEditor.vue:365
msgid "Remove section"
msgstr "Уклони одељак"

#: frontend/src/components/FieldLayoutEditor.vue:324
msgid "Remove tab"
msgstr "Уклони картицу"

#: frontend/src/components/Activities/EmailArea.vue:31
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Одговори"

#: frontend/src/components/Activities/EmailArea.vue:44
msgid "Reply All"
msgstr "Одговори свима"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Пријави проблем"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "Обавезна поља"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:82
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Ресетуј"

#: frontend/src/components/ColumnSettings.vue:82
msgid "Reset Changes"
msgstr "Ресетуј измене"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "Ресетуј ERPNext скрипту обрасца"

#: frontend/src/components/ColumnSettings.vue:93
msgid "Reset to Default"
msgstr "Ресетуј на подразумевано"

#: frontend/src/pages/Dashboard.vue:34
msgid "Reset to default"
msgstr "Врати на подразумевано"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Одговорио"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Детаљи одговора"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "Одговор и праћење"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Врати"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "Врати подразумевано"

#: frontend/src/components/FilesUploader/FilesUploader.vue:54
msgid "Retake"
msgstr "Понови"

#: crm/api/dashboard.py:675
msgid "Revenue"
msgstr "Приход"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:84
msgid "Rich Text"
msgstr "Богати текст"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Десно"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Звоњење"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Ringing..."
msgstr "Звоњење..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:44
msgid "Role"
msgstr "Улога"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "Путања"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "Назив путање"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "Редови"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "Споразум о нивоу услуге"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "Креирање споразума о нивоу услуге"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "Назив споразума о нивоу услуге"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "Статус споразума о нивоу услуге"

#: frontend/src/components/EmailEditor.vue:82
msgid "SUBJECT"
msgstr "НАСЛОВ"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Sales Manager"
msgstr "Менаџер продаје"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:90
#: frontend/src/components/Settings/InviteUserPage.vue:170
#: frontend/src/components/Settings/InviteUserPage.vue:177
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:186
#: frontend/src/components/Settings/Users.vue:268
#: frontend/src/components/Settings/Users.vue:271
msgid "Sales User"
msgstr "Корисник продаје"

#: crm/api/dashboard.py:598
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "Тренд продаје"

#: frontend/src/pages/Dashboard.vue:106
msgid "Sales user"
msgstr "Корисник продаје"

#: crm/api/dashboard.py:1079
msgid "Salesperson"
msgstr "Продавац"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Поздрав"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Субота"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:87
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/DropdownItem.vue:21
#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/Settings/General/CurrencySettings.vue:182
#: frontend/src/components/Telephony/ExotelCallUI.vue:231
#: frontend/src/components/ViewControls.vue:123
#: frontend/src/pages/Dashboard.vue:45
msgid "Save"
msgstr "Сачувај"

#: frontend/src/components/Modals/ViewModal.vue:13
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:157
msgid "Save Changes"
msgstr "Сачувај промене"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "Сачувани прикази"

#: frontend/src/components/Layouts/AppSidebar.vue:564
msgid "Saved view"
msgstr "Сачувани приказ"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "Закажите задатак..."

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "Скрипта"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "Претрага шаблона"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "Претрага корисника"

#: frontend/src/components/FieldLayoutEditor.vue:342
msgid "Section"
msgstr "Одељак"

#: frontend/src/pages/Dashboard.vue:59
msgid "Select Range"
msgstr "Изабери опсег"

#: frontend/src/components/Settings/General/CurrencySettings.vue:58
msgid "Select currency"
msgstr "Изабери валуту"

#: frontend/src/components/Settings/General/CurrencySettings.vue:82
msgid "Select provider"
msgstr "Изаберите провајдера"

#: frontend/src/components/FieldLayout/Field.vue:332
msgid "Select {0}"
msgstr "Изаберите {0}"

#: frontend/src/components/EmailEditor.vue:162
msgid "Send"
msgstr "Пошаљи"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "Пошаљите позивнице"

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "Send Template"
msgstr "Пошаљите шаблон"

#: frontend/src/pages/Deal.vue:93 frontend/src/pages/Lead.vue:144
msgid "Send an email"
msgstr "Пошаљите као имејл"

#: frontend/src/components/Layouts/AppSidebar.vue:455
msgid "Send email"
msgstr "Пошаљи имејл"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "Пошаљи позивнице за"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Сепаратор"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Серија"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "Пружалац услуге"

#: frontend/src/components/Layouts/AppSidebar.vue:576
msgid "Service level agreement"
msgstr "Споразум о нивоу услуге"

#: frontend/src/components/FilesUploader/FilesUploader.vue:69
msgid "Set all as private"
msgstr "Постави све као приватно"

#: frontend/src/components/FilesUploader/FilesUploader.vue:62
msgid "Set all as public"
msgstr "Постави све као јавно"

#: frontend/src/pages/Deal.vue:80
msgid "Set an organization"
msgstr "Постави организацију"

#: frontend/src/pages/Deal.vue:634 frontend/src/pages/MobileDeal.vue:525
msgid "Set as Primary Contact"
msgstr "Постави као примарни контакт"

#: frontend/src/components/ViewControls.vue:1123
msgid "Set as default"
msgstr "Постави као подразумевано"

#: frontend/src/components/Settings/General/CurrencySettings.vue:173
msgid "Set currency"
msgstr "Постави валуту"

#: frontend/src/pages/Lead.vue:122
msgid "Set first name"
msgstr "Постави име"

#: frontend/src/components/Layouts/AppSidebar.vue:528
msgid "Setting up"
msgstr "Подешавање"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "Подешавање Frappe Mail захтева да имате API кључ и API тајну за свој имејл налог. Сазнајте више "

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Подешавање Gmail-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Подешавање Outlook-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Подешавање Sendgrid-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Подешавање SparkPost-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Подешавање Yahoo-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Подешавање Yandex-a захтева укључивање двофакторске аутентификације и лозинки за специфичне апликације. Сазнајте више "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:532
#: frontend/src/components/Settings/Settings.vue:11
#: frontend/src/components/Settings/Settings.vue:75
msgid "Settings"
msgstr "Подешавања"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "Подесите имејл"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "Подесите провајдера девизних курсева као 'Exchangerate Host' у подешавањима, јер подразумевани провајдер не подржава конверзију валута из {0} у {1}."

#: frontend/src/components/Layouts/AppSidebar.vue:334
msgid "Setup your password"
msgstr "Поставите Вашу лозинку"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Show"
msgstr "Прикажи"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "Прикажи лозинку"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Show border"
msgstr "Прикажи ивицу"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Show label"
msgstr "Прикажи ознаку"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "Прикажи приказ"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "Бочни панел"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Ставке бочне траке"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "Једноставни python израз, на пример: doc.status == 'Open' and doc.lead_source == 'Ads'"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:22
#: frontend/src/components/SortBy.vue:240
msgid "Sort"
msgstr "Сортирај"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Modals/EditValueModal.vue:10
msgid "Source"
msgstr "Извор"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "Source Name"
msgstr "Назив извора"

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "Размак"

#: crm/api/dashboard.py:731 crm/api/dashboard.py:790
msgid "Stage"
msgstr "Фаза"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "Стандардне скрипте обрасца не могу да се мењају, уместо тога неопходан је дупликат скрипти."

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Стандардна продајна цена"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "Стандардни прикази"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Датум почетка"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Start Time"
msgstr "Време почетка"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "Започни са 10 пробних потенцијалних клијената"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:516
#: frontend/src/pages/MobileOrganization.vue:460
#: frontend/src/pages/Organization.vue:469
msgid "Status"
msgstr "Статус"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "Евиденција промена статуса"

#: frontend/src/components/Modals/DealModal.vue:217
#: frontend/src/components/Modals/LeadModal.vue:158
msgid "Status is required"
msgstr "Статус је обавезан"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Поддомен"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:71
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:71
msgid "Subject"
msgstr "Наслов"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:161
msgid "Subject is required"
msgstr "Наслов је неопходан"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "Наслов: {0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Недеља"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "Подршка / Продаја"

#: frontend/src/components/FilesUploader/FilesUploader.vue:49
msgid "Switch camera"
msgstr "Промени камеру"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "Синхронизуј своје контакте, имејлове и календаре"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "System Manager"
msgstr "Систем менаџер"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "ЗА"

#: frontend/src/components/Telephony/ExotelCallUI.vue:151
msgid "Take a note..."
msgstr "Запиши белешку..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:550
msgid "Task"
msgstr "Задатак"

#: frontend/src/pages/Deal.vue:565 frontend/src/pages/Lead.vue:431
#: frontend/src/pages/MobileDeal.vue:458 frontend/src/pages/MobileLead.vue:365
msgid "Tasks"
msgstr "Задаци"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Telegram канал"

#: frontend/src/components/Settings/Settings.vue:123
msgid "Telephony"
msgstr "Телефонија"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "Средство телефоније"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "Подешавање телефоније"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:178
msgid "Template created successfully"
msgstr "Шаблон је успешно креиран"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:214
msgid "Template deleted successfully"
msgstr "Шаблон је успешно обрисан"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:198
msgid "Template disabled successfully"
msgstr "Шаблон је успешно онемогућен"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template enabled successfully"
msgstr "Шаблон је успешно омогућен"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "Назив шаблона"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:243
msgid "Template renamed successfully"
msgstr "Шаблон је успешно преименован"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
msgid "Template updated successfully"
msgstr "Шаблон је успешно ажуриран"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "Територије"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1022 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Територија"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Менаџер територије"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Назив територије"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "Услов '{0}' је неважећи: {1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Курс који се уноси за конвертовање валуте пословне прилике у основну валуту из Вашег CRM-a (постављено у CRM подешавањима). Поставља се једном, приликом додавања валуте и не мења се аутоматски."

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Курс који се користи за конвертовање валуте организације у основну валуту из Вашег CRM-a (постављено у CRM подешавањима). Поставља се једном, приликом додавања валуте и не мења се аутоматски."

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "У табели приоритета може постојати само један подразумевани приоритет"

#: frontend/src/components/Modals/AddressModal.vue:129
#: frontend/src/components/Modals/CallLogModal.vue:132
msgid "These fields are required: {0}"
msgstr "Ова поља су неопходна: {0}"

#: frontend/src/components/Filter.vue:644
msgid "This Month"
msgstr "Овај месец"

#: frontend/src/components/Filter.vue:648
msgid "This Quarter"
msgstr "Овај квартал"

#: frontend/src/components/Filter.vue:640
msgid "This Week"
msgstr "Ове недеље"

#: frontend/src/components/Filter.vue:652
msgid "This Year"
msgstr "Ове године"

#: frontend/src/components/SidePanelLayoutEditor.vue:119
msgid "This section is not editable"
msgstr "Овај одељак се не може уредити"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "Ово ће обрисати изабране ставке и повезане ставке са њима, да ли сте сигурни?"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "Ово ће обрисати изабране ставке и поништити повезане ставке са њима, да ли сте сигурни?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "Ова опција ће вратити (уколико не постоје) све подразумеване статусе, прилагођена поља и распореде. Обриши и врати ће обрисати подразумеване распореде и потом их поново креирати."

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Четвртак"

#: frontend/src/components/Filter.vue:356
msgid "Timespan"
msgstr "Временски опсег"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:41
msgid "Title"
msgstr "Наслов"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:32
msgid "Title Field"
msgstr "Поље за наслов"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:63
msgid "To"
msgstr "За"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Датум завршетка"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "До врсте"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "За корисника"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "Да бисте сазнали више о подешавањима имејл налога, кликните"

#: frontend/src/components/Filter.vue:632
msgid "Today"
msgstr "Данас"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "За урадити"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "Пребаци за приказ"

#: frontend/src/components/Filter.vue:636
msgid "Tomorrow"
msgstr "Сутра"

#: frontend/src/components/Modals/NoteModal.vue:37
#: frontend/src/components/Modals/TaskModal.vue:59
msgid "Took a call with John Doe and discussed the new project."
msgstr "Обављен позив са Петром Петровићем и разговарано о новом пројекту."

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "Укупно"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Укупно празника"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "Укупно након попуста"

#: crm/api/dashboard.py:123
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "Укупно потенцијалних клијената"

#: crm/api/dashboard.py:124
msgid "Total number of leads"
msgstr "Укупан број потенцијалних клијената"

#: crm/api/dashboard.py:182
msgid "Total number of non won/lost deals"
msgstr "Укупан број пословних прилика које нису добијене нити изгубљене"

#: crm/api/dashboard.py:297
msgid "Total number of won deals based on its closure date"
msgstr "Укупан број добијених пословних прилика према датуму затварања"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Уторак"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:596
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Грешка приликом креирања Twilio API креденцијала."

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio број"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio није омогућен"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio подешавања су успешно ажурирана"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Type"
msgstr "Врста"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:73
#: frontend/src/components/Controls/MultiSelectUserInput.vue:73
msgid "Type an email address to invite"
msgstr "Унесите имејл адресу за позивање"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "Овде унесите Вашу поруку..."

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "Неовлашћени захтев"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Uncollapsible"
msgstr "Сажимање није могуће"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:130
msgid "Unknown"
msgstr "Непознат"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "Поништи повезивање"

#: frontend/src/components/DeleteLinkedDocModal.vue:77
msgid "Unlink all"
msgstr "Поништи сва повезивања"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "Поништи повезивање и обриши"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "Поништи повезивање и обриши {0} ставки"

#: frontend/src/components/DeleteLinkedDocModal.vue:242
msgid "Unlink linked item"
msgstr "Поништи повезивање повезане ставке"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink {0} item(s)"
msgstr "Поништи повезивање {0} ставки"

#: frontend/src/components/ViewControls.vue:1138
msgid "Unpin View"
msgstr "Уклони фиксирање приказа"

#: frontend/src/components/ViewControls.vue:975
msgid "Unsaved Changes"
msgstr "Несачуване промене"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "Без наслова"

#: frontend/src/components/ColumnSettings.vue:138
#: frontend/src/components/Modals/AssignmentModal.vue:17
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:17
#: frontend/src/components/Settings/General/BrandSettings.vue:23
#: frontend/src/components/Settings/General/CurrencySettings.vue:23
#: frontend/src/components/Settings/General/HomeActions.vue:17
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:219
#: frontend/src/components/ViewControls.vue:980
msgid "Update"
msgstr "Ажурирај"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "Ажурирај налог"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "Ажурирај {0} записа"

#: frontend/src/components/FilesUploader/FilesUploader.vue:86
msgid "Upload"
msgstr "Отпреми"

#: frontend/src/components/Activities/Activities.vue:404
#: frontend/src/components/Activities/ActivityHeader.vue:62
#: frontend/src/components/Activities/ActivityHeader.vue:158
msgid "Upload Attachment"
msgstr "Отпреми прилог"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "Отпреми документ"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "Отпреми слику"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "Отпреми видео"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:42 frontend/src/pages/Lead.vue:96
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:42
msgid "Upload image"
msgstr "Отпреми слику"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Корисник"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Корисничко име"

#: frontend/src/components/Settings/Users.vue:301
msgid "User {0} has been removed"
msgstr "Корисник {0} је уклоњен"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:95
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Корисници"

#: frontend/src/components/Modals/AddExistingUserModal.vue:103
msgid "Users added successfully"
msgstr "Корисници су успешно додати"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Важење"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Вредност"

#: frontend/src/components/Modals/ViewModal.vue:25
msgid "View Name"
msgstr "Назив приказа"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Views"
msgstr "Прикази"

#: frontend/src/components/Layouts/AppSidebar.vue:558
msgid "Web form"
msgstr "Веб-образац"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Токе за верификацију webhook-a"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Веб-сајт"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Среда"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Недељни одмор"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "Порука добродошлице"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "Добро дошли {0}, хајде да додамо твог првог потенцијалног клијента"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:598
#: frontend/src/components/Settings/Settings.vue:129
#: frontend/src/pages/Deal.vue:580 frontend/src/pages/Lead.vue:446
#: frontend/src/pages/MobileDeal.vue:473 frontend/src/pages/MobileLead.vue:380
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp шаблони"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "Where"
msgstr "Где"

#: frontend/src/components/ColumnSettings.vue:117
msgid "Width"
msgstr "Ширина"

#: frontend/src/components/ColumnSettings.vue:122
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "Ширина може бити број, пиксел или rem (eg. 3, 30px, 10rem)"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "Добијено"

#: crm/api/dashboard.py:296
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "Добијене пословне прилике"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Радни дан"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Радно време"

#: frontend/src/components/Filter.vue:628
msgid "Yesterday"
msgstr "Јуче"

#: crm/api/whatsapp.py:36 crm/api/whatsapp.py:216 crm/api/whatsapp.py:230
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Ви"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "Немате дозволу да приступите овом ресурсу."

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "Можете променити подразумевано средство за позивање у подешавањима"

#: frontend/src/components/Settings/General/CurrencySettings.vue:107
msgid "You can get your access key from "
msgstr "Свој кључ за приступ можете добити са "

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "Немате подешен Exotel број у свом агенту телефоније"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "Немате подешен број мобилног телефона у свом агенту телефоније"

#: frontend/src/data/document.js:32
msgid "You do not have permission to access this document"
msgstr "Немате дозволу за приступ овом документу"

#: frontend/src/components/ViewControls.vue:976
msgid "You have unsaved changes. Do you want to save them?"
msgstr "Имате несачуване промене. Да ли желите да их сачувате?"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "Морате бити у развојном режиму да бисте уредили стандардну скрипту обрасца"

#: crm/api/todo.py:111
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "Ваше задужење на задатку {0} је уклоњено од стране {1}"

#: crm/api/todo.py:46 crm/api/todo.py:89
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Ваша додела за {0} {1} је уклоњена од стране {2}"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "додато је"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "амбер"

#: crm/api/todo.py:120
msgid "assigned a new task {0} to you"
msgstr "додељен Вам је нови задатак {0}"

#: crm/api/todo.py:100
msgid "assigned a {0} {1} to you"
msgstr "додељен Вам је {0} {1}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "црна"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "плаво"

#: frontend/src/components/Activities/Activities.vue:232
msgid "changes from"
msgstr "измене из"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "коментар"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "цијан"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:268
#: frontend/src/components/Controls/MultiSelectUserInput.vue:242
msgid "email already exists"
msgstr "имејл већ постоји"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:113
msgid "exchangerate.host"
msgstr "exchangerate.host"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "frankfurter.app"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "сива"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "зелена"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "group_by"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "је обавио позив"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "је ступио у контакт"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "овде"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "за 1 час"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "за 1 минут"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "за 1 годину"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "за {0} М"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "за {0} d"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "за {0} дана"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "за {0} h"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "за {0} часова"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "за {0} m"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "за {0} минута"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "за {0} месеци"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "за {0} w"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "за {0} недеља"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "за {0} y"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "за {0} година"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
#: frontend/src/components/Settings/InviteUserPage.vue:37
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "управо сада"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "канбан"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "ознака"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "листа"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:65
#: frontend/src/pages/MobileNotification.vue:52
msgid "mentioned you in {0}"
msgstr "Вас је поменуо у {0}"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "next"
msgstr "следећи"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "сада"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "наранџаста"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "розе"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "previous"
msgstr "претходно"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "private"
msgstr "приватно"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "public"
msgstr "јавно"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "пурпурна"

#: crm/api/whatsapp.py:37
msgid "received a whatsapp message in {0}"
msgstr "примљена WhatsApp порука у {0}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "црвена"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "тиркизна"

#: frontend/src/components/Activities/Activities.vue:274
#: frontend/src/components/Activities/Activities.vue:337
msgid "to"
msgstr "ка"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "сутра"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "љубичаста"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "жута"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "јуче"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} М"

#: crm/api/todo.py:50
msgid "{0} assigned a {1} {2} to you"
msgstr "Додељено Вам је {1} {2} од стране {0}"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} d"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "пре {0} дана"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0} h"

#: frontend/src/components/Settings/Users.vue:291
msgid "{0} has been granted {1} access"
msgstr "{0} је додељен приступ са улогом {1}"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "пре {0} часова"

#: frontend/src/components/EmailEditor.vue:29
#: frontend/src/components/EmailEditor.vue:64
#: frontend/src/components/EmailEditor.vue:77
#: frontend/src/components/Modals/AddExistingUserModal.vue:36
#: frontend/src/components/Settings/InviteUserPage.vue:41
msgid "{0} is an invalid email address"
msgstr "{0} је неважећа имејл адреса"

#: frontend/src/components/Modals/ConvertToDealModal.vue:181
msgid "{0} is required"
msgstr "{0} је обавезан"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} m"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "пре {0} минута"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "пре {0} месеци"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} w"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "пре {0} недеља"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0} y"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "пре {0} година"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "Избегавајте коришћење \"trigger\" као назива поља - у сукобу је са уграђеном trigger() методом."

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "Метода \"{0}\" није пронађена у класи."

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "Ниједна класа није пронађена за DocType: {0}, обавезно мора постојати класа за матични DocType. Може бити празна, али мора постојати."

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "Нема података за матично поље: {0}"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "Ниједан ред није пронађен за idx: {0} у матичном пољу: {1}"

