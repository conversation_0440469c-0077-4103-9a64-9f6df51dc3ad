msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-08-03 09:38+0000\n"
"PO-Revision-Date: 2025-08-04 08:34\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Chinese Simplified\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: zh_CN\n"

#: frontend/src/components/ViewControls.vue:1217
msgid " (New)"
msgstr "（新建）"

#: frontend/src/components/Modals/TaskModal.vue:99
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "2024年4月1日 下午11:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "1小时前"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "1分钟前"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "一个月前"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "一周前"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "一年前"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>元数据</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>快捷方式</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:98
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:98
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "msgstr \"<p>尊敬的{{ lead_name }}：</p>\\n\\n<p>特此提醒您尚有{{ grand_total }}款项待支付。</p>\\n\\n<p>此致</p>\\n<p>Frappé团队</p>\""

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>门户</b></span>"

#: frontend/src/components/CommunicationArea.vue:85
msgid "@John, can you please check this?"
msgstr "@John，请确认此项？"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:56
msgid "A Lead requires either a person's name or an organization's name"
msgstr "线索需填写个人姓名或组织名称"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Key"
msgstr "API密钥"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr ""

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "API Secret"
msgstr "API密钥"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API令牌"

#: frontend/src/components/Telephony/TwilioCallUI.vue:92
msgid "Accept"
msgstr "接受"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "接受邀请"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "已接受"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "接受时间"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "访问密钥"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "服务商{0}必须提供访问密钥"

#: frontend/src/components/Settings/General/CurrencySettings.vue:97
msgid "Access key"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:101
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "科目名称"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "账户SID"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr ""

#: frontend/src/components/CustomActions.vue:73
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1109
msgid "Actions"
msgstr "操作"

#: frontend/src/pages/Deal.vue:540 frontend/src/pages/Lead.vue:406
#: frontend/src/pages/MobileDeal.vue:432 frontend/src/pages/MobileLead.vue:339
msgid "Activity"
msgstr "活动"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:53
msgid "Add"
msgstr "添加"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:69
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "添加列"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:58
#: frontend/src/components/FieldLayoutEditor.vue:173
#: frontend/src/components/Kanban/KanbanSettings.vue:84
#: frontend/src/components/SidePanelLayoutEditor.vue:98
msgid "Add Field"
msgstr "添加字段"

#: frontend/src/components/Filter.vue:138
msgid "Add Filter"
msgstr "添加筛选器"

#: frontend/src/components/Controls/Grid.vue:321
msgid "Add Row"
msgstr "添加行"

#: frontend/src/components/FieldLayoutEditor.vue:200
#: frontend/src/components/SidePanelLayoutEditor.vue:130
msgid "Add Section"
msgstr "添加区块"

#: frontend/src/components/SortBy.vue:148
msgid "Add Sort"
msgstr "添加排序"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "添加标签页"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "添加每周假期"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:426
msgid "Add column"
msgstr "添加列"

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "添加描述..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr ""

#: frontend/src/components/ViewControls.vue:104
msgid "Add filter"
msgstr "添加筛选器"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "添加备注"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr ""

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "添加SVG代码或使用Feather图标（如'settings'）"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "添加任务"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "添加至假期"

#: frontend/src/components/Layouts/AppSidebar.vue:434
msgid "Add your first comment"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "地址"

#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:172
#: frontend/src/components/Settings/InviteUserPage.vue:179
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:184
#: frontend/src/components/Settings/Users.vue:244
#: frontend/src/components/Settings/Users.vue:247
msgid "Admin"
msgstr "管理员"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "客服正忙，请稍后再拨。"

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "全部"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:512 frontend/src/pages/MobileContact.vue:510
#: frontend/src/pages/MobileOrganization.vue:454
#: frontend/src/pages/Organization.vue:463
msgid "Amount"
msgstr "金额"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr ""

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr ""

#: frontend/src/data/document.js:63
msgid "An error occurred while updating the document"
msgstr ""

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "ICO格式图标文件，尺寸16x16像素，需通过favicon生成器创建。[favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "建议使用1:1和2:1比例的图像"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "And"
msgstr "且"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "年营业额"

#: frontend/src/components/Modals/DealModal.vue:201
#: frontend/src/components/Modals/LeadModal.vue:142
msgid "Annual Revenue should be a number"
msgstr "年营业额应为数值"

#: frontend/src/components/Settings/General/BrandSettings.vue:69
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "显示于左侧栏，推荐PNG或SVG格式，尺寸32x32像素"

#: frontend/src/components/Settings/General/BrandSettings.vue:103
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "显示于浏览器标签页标题旁，推荐PNG或ICO格式，尺寸32x32像素"

#: frontend/src/components/Kanban/KanbanSettings.vue:107
#: frontend/src/components/Kanban/KanbanView.vue:45
msgid "Apply"
msgstr "应用"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "应用于"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "应用至"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "应用"

#: frontend/src/components/Activities/AttachmentArea.vue:139
msgid "Are you sure you want to delete this attachment?"
msgstr "确认删除此附件？"

#: frontend/src/pages/MobileContact.vue:263
msgid "Are you sure you want to delete this contact?"
msgstr "确认删除此联系人？"

#: frontend/src/pages/MobileOrganization.vue:264
msgid "Are you sure you want to delete this organization?"
msgstr "确认删除此组织？"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "确认删除此任务？"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr ""

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "确定登录Frappe云控制面板？"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "确认重置'从CRM商机生成报价单'表单脚本？"

#: frontend/src/components/Settings/General/CurrencySettings.vue:174
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr ""

#: frontend/src/components/ListBulkActions.vue:184
#: frontend/src/components/Modals/AssignmentModal.vue:5
msgid "Assign To"
msgstr "分配给"

#: frontend/src/components/AssignTo.vue:9
msgid "Assign to"
msgstr "分配至"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "负责人"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "分配"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "分配规则"

#: frontend/src/components/ListBulkActions.vue:152
msgid "Assignment cleared successfully"
msgstr "分配已成功清除"

#: frontend/src/components/Layouts/AppSidebar.vue:577
msgid "Assignment rule"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:176
msgid "At least one field is required"
msgstr "至少需填写一个字段"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:76
msgid "Attach"
msgstr "附加"

#: frontend/src/pages/Deal.vue:117 frontend/src/pages/Lead.vue:174
msgid "Attach a file"
msgstr "附加文件"

#: frontend/src/pages/Deal.vue:575 frontend/src/pages/Lead.vue:441
#: frontend/src/pages/MobileDeal.vue:468 frontend/src/pages/MobileLead.vue:375
msgid "Attachments"
msgstr "附件"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "认证令牌"

#: crm/api/dashboard.py:238
msgid "Average deal value of non won/lost deals"
msgstr ""

#: crm/api/dashboard.py:411
msgid "Average deal value of ongoing & won deals"
msgstr ""

#: crm/api/dashboard.py:354
msgid "Average deal value of won deals"
msgstr ""

#: crm/api/dashboard.py:518
msgid "Average time taken from deal creation to deal closure"
msgstr ""

#: crm/api/dashboard.py:464
msgid "Average time taken from lead creation to deal closure"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr ""

#: crm/api/dashboard.py:410
msgid "Avg. deal value"
msgstr ""

#: crm/api/dashboard.py:237
msgid "Avg. ongoing deal value"
msgstr ""

#: crm/api/dashboard.py:517
msgid "Avg. time to close a deal"
msgstr ""

#: crm/api/dashboard.py:463
msgid "Avg. time to close a lead"
msgstr ""

#: crm/api/dashboard.py:353
msgid "Avg. won deal value"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr ""

#: frontend/src/components/Activities/EmailArea.vue:72
#: frontend/src/components/EmailEditor.vue:44
#: frontend/src/components/EmailEditor.vue:69
msgid "BCC"
msgstr "密送"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "返回"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "返回文件上传"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "待办事项"

#: frontend/src/components/Filter.vue:355
msgid "Between"
msgstr "介于"

#: frontend/src/components/Settings/General/BrandSettings.vue:40
msgid "Brand name"
msgstr ""

#: frontend/src/components/Settings/General/BrandSettings.vue:9
msgid "Brand settings"
msgstr ""

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "品牌标识"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "批量编辑"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "忙碌"

#: frontend/src/components/Activities/EmailArea.vue:67
#: frontend/src/components/EmailEditor.vue:34
#: frontend/src/components/EmailEditor.vue:56
msgid "CC"
msgstr "抄送"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "CRM通话记录"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "CRM沟通状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "CRM联系人"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:318
msgid "CRM Dashboard"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "CRM商机"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "CRM商机状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "CRM下拉项"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "CRM Exotel设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "CRM字段布局"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "CRM表单脚本"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "CRM全局设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "CRM假期"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "CRM假期列表"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "CRM行业"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "CRM邀请"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "CRM线索"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "CRM线索来源"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "CRM线索状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "CRM通知"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "CRM组织"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "CRM门户页面"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "CRM服务日"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "CRM服务级别协议"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "CRM服务级别优先级"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "CRM状态变更日志"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "CRM任务"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "CRM电话客服"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "CRM电话设备"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "CRM区域"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "CRM Twilio设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "CRM视图设置"

#: frontend/src/components/Settings/General/CurrencySettings.vue:42
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr ""

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "通话详情"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "接听人"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "通话时长（秒）"

#: frontend/src/components/Layouts/AppSidebar.vue:551
msgid "Call log"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "使用{0}呼叫"

#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:43
msgid "Call with John Doe"
msgstr "与John Doe通话"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "主叫方"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "呼叫媒介"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Calling..."
msgstr "呼叫中..."

#: frontend/src/pages/Deal.vue:560 frontend/src/pages/Lead.vue:426
#: frontend/src/pages/MobileDeal.vue:452 frontend/src/pages/MobileLead.vue:359
msgid "Calls"
msgstr "通话记录"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "摄像头"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/ColumnSettings.vue:132
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:114
#: frontend/src/components/Modals/AssignmentModal.vue:9
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:77
#: frontend/src/components/ViewControls.vue:56
#: frontend/src/components/ViewControls.vue:156
#: frontend/src/pages/Dashboard.vue:41
msgid "Cancel"
msgstr "取消"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "已取消"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "无法删除标准项{0}"

#: frontend/src/components/FilesUploader/FilesUploader.vue:94
msgid "Capture"
msgstr "捕获"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Capturing leads"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:485
msgid "Change"
msgstr "更改"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "修改密码"

#: frontend/src/components/Activities/TaskArea.vue:44
msgid "Change Status"
msgstr "变更状态"

#: frontend/src/components/Layouts/AppSidebar.vue:476
#: frontend/src/components/Layouts/AppSidebar.vue:484
msgid "Change deal status"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:41 frontend/src/pages/Lead.vue:95
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:41
msgid "Change image"
msgstr "更换图片"

#: frontend/src/pages/Dashboard.vue:28
msgid "Chart"
msgstr "图表"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "图表类型"

#: frontend/src/components/Modals/ConvertToDealModal.vue:43
#: frontend/src/components/Modals/ConvertToDealModal.vue:69
#: frontend/src/pages/MobileLead.vue:124 frontend/src/pages/MobileLead.vue:151
msgid "Choose Existing"
msgstr "选择现有项"

#: frontend/src/components/Modals/DealModal.vue:45
msgid "Choose Existing Contact"
msgstr "选择现有联系人"

#: frontend/src/components/Modals/DealModal.vue:38
msgid "Choose Existing Organization"
msgstr "选择现有组织"

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr ""

#: frontend/src/components/Controls/Link.vue:62
msgid "Clear"
msgstr "清除"

#: frontend/src/components/ListBulkActions.vue:134
#: frontend/src/components/ListBulkActions.vue:142
#: frontend/src/components/ListBulkActions.vue:188
msgid "Clear Assignment"
msgstr "清除分配"

#: frontend/src/components/SortBy.vue:160
msgid "Clear Sort"
msgstr "清除排序"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "清空表格"

#: frontend/src/components/Filter.vue:18 frontend/src/components/Filter.vue:150
msgid "Clear all Filter"
msgstr "清除所有筛选"

#: frontend/src/components/Notifications.vue:28
msgid "Close"
msgstr "关闭"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "折叠"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Collapsible"
msgstr "可折叠"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "颜色"

#: frontend/src/components/FieldLayoutEditor.vue:423
msgid "Column"
msgstr "列"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:15
msgid "Column Field"
msgstr "列字段"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "列"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:80
#: frontend/src/components/CommunicationArea.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:574
msgid "Comment"
msgstr "评论"

#: frontend/src/pages/Deal.vue:550 frontend/src/pages/Lead.vue:416
#: frontend/src/pages/MobileDeal.vue:442 frontend/src/pages/MobileLead.vue:349
msgid "Comments"
msgstr "评论"

#: crm/api/dashboard.py:884
msgid "Common reasons for losing deals"
msgstr ""

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "沟通状态"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "沟通状态列表"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "ERPNext站点中的公司"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "已完成"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "计算机"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "条件"

#: frontend/src/components/Settings/General/GeneralSettings.vue:8
msgid "Configure general settings for your CRM"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:70
msgid "Configure the exchange rate provider for your CRM"
msgstr ""

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "确认"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:250
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:253
msgid "Confirm Delete"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "确认密码"

#: frontend/src/components/Settings/Users.vue:225
#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr ""

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr ""

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:547
#: frontend/src/components/Modals/ConvertToDealModal.vue:65
#: frontend/src/pages/MobileLead.vue:147
msgid "Contact"
msgstr "联系人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:212
msgid "Contact Already Exists"
msgstr "联系人已存在"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "联系我们"

#: frontend/src/pages/Deal.vue:655 frontend/src/pages/MobileDeal.vue:546
msgid "Contact added"
msgstr "联系人已添加"

#: frontend/src/pages/Deal.vue:645 frontend/src/pages/MobileDeal.vue:536
msgid "Contact already added"
msgstr "联系人已存在"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:211
msgid "Contact already exists with {0}"
msgstr "联系人{0}已存在"

#: frontend/src/pages/Contact.vue:282 frontend/src/pages/MobileContact.vue:255
msgid "Contact image updated"
msgstr ""

#: frontend/src/pages/Deal.vue:666 frontend/src/pages/MobileDeal.vue:557
msgid "Contact removed"
msgstr "联系人已移除"

#: frontend/src/pages/Contact.vue:437 frontend/src/pages/Contact.vue:450
#: frontend/src/pages/Contact.vue:463 frontend/src/pages/Contact.vue:473
#: frontend/src/pages/MobileContact.vue:435
#: frontend/src/pages/MobileContact.vue:448
#: frontend/src/pages/MobileContact.vue:461
#: frontend/src/pages/MobileContact.vue:471
msgid "Contact updated"
msgstr ""

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:237 frontend/src/pages/MobileContact.vue:215
#: frontend/src/pages/MobileOrganization.vue:334
msgid "Contacts"
msgstr "联系人"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:34
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:105
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:92
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:105
msgid "Content"
msgstr "内容"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Content Type"
msgstr "内容类型"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:167
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:165
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:169
msgid "Content is required"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:375
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:8
#: frontend/src/pages/MobileLead.vue:56 frontend/src/pages/MobileLead.vue:110
msgid "Convert"
msgstr "转换"

#: frontend/src/components/Layouts/AppSidebar.vue:366
#: frontend/src/components/Layouts/AppSidebar.vue:374
msgid "Convert lead to deal"
msgstr ""

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:195
#: frontend/src/components/Modals/ConvertToDealModal.vue:19
#: frontend/src/pages/Lead.vue:45 frontend/src/pages/MobileLead.vue:106
msgid "Convert to Deal"
msgstr "转换为商机"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "已转换"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "转换成功"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr ""

#: crm/api/dashboard.py:607 crm/api/dashboard.py:736 crm/api/dashboard.py:794
#: crm/api/dashboard.py:891
msgid "Count"
msgstr "计数"

#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/ContactModal.vue:41
#: frontend/src/components/Modals/CreateDocumentModal.vue:93
#: frontend/src/components/Modals/DealModal.vue:67
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/LeadModal.vue:38
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/OrganizationModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Modals/ViewModal.vue:16
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/pages/CallLogs.vue:11 frontend/src/pages/Contacts.vue:13
#: frontend/src/pages/Contacts.vue:60 frontend/src/pages/Deals.vue:13
#: frontend/src/pages/Deals.vue:236 frontend/src/pages/Leads.vue:13
#: frontend/src/pages/Leads.vue:262 frontend/src/pages/Notes.vue:7
#: frontend/src/pages/Notes.vue:93 frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:11
#: frontend/src/pages/Tasks.vue:185
msgid "Create"
msgstr "创建"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "创建商机"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "创建线索"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/SidePanelLayout.vue:137
msgid "Create New"
msgstr "新建"

#: frontend/src/components/Activities/Activities.vue:384
#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Create Note"
msgstr "创建备注"

#: frontend/src/components/Activities/Activities.vue:399
#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Create Task"
msgstr "创建任务"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "创建视图"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "状态变更时创建客户"

#: frontend/src/components/Modals/CallLogDetailModal.vue:152
msgid "Create lead"
msgstr "创建线索"

#: frontend/src/components/Layouts/AppSidebar.vue:344
msgid "Create your first lead"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:414
msgid "Create your first note"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:394
msgid "Create your first task"
msgstr ""

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:38
msgid "Currency"
msgstr "货币"

#: frontend/src/components/Settings/General/CurrencySettings.vue:9
msgid "Currency & Exchange rate provider"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:188
msgid "Currency set as {0} successfully"
msgstr ""

#: crm/api/dashboard.py:839
msgid "Current pipeline distribution"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:586
msgid "Custom actions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:536
msgid "Custom branding"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:585
msgid "Custom fields"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:588
msgid "Custom list actions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:587
msgid "Custom statuses"
msgstr ""

#: frontend/src/pages/Deal.vue:486
msgid "Customer created successfully"
msgstr "客户创建成功"

#: frontend/src/components/Layouts/AppSidebar.vue:582
msgid "Customization"
msgstr "定制"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "自定义快速筛选"

#: crm/api/dashboard.py:599
msgid "Daily performance of leads, deals, and wins"
msgstr ""

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:575
#: frontend/src/pages/Deal.vue:555 frontend/src/pages/Lead.vue:421
#: frontend/src/pages/MobileDeal.vue:447 frontend/src/pages/MobileLead.vue:354
msgid "Data"
msgstr "数据"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "数据字段"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:601 crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "Date"
msgstr "日期"

#: frontend/src/components/Layouts/AppSidebar.vue:546
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:62
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:54
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:62
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:129
msgid "Deal"
msgstr "商机"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "商机负责人"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "商机状态"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "商机状态列表"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr ""

#: crm/api/dashboard.py:977
msgid "Deal generation channel analysis"
msgstr ""

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:531
#: frontend/src/pages/MobileOrganization.vue:475
#: frontend/src/pages/Organization.vue:484
msgid "Deal owner"
msgstr "商机负责人"

#: crm/api/dashboard.py:1030 crm/api/dashboard.py:1087
msgid "Deal value"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:500 frontend/src/pages/MobileContact.vue:291
#: frontend/src/pages/MobileDeal.vue:386
#: frontend/src/pages/MobileOrganization.vue:328
msgid "Deals"
msgstr "商机"

#: crm/api/dashboard.py:788
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr ""

#: crm/api/dashboard.py:1076
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr ""

#: crm/api/dashboard.py:976
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr ""

#: crm/api/dashboard.py:838
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr ""

#: crm/api/dashboard.py:1019
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:115
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:115
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "尊敬的{{ lead_name }}：\\n\\n特此提醒您尚有{{ grand_total }}款项待支付。\\n\\n此致\\nFrappé团队"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "默认"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "默认收件箱"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "默认收件箱"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "默认媒介"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "默认外发"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "默认优先级"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "默认发送账号"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "默认发送和收件账号"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "{0}的默认服务级别协议已存在"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "当前用户的默认呼叫媒介"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "默认呼叫媒介更新成功"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "默认媒介"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "默认状态、自定义字段及布局已成功恢复。"

#: frontend/src/components/Activities/AttachmentArea.vue:142
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:316
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:89
#: frontend/src/components/Kanban/KanbanView.vue:225
#: frontend/src/components/ListBulkActions.vue:177
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:235
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:238
#: frontend/src/components/ViewControls.vue:1161
#: frontend/src/components/ViewControls.vue:1172
#: frontend/src/pages/Contact.vue:103 frontend/src/pages/Deal.vue:124
#: frontend/src/pages/Lead.vue:183 frontend/src/pages/MobileContact.vue:82
#: frontend/src/pages/MobileContact.vue:266
#: frontend/src/pages/MobileDeal.vue:517
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:267
#: frontend/src/pages/Notes.vue:40 frontend/src/pages/Organization.vue:83
#: frontend/src/pages/Tasks.vue:369
msgid "Delete"
msgstr "删除"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "删除并恢复"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "删除任务"

#: frontend/src/components/ViewControls.vue:1157
#: frontend/src/components/ViewControls.vue:1165
msgid "Delete View"
msgstr "删除视图"

#: frontend/src/components/DeleteLinkedDocModal.vue:65
msgid "Delete all"
msgstr ""

#: frontend/src/components/Activities/AttachmentArea.vue:62
#: frontend/src/components/Activities/AttachmentArea.vue:138
msgid "Delete attachment"
msgstr "删除附件"

#: frontend/src/pages/MobileContact.vue:262
msgid "Delete contact"
msgstr "删除联系人"

#: frontend/src/components/DeleteLinkedDocModal.vue:229
msgid "Delete linked item"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr ""

#: frontend/src/pages/MobileOrganization.vue:263
msgid "Delete organization"
msgstr "删除组织"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete {0} item(s)"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr ""

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Modals/TaskModal.vue:49
msgid "Description"
msgstr "描述"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "工作台"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:286
#: frontend/src/pages/MobileDeal.vue:426 frontend/src/pages/MobileLead.vue:333
#: frontend/src/pages/MobileOrganization.vue:323
msgid "Details"
msgstr "详情"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "设备"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "禁用"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "禁用"

#: frontend/src/components/CommentBox.vue:76
#: frontend/src/components/EmailEditor.vue:158
msgid "Discard"
msgstr "放弃"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "折扣 %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "折扣金额"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "文档类型"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "文档类型"

#: frontend/src/data/document.js:28
msgid "Document does not exist"
msgstr ""

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "未找到文档"

#: frontend/src/data/document.js:42
msgid "Document updated successfully"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "用户操作手册"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "完成"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr ""

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "下载"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "拖放文件至此或从本地"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "拖放文件至此"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "下拉选项"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "截止日期"

#: frontend/src/components/Modals/ViewModal.vue:15
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:225
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:228
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:19
#: frontend/src/components/ViewControls.vue:1113
msgid "Duplicate"
msgstr "复制"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "复制视图"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr ""

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "持续时间"

#: frontend/src/components/Layouts/AppSidebar.vue:599
#: frontend/src/components/Settings/Settings.vue:135
msgid "ERPNext"
msgstr "ERPNext"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "ERPNext CRM设置"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "ERPNext站点API"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "ERPNext站点URL"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "当前站点未安装ERPNext"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "ERPNext未与CRM集成"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr ""

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:319
#: frontend/src/components/FieldLayoutEditor.vue:345
#: frontend/src/components/ListBulkActions.vue:170
#: frontend/src/components/ViewControls.vue:1131
#: frontend/src/pages/Dashboard.vue:19
msgid "Edit"
msgstr "编辑"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "Edit Call Log"
msgstr "编辑通话记录"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "编辑数据字段布局"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr ""

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "编辑字段布局"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "编辑网格字段布局"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "编辑网格行字段布局"

#: frontend/src/components/Modals/NoteModal.vue:15
msgid "Edit Note"
msgstr "编辑备注"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "编辑快速录入布局"

#: frontend/src/components/Modals/TaskModal.vue:18
msgid "Edit Task"
msgstr "编辑任务"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "编辑视图"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "编辑备注"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "编辑任务"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "正在编辑第{0}行"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:523
#: frontend/src/pages/MobileContact.vue:521
#: frontend/src/pages/MobileOrganization.vue:465
#: frontend/src/pages/MobileOrganization.vue:493
#: frontend/src/pages/Organization.vue:474
#: frontend/src/pages/Organization.vue:502
msgid "Email"
msgstr "电子邮件"

#: frontend/src/components/Settings/Settings.vue:107
msgid "Email Accounts"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr ""

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "发送时间"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:113
msgid "Email Templates"
msgstr "邮件模板"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr ""

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr ""

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:573
msgid "Email communication"
msgstr ""

#: frontend/src/components/EmailEditor.vue:206
msgid "Email from Lead"
msgstr "来自线索的邮件"

#: frontend/src/components/Layouts/AppSidebar.vue:552
msgid "Email template"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr ""

#: frontend/src/pages/Deal.vue:545 frontend/src/pages/Lead.vue:411
#: frontend/src/pages/MobileDeal.vue:437 frontend/src/pages/MobileLead.vue:344
msgid "Emails"
msgstr "邮件"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "空"

#: frontend/src/components/Filter.vue:124
msgid "Empty - Choose a field to filter by"
msgstr "空 - 选择筛选字段"

#: frontend/src/components/SortBy.vue:134
msgid "Empty - Choose a field to sort by"
msgstr "空 - 选择排序字段"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "启用"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "收邮件"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "启用该邮箱帐号发送邮件"

#: frontend/src/components/Settings/General/GeneralSettings.vue:19
msgid "Enable forecasting"
msgstr ""

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:33
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:33
msgid "Enabled"
msgstr "已启用"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "结束日期"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "End Time"
msgstr "结束时间"

#: frontend/src/components/Settings/General/CurrencySettings.vue:122
msgid "Enter access key"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:334
msgid "Enter {0}"
msgstr "输入{0}"

#: frontend/src/components/Filter.vue:67 frontend/src/components/Filter.vue:100
#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:310
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:332
#: frontend/src/components/Filter.vue:348
msgid "Equals"
msgstr "等于"

#: frontend/src/components/Modals/ConvertToDealModal.vue:185
msgid "Error converting to deal: {0}"
msgstr ""

#: frontend/src/pages/Deal.vue:739 frontend/src/pages/Lead.vue:486
#: frontend/src/pages/MobileDeal.vue:614 frontend/src/pages/MobileLead.vue:415
msgid "Error updating field"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:261
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "ERPNext创建客户失败，请查看错误日志"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:173
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "ERPNext创建潜在客户失败，请查看错误日志"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "ERPNext获取客户失败，请查看错误日志"

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "汇率"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:67
msgid "Exchange rate provider"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:597
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel异常"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel号码"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Exotel号码缺失"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel号码{0}无效"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel未启用"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel设置更新成功"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "展开"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:161
msgid "Expected Closure Date is required."
msgstr ""

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:159
msgid "Expected Deal Value is required."
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "已过期"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "导出"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "导出全部{0}条记录"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "导出类型"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "FCRM备注"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "FCRM设置"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "失败"

#: frontend/src/components/Modals/AddExistingUserModal.vue:109
msgid "Failed to add users"
msgstr ""

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "获取Twilio录音失败"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:182
msgid "Failed to create template"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:217
msgid "Failed to delete template"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr ""

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:246
msgid "Failed to rename template"
msgstr ""

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "更新Twilio通话状态失败"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:212
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:202
msgid "Failed to update template"
msgstr ""

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:81
msgid "Favicon"
msgstr "网站图标"

#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "字段"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:51
msgid "Fields Order"
msgstr "字段顺序"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "由于文件类型无效，文件“{0}”被跳过"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "文件\"{0}\"已跳过，因为最多只允许上传{1}个文件"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "文件\"{0}\"已跳过，因为文档类型\"{2}\"最多只允许上传{1}个文件"

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "筛选器"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "筛选条件"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:112
#: frontend/src/components/Filter.vue:58 frontend/src/components/Filter.vue:89
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:106
#: frontend/src/components/SortBy.vue:140
msgid "First Name"
msgstr "名字"

#: frontend/src/components/Modals/LeadModal.vue:135
msgid "First Name is mandatory"
msgstr "名字为必填项"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "首次响应时间"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "首次响应截止"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "首次响应时间"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "首次响应时间"

#: frontend/src/components/Filter.vue:131
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "名字"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
msgid "For"
msgstr "目标"

#: crm/api/dashboard.py:666
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr ""

#: frontend/src/components/Settings/General/GeneralSettings.vue:100
msgid "Forecasting disabled successfully"
msgstr ""

#: frontend/src/components/Settings/General/GeneralSettings.vue:99
msgid "Forecasting enabled successfully"
msgstr ""

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "表单"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "表单脚本更新成功"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:603
msgid "Frappe CRM mobile"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "周五"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "发件人"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "起始日期"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr ""

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "来自用户"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "已完成"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "全名"

#: crm/api/dashboard.py:728
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr ""

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "性别"

#: frontend/src/components/Settings/General/GeneralSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:89
msgid "General"
msgstr "常规"

#: crm/api/dashboard.py:1020
msgid "Geographic distribution of deals and revenue"
msgstr ""

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr ""

#: frontend/src/pages/Deal.vue:104 frontend/src/pages/Lead.vue:159
msgid "Go to website"
msgstr "访问网站"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "网格行"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "分组依据"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "分组字段"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "分组依据："

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "帮助"

#: frontend/src/components/CommunicationArea.vue:62
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "John 您好：\\n\\n可否请您就此事项提供更多详细信息..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "隐藏"

#: frontend/src/components/Activities/Activities.vue:230
msgid "Hide"
msgstr "隐藏"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr ""

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Hide Recording"
msgstr "隐藏录音"

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Hide border"
msgstr "隐藏边框"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Hide label"
msgstr "隐藏标签"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "隐藏预览"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "高"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "假期列表"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "假期列表名称"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "假期"

#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/General/HomeActions.vue:9
msgid "Home actions"
msgstr "主页操作"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "图标"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr ""

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "图片"

#: frontend/src/components/Filter.vue:276
#: frontend/src/components/Filter.vue:297
#: frontend/src/components/Filter.vue:312
#: frontend/src/components/Filter.vue:325
#: frontend/src/components/Filter.vue:339
msgid "In"
msgstr "在"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "进行中"

#: frontend/src/components/SLASection.vue:75
msgid "In less than a minute"
msgstr "不到一分钟前"

#: frontend/src/components/Activities/CallArea.vue:35
msgid "Inbound Call"
msgstr "呼入通话"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "收件箱"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "呼入"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "来电中..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "行业"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "行业"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "已启动"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "正在发起呼叫..."

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Integration"
msgstr ""

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "未启用集成"

#: frontend/src/components/Settings/Settings.vue:120
msgctxt "FCRM"
msgid "Integrations"
msgstr "集成"

#: frontend/src/components/Layouts/AppSidebar.vue:524
#: frontend/src/components/Layouts/AppSidebar.vue:527
msgid "Introduction"
msgstr "简介"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "无效的账户SID或认证令牌"

#: frontend/src/components/Modals/DealModal.vue:213
#: frontend/src/components/Modals/LeadModal.vue:154
msgid "Invalid Email"
msgstr "无效的电子邮件"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "无效的Exotel号码"

#: crm/api/dashboard.py:73
msgid "Invalid chart name"
msgstr ""

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "无效的凭据"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr ""

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:101
msgid "Invite User"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:56
msgid "Invite as"
msgstr "邀请身份"

#: frontend/src/components/Settings/InviteUserPage.vue:29
msgid "Invite by email"
msgstr "通过邮件邀请"

#: frontend/src/components/Layouts/AppSidebar.vue:538
msgid "Invite users"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:354
msgid "Invite your team"
msgstr ""

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "邀请人"

#: frontend/src/components/Filter.vue:278
#: frontend/src/components/Filter.vue:287
#: frontend/src/components/Filter.vue:299
#: frontend/src/components/Filter.vue:314
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:341
#: frontend/src/components/Filter.vue:350
msgid "Is"
msgstr "是"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "设为默认"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "ERPNext是否安装在其他站点？"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "是否分组"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "是否主要"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "是否标准"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr ""

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "职位"

#: frontend/src/components/Filter.vue:75 frontend/src/components/Filter.vue:108
#: frontend/src/components/Modals/AssignmentModal.vue:35
#: frontend/src/components/Modals/TaskModal.vue:76
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "张三"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "看板"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "看板列"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "看板字段"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:11
msgid "Kanban Settings"
msgstr "看板设置"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Key"
msgstr "键"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:109
msgid "Label"
msgstr "标签"

#: frontend/src/components/Filter.vue:620
msgid "Last 6 Months"
msgstr "过去6个月"

#: frontend/src/components/Filter.vue:612
msgid "Last Month"
msgstr "上月"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "姓氏"

#: frontend/src/components/Filter.vue:616
msgid "Last Quarter"
msgstr "上季度"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "最后状态变更日志"

#: frontend/src/components/Filter.vue:608
msgid "Last Week"
msgstr "上周"

#: frontend/src/components/Filter.vue:624
msgid "Last Year"
msgstr "去年"

#: frontend/src/pages/Contact.vue:538 frontend/src/pages/MobileContact.vue:536
#: frontend/src/pages/MobileOrganization.vue:480
#: frontend/src/pages/MobileOrganization.vue:508
#: frontend/src/pages/Organization.vue:489
#: frontend/src/pages/Organization.vue:517
msgid "Last modified"
msgstr "最后修改"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr ""

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "布局"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:545
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:58
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:58
#: frontend/src/components/Telephony/ExotelCallUI.vue:205
#: frontend/src/pages/Tasks.vue:130
msgid "Lead"
msgstr "线索"

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "线索详情"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "线索名称"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "线索负责人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:73
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "线索负责人不能与线索邮箱地址相同"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "线索来源"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "线索状态"

#: crm/api/dashboard.py:935
msgid "Lead generation channel analysis"
msgstr ""

#: crm/api/dashboard.py:729
msgid "Lead to deal conversion pipeline"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Lead.vue:369 frontend/src/pages/MobileLead.vue:293
msgid "Leads"
msgstr "线索"

#: crm/api/dashboard.py:934
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr ""

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "左侧"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "媒体库"

#: frontend/src/components/Filter.vue:274
#: frontend/src/components/Filter.vue:285
#: frontend/src/components/Filter.vue:295
#: frontend/src/components/Filter.vue:323
#: frontend/src/components/Filter.vue:337
msgid "Like"
msgstr "相似"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "链接"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "链接"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "列表"

#: frontend/src/components/Activities/CallArea.vue:74
msgid "Listen"
msgstr "收听"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "加载默认列"

#: frontend/src/components/Kanban/KanbanView.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "加载更多"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:37
#: frontend/src/pages/Deal.vue:193 frontend/src/pages/MobileDeal.vue:119
msgid "Loading..."
msgstr "加载中..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "日志"

#: frontend/src/components/Activities/Activities.vue:803
#: frontend/src/components/Activities/ActivityHeader.vue:137
#: frontend/src/components/Activities/ActivityHeader.vue:180
msgid "Log a Call"
msgstr ""

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "登录Frappe云平台？"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/BrandSettings.vue:47
msgid "Logo"
msgstr "徽标"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "未成交"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr ""

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "未成交原因"

#: crm/api/dashboard.py:883
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr ""

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "低"

#: frontend/src/pages/Contact.vue:94 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "发起呼叫"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Private"
msgstr "设为私有"

#: frontend/src/components/ViewControls.vue:1146
msgid "Make Public"
msgstr "设为公开"

#: frontend/src/components/Activities/Activities.vue:807
#: frontend/src/components/Activities/ActivityHeader.vue:142
#: frontend/src/components/Activities/ActivityHeader.vue:185
#: frontend/src/pages/Deals.vue:504 frontend/src/pages/Leads.vue:531
msgid "Make a Call"
msgstr "拨打电话"

#: frontend/src/pages/Deal.vue:86 frontend/src/pages/Lead.vue:128
msgid "Make a call"
msgstr "发起呼叫"

#: frontend/src/components/Activities/AttachmentArea.vue:109
msgid "Make attachment {0}"
msgstr "将附件{0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "拨打电话"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "设为私有"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "设为公开"

#: frontend/src/components/Activities/AttachmentArea.vue:118
msgid "Make {0}"
msgstr "创建{0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "将{0}设为默认呼叫媒介"

#: frontend/src/components/Settings/General/GeneralSettings.vue:23
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr ""

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr ""

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:91
#: frontend/src/components/Settings/InviteUserPage.vue:171
#: frontend/src/components/Settings/InviteUserPage.vue:178
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:185
#: frontend/src/components/Settings/Users.vue:256
#: frontend/src/components/Settings/Users.vue:259
msgid "Manager"
msgstr "经理"

#: frontend/src/data/document.js:54
msgid "Mandatory field error: {0}"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "手动"

#: frontend/src/components/Notifications.vue:19
#: frontend/src/pages/MobileNotification.vue:11
#: frontend/src/pages/MobileNotification.vue:14
msgid "Mark all as read"
msgstr "全部标记为已读"

#: frontend/src/components/Layouts/AppSidebar.vue:542
msgid "Masters"
msgstr "主数据"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "媒介"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "@提及"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "消息"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "中间名"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "手机号码"

#: frontend/src/components/Modals/DealModal.vue:209
#: frontend/src/components/Modals/LeadModal.vue:150
msgid "Mobile No should be a number"
msgstr "手机号码应为数字"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "手机号"

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "手机号码"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "手机号码缺失"

#: frontend/src/components/Layouts/AppSidebar.vue:606
msgid "Mobile app installation"
msgstr ""

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:526
#: frontend/src/pages/MobileOrganization.vue:470
#: frontend/src/pages/Organization.vue:479
msgid "Mobile no"
msgstr "手机号"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "周一"

#: crm/api/dashboard.py:669
msgid "Month"
msgstr "月"

#: frontend/src/components/FieldLayoutEditor.vue:454
msgid "Move to next section"
msgstr "跳转至下一区块"

#: frontend/src/components/FieldLayoutEditor.vue:407
msgid "Move to next tab"
msgstr "切换至下一标签页"

#: frontend/src/components/FieldLayoutEditor.vue:464
msgid "Move to previous section"
msgstr "返回上一区块"

#: frontend/src/components/FieldLayoutEditor.vue:393
msgid "Move to previous tab"
msgstr "返回上一标签页"

#: frontend/src/components/Modals/ViewModal.vue:40
msgid "My Open Deals"
msgstr "我负责的开放商机"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:42
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:42
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:488
#: frontend/src/pages/Organization.vue:497
msgid "Name"
msgstr "名称"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:157
msgid "Name is required"
msgstr ""

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "命名规则"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "净额"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "净总计"

#: frontend/src/components/Activities/ActivityHeader.vue:82
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "新建"

#: frontend/src/components/Modals/AddressModal.vue:94
msgid "New Address"
msgstr "新建地址"

#: frontend/src/components/Modals/CallLogModal.vue:98
msgid "New Call Log"
msgstr "新建通话记录"

#: frontend/src/components/Activities/Activities.vue:394
#: frontend/src/components/Activities/ActivityHeader.vue:27
#: frontend/src/components/Activities/ActivityHeader.vue:132
msgid "New Comment"
msgstr "新建评论"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "新建联系人"

#: frontend/src/components/Activities/Activities.vue:389
#: frontend/src/components/Activities/ActivityHeader.vue:17
#: frontend/src/components/Activities/ActivityHeader.vue:127
msgid "New Email"
msgstr "新建邮件"

#: frontend/src/components/Activities/ActivityHeader.vue:73
msgid "New Message"
msgstr "新建消息"

#: frontend/src/components/Activities/ActivityHeader.vue:42
#: frontend/src/components/Activities/ActivityHeader.vue:148
#: frontend/src/pages/Deals.vue:510 frontend/src/pages/Leads.vue:537
msgid "New Note"
msgstr "新建备注"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "新建组织"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "新密码"

#: frontend/src/components/FieldLayoutEditor.vue:203
#: frontend/src/components/SidePanelLayoutEditor.vue:133
msgid "New Section"
msgstr "新建区块"

#: frontend/src/components/FieldLayoutEditor.vue:299
#: frontend/src/components/FieldLayoutEditor.vue:304
msgid "New Tab"
msgstr "新建标签页"

#: frontend/src/components/Activities/ActivityHeader.vue:52
#: frontend/src/components/Activities/ActivityHeader.vue:153
#: frontend/src/pages/Deals.vue:515 frontend/src/pages/Leads.vue:542
msgid "New Task"
msgstr "新建任务"

#: frontend/src/components/Activities/ActivityHeader.vue:163
msgid "New WhatsApp Message"
msgstr "新建WhatsApp消息"

#: frontend/src/components/Modals/ConvertToDealModal.vue:81
#: frontend/src/pages/MobileLead.vue:164
msgid "New contact will be created based on the person's details"
msgstr "将根据个人信息新建联系人"

#: frontend/src/components/Modals/ConvertToDealModal.vue:56
#: frontend/src/pages/MobileLead.vue:138
msgid "New organization will be created based on the data in details section"
msgstr "将根据详情数据新建组织"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr ""

#: frontend/src/components/Modals/CreateDocumentModal.vue:89
msgid "New {0}"
msgstr "新建 {0}"

#: frontend/src/components/Filter.vue:668
msgid "Next 6 Months"
msgstr "未来6个月"

#: frontend/src/components/Filter.vue:660
msgid "Next Month"
msgstr "下月"

#: frontend/src/components/Filter.vue:664
msgid "Next Quarter"
msgstr "下季度"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "下一步"

#: frontend/src/components/Filter.vue:656
msgid "Next Week"
msgstr "下周"

#: frontend/src/components/Filter.vue:672
msgid "Next Year"
msgstr "明年"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "否"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "无应答"

#: frontend/src/components/Controls/Grid.vue:309
msgid "No Data"
msgstr "无数据"

#: frontend/src/components/Kanban/KanbanView.vue:102
#: frontend/src/pages/Deals.vue:106 frontend/src/pages/Leads.vue:122
#: frontend/src/pages/Tasks.vue:68
msgid "No Title"
msgstr "无标题"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "未作更改"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:282 frontend/src/pages/MobileDeal.vue:207
msgid "No contacts added"
msgstr "未添加联系人"

#: frontend/src/pages/Deal.vue:97 frontend/src/pages/Lead.vue:150
msgid "No email set"
msgstr "未设置邮箱"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "无标签"

#: frontend/src/pages/Deal.vue:705
msgid "No mobile number set"
msgstr "未设置手机号"

#: frontend/src/components/Notifications.vue:83
#: frontend/src/pages/MobileNotification.vue:67
msgid "No new notifications"
msgstr "无新通知"

#: frontend/src/pages/Lead.vue:135
msgid "No phone number set"
msgstr "未设置电话号码"

#: frontend/src/pages/Deal.vue:700
msgid "No primary contact set"
msgstr "未设置主要联系人"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:72
#: frontend/src/components/Controls/MultiSelectUserInput.vue:72
msgid "No results found"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "未找到模板"

#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr ""

#: frontend/src/pages/MobileOrganization.vue:284
#: frontend/src/pages/Organization.vue:300
msgid "No website found"
msgstr ""

#: frontend/src/pages/Deal.vue:110 frontend/src/pages/Lead.vue:165
msgid "No website set"
msgstr "未设置网站"

#: frontend/src/components/SidePanelLayout.vue:128
msgid "No {0} Available"
msgstr "无可用{0}"

#: frontend/src/pages/CallLogs.vue:56 frontend/src/pages/Contact.vue:159
#: frontend/src/pages/Contacts.vue:59 frontend/src/pages/Deals.vue:235
#: frontend/src/pages/Leads.vue:261 frontend/src/pages/MobileContact.vue:150
#: frontend/src/pages/MobileOrganization.vue:142
#: frontend/src/pages/Notes.vue:92 frontend/src/pages/Organization.vue:156
#: frontend/src/pages/Organizations.vue:59 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "未找到{0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "员工数量"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "普通"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "不允许"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:311
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:349
msgid "Not Equals"
msgstr "不等于"

#: frontend/src/components/Filter.vue:277
#: frontend/src/components/Filter.vue:298
#: frontend/src/components/Filter.vue:313
#: frontend/src/components/Filter.vue:326
#: frontend/src/components/Filter.vue:340
msgid "Not In"
msgstr "不在"

#: frontend/src/components/Filter.vue:275
#: frontend/src/components/Filter.vue:286
#: frontend/src/components/Filter.vue:296
#: frontend/src/components/Filter.vue:324
#: frontend/src/components/Filter.vue:338
msgid "Not Like"
msgstr "不包含"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/General/BrandSettings.vue:16
#: frontend/src/components/Settings/General/CurrencySettings.vue:16
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "未保存"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:260
msgid "Not allowed to add contact to Deal"
msgstr "无权向商机添加联系人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:408
msgid "Not allowed to convert Lead to Deal"
msgstr "无权将线索转为商机"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:271
msgid "Not allowed to remove contact from Deal"
msgstr "无权从商机移除联系人"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:282
msgid "Not allowed to set primary contact for Deal"
msgstr "无权设置商机主要联系人"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:549
msgid "Note"
msgstr "备注"

#: frontend/src/pages/Deal.vue:570 frontend/src/pages/Lead.vue:436
#: frontend/src/pages/MobileDeal.vue:463 frontend/src/pages/MobileLead.vue:370
msgid "Notes"
msgstr "备注"

#: frontend/src/pages/Notes.vue:20
msgid "Notes View"
msgstr "备注视图"

#: frontend/src/components/Activities/EmailArea.vue:13
#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Notification"
msgstr "通知"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "通知内容"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "通知类型文档"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "通知类型文档类型"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "通知"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "编号"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr ""

#: crm/api/dashboard.py:1027 crm/api/dashboard.py:1084
msgid "Number of deals"
msgstr ""

#: crm/api/dashboard.py:1077
msgid "Number of deals and total value per salesperson"
msgstr ""

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "原上级"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "临时冻结"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr ""

#: crm/api/dashboard.py:181
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr ""

#: frontend/src/utils/index.js:444
msgid "Only image files are allowed"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "仅可设置一个主要{0}"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "待处理"

#: frontend/src/components/Modals/NoteModal.vue:18
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Open Deal"
msgstr "开放商机"

#: frontend/src/components/Modals/NoteModal.vue:19
#: frontend/src/components/Modals/TaskModal.vue:26
msgid "Open Lead"
msgstr "开放线索"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "在门户中打开"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "新窗口打开"

#: frontend/src/pages/Organization.vue:92
msgid "Open website"
msgstr "打开网站"

#: frontend/src/components/Kanban/KanbanView.vue:221
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "选项"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr ""

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "排序依据"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:548
#: frontend/src/components/Modals/ConvertToDealModal.vue:39
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:505
#: frontend/src/pages/MobileLead.vue:120
#: frontend/src/pages/MobileOrganization.vue:449
#: frontend/src/pages/MobileOrganization.vue:503
#: frontend/src/pages/Organization.vue:458
#: frontend/src/pages/Organization.vue:512
msgid "Organization"
msgstr "组织"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "组织详情"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "组织徽标"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "组织名称"

#: frontend/src/pages/Deal.vue:69
msgid "Organization logo"
msgstr "组织徽标"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:208
#: frontend/src/pages/Organization.vue:238
msgid "Organizations"
msgstr "组织"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Other features"
msgstr ""

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "其他"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Outbound Call"
msgstr "呼出通话"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "呼出"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "负责人"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "上级CRM区域"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "密码"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "演示用户{}无法重置密码"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr ""

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:41
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:41
msgid "Payment Reminder"
msgstr "付款提醒"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:72
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:72
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Frappé付款提醒 - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "待处理"

#: frontend/src/components/Settings/InviteUserPage.vue:66
msgid "Pending Invites"
msgstr "待处理邀请"

#: frontend/src/pages/Dashboard.vue:79
msgid "Period"
msgstr "期间"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "个人"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:498
#: frontend/src/pages/Organization.vue:507
msgid "Phone"
msgstr "电话"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "电话号码"

#: frontend/src/components/ViewControls.vue:1138
msgid "Pin View"
msgstr "固定视图"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "已固定"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "固定视图"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Pinned view"
msgstr ""

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "播放速度"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "请先启用Twilio设置"

#: frontend/src/components/FilesUploader/FilesUploader.vue:168
msgid "Please enter a valid URL"
msgstr "请输入有效URL"

#: frontend/src/components/Settings/General/CurrencySettings.vue:159
msgid "Please enter the Exchangerate Host access key."
msgstr ""

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:152
msgid "Please select a currency before saving."
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:145
#: frontend/src/pages/MobileLead.vue:434
msgid "Please select an existing contact"
msgstr "请选择现有联系人"

#: frontend/src/components/Modals/ConvertToDealModal.vue:150
#: frontend/src/pages/MobileLead.vue:439
msgid "Please select an existing organization"
msgstr "请选择现有组织"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "请配置Exotel集成"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:169
msgid "Please specify a reason for losing the deal."
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Please specify the reason for losing the deal."
msgstr ""

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "位置"

#: frontend/src/pages/Deal.vue:222 frontend/src/pages/MobileDeal.vue:151
msgid "Primary"
msgstr "主要"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "主要邮箱"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr ""

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "主要电话"

#: frontend/src/pages/Deal.vue:677 frontend/src/pages/MobileDeal.vue:568
msgid "Primary contact set"
msgstr "已设置主要联系人"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "优先级"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Priority"
msgstr "优先级"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "私有"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "成交概率"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "产品"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr ""

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr ""

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "产品"

#: frontend/src/components/Layouts/AppSidebar.vue:535
#: frontend/src/components/Settings/Settings.vue:79
msgid "Profile"
msgstr "个人资料"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "个人资料更新成功"

#: crm/api/dashboard.py:667
msgid "Projected vs actual revenue based on deal probability"
msgstr ""

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "公开"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "公开视图"

#: frontend/src/components/Layouts/AppSidebar.vue:565
msgid "Public view"
msgstr ""

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "数量"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "已排队"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "快速录入"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "快速筛选"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "快速筛选更新成功"

#: frontend/src/components/Layouts/AppSidebar.vue:589
msgid "Quick entry layout"
msgstr ""

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "单价"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "已读"

#: crm/api/dashboard.py:886
msgid "Reason"
msgstr "原因"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "录制通话"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "录制呼出通话"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "录音URL"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "关联文档"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "关联文档类型"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "关联文档类型"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "关联名称"

#: frontend/src/components/ViewControls.vue:25
#: frontend/src/components/ViewControls.vue:160
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "刷新"

#: frontend/src/components/Telephony/TwilioCallUI.vue:104
msgid "Reject"
msgstr "拒绝"

#: frontend/src/components/Settings/Users.vue:210
#: frontend/src/components/Settings/Users.vue:213
#: frontend/src/pages/Deal.vue:626
msgid "Remove"
msgstr "移除"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "全部移除"

#: frontend/src/components/FieldLayoutEditor.vue:444
msgid "Remove and move fields to previous column"
msgstr "移除并将字段移至前一列"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove column"
msgstr "移除列"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:101
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:47
msgid "Remove image"
msgstr "移除图片"

#: frontend/src/components/FieldLayoutEditor.vue:365
msgid "Remove section"
msgstr "移除区块"

#: frontend/src/components/FieldLayoutEditor.vue:324
msgid "Remove tab"
msgstr "移除标签页"

#: frontend/src/components/Activities/EmailArea.vue:31
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "回复"

#: frontend/src/components/Activities/EmailArea.vue:44
msgid "Reply All"
msgstr "回复全部"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "提交一个问题"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "必填字段"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:82
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "重置"

#: frontend/src/components/ColumnSettings.vue:82
msgid "Reset Changes"
msgstr "撤销修改"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "重置ERPNext表单脚本"

#: frontend/src/components/ColumnSettings.vue:93
msgid "Reset to Default"
msgstr "恢复默认"

#: frontend/src/pages/Dashboard.vue:34
msgid "Reset to default"
msgstr "恢复默认设置"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "响应人"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "响应详情"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "响应与跟进"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "恢复"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "恢复默认值"

#: frontend/src/components/FilesUploader/FilesUploader.vue:54
msgid "Retake"
msgstr "重新拍摄"

#: crm/api/dashboard.py:675
msgid "Revenue"
msgstr "收入"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:84
msgid "Rich Text"
msgstr "富文本"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "右侧"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "响铃中"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:148
msgid "Ringing..."
msgstr "正在响铃..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:44
msgid "Role"
msgstr "角色"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "路由"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "路由名称"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "行"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "服务级别协议"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "SLA创建"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "SLA名称"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "SLA状态"

#: frontend/src/components/EmailEditor.vue:82
msgid "SUBJECT"
msgstr "主题"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Sales Manager"
msgstr "销售经理"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:90
#: frontend/src/components/Settings/InviteUserPage.vue:170
#: frontend/src/components/Settings/InviteUserPage.vue:177
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:186
#: frontend/src/components/Settings/Users.vue:268
#: frontend/src/components/Settings/Users.vue:271
msgid "Sales User"
msgstr "销售人员"

#: crm/api/dashboard.py:598
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr ""

#: frontend/src/pages/Dashboard.vue:106
msgid "Sales user"
msgstr ""

#: crm/api/dashboard.py:1079
msgid "Salesperson"
msgstr ""

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "称呼"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "周六"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:87
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/DropdownItem.vue:21
#: frontend/src/components/Modals/AddressModal.vue:99
#: frontend/src/components/Modals/CallLogModal.vue:102
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/Settings/General/CurrencySettings.vue:182
#: frontend/src/components/Telephony/ExotelCallUI.vue:231
#: frontend/src/components/ViewControls.vue:123
#: frontend/src/pages/Dashboard.vue:45
msgid "Save"
msgstr "保存"

#: frontend/src/components/Modals/ViewModal.vue:13
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:157
msgid "Save Changes"
msgstr "保存更改"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "已保存视图"

#: frontend/src/components/Layouts/AppSidebar.vue:564
msgid "Saved view"
msgstr ""

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "安排任务..."

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "脚本"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr ""

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:342
msgid "Section"
msgstr "区块"

#: frontend/src/pages/Dashboard.vue:59
msgid "Select Range"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:58
msgid "Select currency"
msgstr ""

#: frontend/src/components/Settings/General/CurrencySettings.vue:82
msgid "Select provider"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:332
msgid "Select {0}"
msgstr "选择{0}"

#: frontend/src/components/EmailEditor.vue:162
msgid "Send"
msgstr "发送"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "发送邀请"

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "Send Template"
msgstr "发送模板"

#: frontend/src/pages/Deal.vue:93 frontend/src/pages/Lead.vue:144
msgid "Send an email"
msgstr "发送邮件"

#: frontend/src/components/Layouts/AppSidebar.vue:455
msgid "Send email"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "分隔符"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "系列"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "服务商"

#: frontend/src/components/Layouts/AppSidebar.vue:576
msgid "Service level agreement"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:69
msgid "Set all as private"
msgstr "全部设为私有"

#: frontend/src/components/FilesUploader/FilesUploader.vue:62
msgid "Set all as public"
msgstr "全部设为公开"

#: frontend/src/pages/Deal.vue:80
msgid "Set an organization"
msgstr "设置组织"

#: frontend/src/pages/Deal.vue:634 frontend/src/pages/MobileDeal.vue:525
msgid "Set as Primary Contact"
msgstr "设为主要联系人"

#: frontend/src/components/ViewControls.vue:1123
msgid "Set as default"
msgstr "设为默认"

#: frontend/src/components/Settings/General/CurrencySettings.vue:173
msgid "Set currency"
msgstr ""

#: frontend/src/pages/Lead.vue:122
msgid "Set first name"
msgstr "设置名字"

#: frontend/src/components/Layouts/AppSidebar.vue:528
msgid "Setting up"
msgstr "系统配置中"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr ""

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:532
#: frontend/src/components/Settings/Settings.vue:11
#: frontend/src/components/Settings/Settings.vue:75
msgid "Settings"
msgstr "设置"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:334
msgid "Setup your password"
msgstr ""

#: frontend/src/components/Activities/Activities.vue:230
msgid "Show"
msgstr "显示"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:360
msgid "Show border"
msgstr "显示边框"

#: frontend/src/components/FieldLayoutEditor.vue:355
msgid "Show label"
msgstr "显示标签"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "显示预览"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "侧边栏"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "侧边栏项"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "简单Python表达式，示例：doc.status == '开放' and doc.lead_source == '广告'"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:22
#: frontend/src/components/SortBy.vue:240
msgid "Sort"
msgstr "排序"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Modals/EditValueModal.vue:10
msgid "Source"
msgstr "来源"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "Source Name"
msgstr "来源名称"

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "空白分隔线"

#: crm/api/dashboard.py:731 crm/api/dashboard.py:790
msgid "Stage"
msgstr "阶段"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "标准表单脚本不可修改，请复制后进行编辑"

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "标准售价"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "标准视图"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "开始日期"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Start Time"
msgstr "开始时间"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr ""

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:516
#: frontend/src/pages/MobileOrganization.vue:460
#: frontend/src/pages/Organization.vue:469
msgid "Status"
msgstr "状态"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "状态变更日志"

#: frontend/src/components/Modals/DealModal.vue:217
#: frontend/src/components/Modals/LeadModal.vue:158
msgid "Status is required"
msgstr "状态为必填项"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "子域名"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:71
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:71
msgid "Subject"
msgstr "主题"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:161
msgid "Subject is required"
msgstr "必须填写主题"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "主题：{0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "周日"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:49
msgid "Switch camera"
msgstr "切换摄像头"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr ""

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "System Manager"
msgstr "系统管理员"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "收件人"

#: frontend/src/components/Telephony/ExotelCallUI.vue:151
msgid "Take a note..."
msgstr "记录备注..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:550
msgid "Task"
msgstr "任务"

#: frontend/src/pages/Deal.vue:565 frontend/src/pages/Lead.vue:431
#: frontend/src/pages/MobileDeal.vue:458 frontend/src/pages/MobileLead.vue:365
msgid "Tasks"
msgstr "任务"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:123
msgid "Telephony"
msgstr "电话系统"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "电话媒介"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:178
msgid "Template created successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:214
msgid "Template deleted successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:198
msgid "Template disabled successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template enabled successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:243
msgid "Template renamed successfully"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
msgid "Template updated successfully"
msgstr ""

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "区域"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1022 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "区域"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "区域经理"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "区域名称"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "条件'{0}'无效：{1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr ""

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr ""

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "优先级表中只能有一个默认优先级"

#: frontend/src/components/Modals/AddressModal.vue:129
#: frontend/src/components/Modals/CallLogModal.vue:132
msgid "These fields are required: {0}"
msgstr ""

#: frontend/src/components/Filter.vue:644
msgid "This Month"
msgstr "本月"

#: frontend/src/components/Filter.vue:648
msgid "This Quarter"
msgstr "本季度"

#: frontend/src/components/Filter.vue:640
msgid "This Week"
msgstr "本周"

#: frontend/src/components/Filter.vue:652
msgid "This Year"
msgstr "本年"

#: frontend/src/components/SidePanelLayoutEditor.vue:119
msgid "This section is not editable"
msgstr "此区块不可编辑"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr ""

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "将恢复（如不存在）所有默认状态、自定义字段及布局。删除并恢复将删除默认布局后重新创建"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "周四"

#: frontend/src/components/Filter.vue:356
msgid "Timespan"
msgstr "时间跨度"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:30
#: frontend/src/components/Modals/TaskModal.vue:41
msgid "Title"
msgstr "标题"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:32
msgid "Title Field"
msgstr "标题字段"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:63
msgid "To"
msgstr "至"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "截止日期"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr ""

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "目标用户"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr ""

#: frontend/src/components/Filter.vue:632
msgid "Today"
msgstr "今天"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "待办"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "切换预览模式"

#: frontend/src/components/Filter.vue:636
msgid "Tomorrow"
msgstr "明天"

#: frontend/src/components/Modals/NoteModal.vue:37
#: frontend/src/components/Modals/TaskModal.vue:59
msgid "Took a call with John Doe and discussed the new project."
msgstr "与张三通话讨论新项目"

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "总计"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "总假期天数"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr ""

#: crm/api/dashboard.py:123
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr ""

#: crm/api/dashboard.py:124
msgid "Total number of leads"
msgstr ""

#: crm/api/dashboard.py:182
msgid "Total number of non won/lost deals"
msgstr ""

#: crm/api/dashboard.py:297
msgid "Total number of won deals based on its closure date"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "周二"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:596
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Twilio API凭证创建错误"

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio号码"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio未启用"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio设置更新成功"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Type"
msgstr "类型"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:73
#: frontend/src/components/Controls/MultiSelectUserInput.vue:73
msgid "Type an email address to invite"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "在此输入消息..."

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "未授权的请求"

#: frontend/src/components/FieldLayoutEditor.vue:350
msgid "Uncollapsible"
msgstr "不可折叠"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:130
msgid "Unknown"
msgstr "未知"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:77
msgid "Unlink all"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr ""

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:242
msgid "Unlink linked item"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink {0} item(s)"
msgstr ""

#: frontend/src/components/ViewControls.vue:1138
msgid "Unpin View"
msgstr "取消固定视图"

#: frontend/src/components/ViewControls.vue:975
msgid "Unsaved Changes"
msgstr "未保存的更改"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "未命名"

#: frontend/src/components/ColumnSettings.vue:138
#: frontend/src/components/Modals/AssignmentModal.vue:17
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/NoteModal.vue:6
#: frontend/src/components/Modals/TaskModal.vue:8
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:17
#: frontend/src/components/Settings/General/BrandSettings.vue:23
#: frontend/src/components/Settings/General/CurrencySettings.vue:23
#: frontend/src/components/Settings/General/HomeActions.vue:17
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:219
#: frontend/src/components/ViewControls.vue:980
msgid "Update"
msgstr "更新"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr ""

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "更新{0}条记录"

#: frontend/src/components/FilesUploader/FilesUploader.vue:86
msgid "Upload"
msgstr "上传"

#: frontend/src/components/Activities/Activities.vue:404
#: frontend/src/components/Activities/ActivityHeader.vue:62
#: frontend/src/components/Activities/ActivityHeader.vue:158
msgid "Upload Attachment"
msgstr "上传附件"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "上传文档"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "上传图片"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "上传视频"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:42 frontend/src/pages/Lead.vue:96
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:42
msgid "Upload image"
msgstr "上传图片"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "用户"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "用户名"

#: frontend/src/components/Settings/Users.vue:301
msgid "User {0} has been removed"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:95
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "用户"

#: frontend/src/components/Modals/AddExistingUserModal.vue:103
msgid "Users added successfully"
msgstr ""

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "有效期"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "值"

#: frontend/src/components/Modals/ViewModal.vue:25
msgid "View Name"
msgstr "视图名称"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Views"
msgstr "视图"

#: frontend/src/components/Layouts/AppSidebar.vue:558
msgid "Web form"
msgstr ""

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Webhook验证令牌"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "网站"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "周三"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "每周休息"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "欢迎消息"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:598
#: frontend/src/components/Settings/Settings.vue:129
#: frontend/src/pages/Deal.vue:580 frontend/src/pages/Lead.vue:446
#: frontend/src/pages/MobileDeal.vue:473 frontend/src/pages/MobileLead.vue:380
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp模板"

#: frontend/src/components/Filter.vue:44 frontend/src/components/Filter.vue:82
msgid "Where"
msgstr "条件"

#: frontend/src/components/ColumnSettings.vue:117
msgid "Width"
msgstr "宽度"

#: frontend/src/components/ColumnSettings.vue:122
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "宽度可为数字、像素或rem（如3、30px、10rem）"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr ""

#: crm/api/dashboard.py:296
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr ""

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "工作日"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "工作时间"

#: frontend/src/components/Filter.vue:628
msgid "Yesterday"
msgstr "昨天"

#: crm/api/whatsapp.py:36 crm/api/whatsapp.py:216 crm/api/whatsapp.py:230
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "您"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "您无权访问此资源"

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "可在设置中修改默认呼叫媒介"

#: frontend/src/components/Settings/General/CurrencySettings.vue:107
msgid "You can get your access key from "
msgstr ""

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "您的电话客服未设置Exotel号码"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "您的电话客服未设置手机号码"

#: frontend/src/data/document.js:32
msgid "You do not have permission to access this document"
msgstr ""

#: frontend/src/components/ViewControls.vue:976
msgid "You have unsaved changes. Do you want to save them?"
msgstr "存在未保存更改，是否保存？"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "需进入开发者模式才能编辑标准表单脚本"

#: crm/api/todo.py:111
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "{1}移除了您在任务{0}的分配"

#: crm/api/todo.py:46 crm/api/todo.py:89
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "{2}移除了您在{0}{1}的分配"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "添加了"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "琥珀色"

#: crm/api/todo.py:120
msgid "assigned a new task {0} to you"
msgstr "向您分配了新任务{0}"

#: crm/api/todo.py:100
msgid "assigned a {0} {1} to you"
msgstr "向您分配了{0}{1}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "黑色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "蓝色"

#: frontend/src/components/Activities/Activities.vue:232
msgid "changes from"
msgstr "来自的更改"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "评论"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "青色"

#: frontend/src/components/Controls/MultiSelectEmailInput.vue:268
#: frontend/src/components/Controls/MultiSelectUserInput.vue:242
msgid "email already exists"
msgstr ""

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/General/CurrencySettings.vue:113
msgid "exchangerate.host"
msgstr "汇率服务商"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "法兰克福应用"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "灰色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "绿色"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "分组依据"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "进行了通话"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "已联系"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr ""

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr ""

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr ""

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr ""

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr ""

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr ""

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr ""

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr ""

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr ""

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr ""

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr ""

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr ""

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr ""

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr ""

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr ""

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
#: frontend/src/components/Settings/InviteUserPage.vue:37
msgid "<EMAIL>"
msgstr ""

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "刚刚"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "看板"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "标签"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "列表"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:65
#: frontend/src/pages/MobileNotification.vue:52
msgid "mentioned you in {0}"
msgstr "在{0}中提到您"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "next"
msgstr "下一步"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "现在"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "橙色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "粉色"

#: frontend/src/components/FieldLayoutEditor.vue:374
msgid "previous"
msgstr "上一步"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "private"
msgstr "私有"

#: frontend/src/components/Activities/AttachmentArea.vue:108
msgid "public"
msgstr "公开"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "紫色"

#: crm/api/whatsapp.py:37
msgid "received a whatsapp message in {0}"
msgstr "在{0}收到WhatsApp消息"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "红色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "青色"

#: frontend/src/components/Activities/Activities.vue:274
#: frontend/src/components/Activities/Activities.vue:337
msgid "to"
msgstr "至"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "明天"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "紫罗兰色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "黄色"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "昨天"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} 月"

#: crm/api/todo.py:50
msgid "{0} assigned a {1} {2} to you"
msgstr "{0}向您分配了{1}{2}"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} 天"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "{0}天前"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0}小时"

#: frontend/src/components/Settings/Users.vue:291
msgid "{0} has been granted {1} access"
msgstr ""

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "{0}小时前"

#: frontend/src/components/EmailEditor.vue:29
#: frontend/src/components/EmailEditor.vue:64
#: frontend/src/components/EmailEditor.vue:77
#: frontend/src/components/Modals/AddExistingUserModal.vue:36
#: frontend/src/components/Settings/InviteUserPage.vue:41
msgid "{0} is an invalid email address"
msgstr "{0}是无效的电子邮件地址"

#: frontend/src/components/Modals/ConvertToDealModal.vue:181
msgid "{0} is required"
msgstr "{0}是必填项"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} 分"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "{0}分钟前"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "{0}个月前"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} 周"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "{0}周前"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0}年前"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "{0}年前"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr ""

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr ""

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr ""

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr ""

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr ""

