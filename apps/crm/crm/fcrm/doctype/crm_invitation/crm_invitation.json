{"actions": [], "allow_rename": 1, "creation": "2024-09-03 12:19:18.933810", "doctype": "DocType", "engine": "InnoDB", "field_order": ["email", "role", "key", "invited_by", "column_break_dsuz", "status", "email_sent_at", "accepted_at"], "fields": [{"fieldname": "email", "fieldtype": "Data", "in_list_view": 1, "label": "Email", "reqd": 1}, {"fieldname": "role", "fieldtype": "Select", "in_list_view": 1, "label": "Role", "options": "\nSales User\nSales Manager\nSystem Manager", "reqd": 1}, {"fieldname": "key", "fieldtype": "Data", "label": "Key"}, {"fieldname": "invited_by", "fieldtype": "Link", "in_list_view": 1, "label": "Invited By", "options": "User"}, {"fieldname": "column_break_dsuz", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "\nPending\nAccepted\nExpired"}, {"fieldname": "email_sent_at", "fieldtype": "Datetime", "label": "<PERSON><PERSON>"}, {"fieldname": "accepted_at", "fieldtype": "Datetime", "label": "Accepted At"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-17 17:20:18.935395", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Invitation", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}