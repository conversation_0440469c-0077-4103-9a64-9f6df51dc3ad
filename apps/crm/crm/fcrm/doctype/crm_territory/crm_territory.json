{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:territory_name", "creation": "2024-01-04 18:52:58.872535", "doctype": "DocType", "engine": "InnoDB", "field_order": ["territory_name", "column_break_mckp", "territory_manager", "section_break_qhaf", "old_parent", "parent_crm_territory", "column_break_pypy", "lft", "rgt", "is_group"], "fields": [{"fieldname": "territory_name", "fieldtype": "Data", "in_list_view": 1, "label": "Territory Name", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "is_group", "fieldtype": "Check", "label": "Is Group"}, {"fieldname": "column_break_pypy", "fieldtype": "Column Break"}, {"fieldname": "territory_manager", "fieldtype": "Link", "label": "Territory Manager", "options": "User"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "Left", "no_copy": 1, "read_only": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "Right", "no_copy": 1, "read_only": 1}, {"default": "0", "fieldname": "is_group", "fieldtype": "Check", "label": "Is Group"}, {"fieldname": "old_parent", "fieldtype": "Link", "label": "Old Parent", "options": "CRM Territory"}, {"fieldname": "parent_crm_territory", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Parent CRM Territory", "options": "CRM Territory"}, {"fieldname": "column_break_mckp", "fieldtype": "Column Break"}, {"fieldname": "section_break_qhaf", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "is_tree": 1, "links": [], "modified": "2024-01-19 21:53:53.451891", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Territory", "naming_rule": "By fieldname", "nsm_parent_field": "parent_crm_territory", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}