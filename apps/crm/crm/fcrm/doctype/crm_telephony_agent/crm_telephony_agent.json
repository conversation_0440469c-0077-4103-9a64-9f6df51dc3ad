{"actions": [], "allow_rename": 1, "autoname": "field:user", "creation": "2025-01-11 16:12:46.602782", "doctype": "DocType", "engine": "InnoDB", "field_order": ["user", "user_name", "column_break_hdec", "mobile_no", "default_medium", "section_break_ozjn", "twi<PERSON>", "twilio_number", "call_receiving_device", "column_break_aydj", "exotel", "exotel_number", "section_break_phlq", "phone_nos"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "User", "options": "User", "reqd": 1, "unique": 1}, {"fieldname": "column_break_hdec", "fieldtype": "Column Break"}, {"fieldname": "mobile_no", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Mobile No.", "read_only": 1}, {"fetch_from": "user.full_name", "fieldname": "user_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "User Name"}, {"depends_on": "exotel", "fieldname": "exotel_number", "fieldtype": "Data", "label": "Exotel Number", "mandatory_depends_on": "exotel"}, {"fieldname": "section_break_phlq", "fieldtype": "Section Break"}, {"fieldname": "section_break_ozjn", "fieldtype": "Section Break"}, {"fieldname": "column_break_aydj", "fieldtype": "Column Break"}, {"depends_on": "twi<PERSON>", "fieldname": "twilio_number", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON>", "mandatory_depends_on": "twi<PERSON>"}, {"fieldname": "phone_nos", "fieldtype": "Table", "label": "Phone Numbers", "options": "CRM Telephony Phone"}, {"default": "0", "fieldname": "twi<PERSON>", "fieldtype": "Check", "label": "<PERSON><PERSON><PERSON>"}, {"default": "0", "fieldname": "exotel", "fieldtype": "Check", "label": "Exotel"}, {"fieldname": "default_medium", "fieldtype": "Select", "label": "Default Medium", "options": "\n<PERSON><PERSON><PERSON>"}, {"default": "Computer", "depends_on": "twi<PERSON>", "fieldname": "call_receiving_device", "fieldtype": "Select", "label": "<PERSON><PERSON>", "options": "Computer\nPhone"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-23 22:24:53.448716", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Telephony Agent", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "user_name"}