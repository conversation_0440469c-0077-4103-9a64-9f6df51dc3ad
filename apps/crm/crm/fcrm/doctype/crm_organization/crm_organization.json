{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:organization_name", "creation": "2023-11-03 16:23:59.341751", "doctype": "DocType", "engine": "InnoDB", "field_order": ["organization_name", "no_of_employees", "currency", "exchange_rate", "annual_revenue", "organization_logo", "column_break_pnpp", "website", "territory", "industry", "address"], "fields": [{"fieldname": "organization_name", "fieldtype": "Data", "label": "Organization Name", "unique": 1}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "organization_logo", "fieldtype": "Attach Image", "label": "Organization Logo"}, {"fieldname": "no_of_employees", "fieldtype": "Select", "label": "No. of Employees", "options": "1-10\n11-50\n51-200\n201-500\n501-1000\n1000+"}, {"fieldname": "column_break_pnpp", "fieldtype": "Column Break"}, {"fieldname": "annual_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Annual Revenue", "options": "currency"}, {"fieldname": "industry", "fieldtype": "Link", "label": "Industry", "options": "CRM Industry"}, {"fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "CRM Territory"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "address", "fieldtype": "Link", "label": "Address", "options": "Address"}, {"description": "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically.", "fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate"}], "image_field": "organization_logo", "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-15 11:40:12.175598", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Organization", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}