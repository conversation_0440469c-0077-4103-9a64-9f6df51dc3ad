{"actions": [], "allow_rename": 1, "creation": "2025-01-08 15:55:50.710356", "doctype": "DocType", "engine": "InnoDB", "field_order": ["enabled", "column_break_uxtz", "record_call", "section_break_kfez", "account_sid", "subdomain", "column_break_qwfn", "webhook_verify_token", "section_break_iuct", "api_key", "column_break_hyen", "api_token"], "fields": [{"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "section_break_kfez", "fieldtype": "Section Break", "hide_border": 1}, {"depends_on": "enabled", "fieldname": "account_sid", "fieldtype": "Data", "label": "Account SID", "mandatory_depends_on": "enabled"}, {"depends_on": "enabled", "fieldname": "section_break_iuct", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "column_break_hyen", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "api_key", "fieldtype": "Data", "in_list_view": 1, "label": "API Key", "mandatory_depends_on": "enabled"}, {"fieldname": "column_break_uxtz", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "api_token", "fieldtype": "Password", "in_list_view": 1, "label": "API Token", "mandatory_depends_on": "enabled"}, {"default": "0", "depends_on": "enabled", "fieldname": "record_call", "fieldtype": "Check", "label": "Record Outgoing Calls"}, {"fieldname": "column_break_qwfn", "fieldtype": "Column Break"}, {"depends_on": "enabled", "fieldname": "webhook_verify_token", "fieldtype": "Data", "label": "Webhook Verify Token", "mandatory_depends_on": "enabled"}, {"depends_on": "enabled", "fieldname": "subdomain", "fieldtype": "Data", "label": "Subdomain", "mandatory_depends_on": "enabled"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-01-22 19:54:20.074393", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Exotel Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "All", "share": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}