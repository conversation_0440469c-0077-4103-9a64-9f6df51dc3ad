{"actions": [], "allow_rename": 1, "autoname": "field:holiday_list_name", "creation": "2023-12-14 11:09:12.876640", "doctype": "DocType", "engine": "InnoDB", "field_order": ["holiday_list_name", "from_date", "to_date", "column_break_qwqc", "total_holidays", "add_weekly_holidays_section", "weekly_off", "add_to_holidays", "holidays_section", "holidays", "clear_table"], "fields": [{"fieldname": "holiday_list_name", "fieldtype": "Data", "in_list_view": 1, "label": "Holiday List Name", "reqd": 1, "unique": 1}, {"fieldname": "from_date", "fieldtype": "Date", "in_list_view": 1, "label": "From Date", "reqd": 1}, {"fieldname": "to_date", "fieldtype": "Date", "in_list_view": 1, "label": "To Date", "reqd": 1}, {"fieldname": "column_break_qwqc", "fieldtype": "Column Break"}, {"fieldname": "total_holidays", "fieldtype": "Int", "label": "Total Holidays"}, {"fieldname": "add_weekly_holidays_section", "fieldtype": "Section Break", "label": "Add Weekly Holidays"}, {"fieldname": "weekly_off", "fieldtype": "Select", "label": "Weekly Off", "options": "\nMonday\nTuesday\nWednesday\nThursday\nFriday\nSaturday\nSunday"}, {"fieldname": "add_to_holidays", "fieldtype": "<PERSON><PERSON>", "label": "Add to Holidays"}, {"fieldname": "holidays_section", "fieldtype": "Section Break", "label": "Holidays"}, {"fieldname": "clear_table", "fieldtype": "<PERSON><PERSON>", "label": "Clear Table"}, {"fieldname": "holidays", "fieldtype": "Table", "label": "Holidays", "options": "CRM Holiday"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-19 21:54:54.809445", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Holiday List", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}