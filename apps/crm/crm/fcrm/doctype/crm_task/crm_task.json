{"actions": [], "allow_import": 1, "autoname": "autoincrement", "creation": "2023-09-28 15:04:28.084159", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "priority", "start_date", "reference_doctype", "reference_docname", "column_break_cqua", "assigned_to", "status", "due_date", "section_break_bzhd", "description"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "priority", "fieldtype": "Select", "label": "Priority", "options": "Low\nMedium\nHigh"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "column_break_cqua", "fieldtype": "Column Break"}, {"fieldname": "assigned_to", "fieldtype": "Link", "label": "Assigned To", "options": "User"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Backlog\nTodo\nIn Progress\nDone\nCanceled"}, {"fieldname": "due_date", "fieldtype": "Datetime", "label": "Due Date"}, {"fieldname": "section_break_bzhd", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description"}, {"default": "CRM Lead", "fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference Document Type", "options": "DocType"}, {"fieldname": "reference_docname", "fieldtype": "Dynamic Link", "label": "Reference Doc", "options": "reference_doctype"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-02-08 12:04:00.955984", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Task", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}