{"actions": [], "allow_rename": 1, "creation": "2024-09-29 13:48:02.715924", "doctype": "DocType", "engine": "InnoDB", "field_order": ["defaults_tab", "restore_defaults", "enable_forecasting", "currency_tab", "currency", "exchange_rate_provider_section", "service_provider", "column_break_vqck", "access_key", "branding_tab", "brand_name", "brand_logo", "favicon", "dropdown_items_tab", "dropdown_items"], "fields": [{"fieldname": "restore_defaults", "fieldtype": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"fieldname": "dropdown_items", "fieldtype": "Table", "options": "CRM Dropdown Item"}, {"fieldname": "defaults_tab", "fieldtype": "Tab Break", "label": "Settings"}, {"fieldname": "branding_tab", "fieldtype": "Tab Break", "label": "Branding"}, {"description": "An image with 1:1 & 2:1 ratio is preferred", "fieldname": "brand_logo", "fieldtype": "Attach", "label": "Logo"}, {"fieldname": "dropdown_items_tab", "fieldtype": "Tab Break", "label": "Dropdown Items"}, {"fieldname": "brand_name", "fieldtype": "Data", "label": "Name"}, {"description": "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]", "fieldname": "favicon", "fieldtype": "Attach", "label": "Favicon"}, {"default": "0", "description": "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights", "fieldname": "enable_forecasting", "fieldtype": "Check", "label": "Enable Forecasting"}, {"fieldname": "currency", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "currency_tab", "fieldtype": "Tab Break", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "exchange_rate_provider_section", "fieldtype": "Section Break", "label": "Exchange Rate Provider"}, {"default": "frankfurter.app", "fieldname": "service_provider", "fieldtype": "Select", "label": "Service Provider", "options": "frankfurter.app\nexchangerate.host"}, {"depends_on": "eval:doc.service_provider == 'exchangerate.host';", "fieldname": "access_key", "fieldtype": "Data", "label": "Access Key", "mandatory_depends_on": "eval:doc.service_provider == 'exchangerate.host';"}, {"fieldname": "column_break_vqck", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-07-29 11:26:50.420614", "modified_by": "Administrator", "module": "FCRM", "name": "FCRM Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "All", "share": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}