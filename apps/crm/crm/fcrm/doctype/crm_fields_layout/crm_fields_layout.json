{"actions": [], "allow_rename": 1, "autoname": "format:{dt}-{type}", "creation": "2024-06-07 16:42:05.495324", "doctype": "DocType", "engine": "InnoDB", "field_order": ["dt", "column_break_post", "type", "section_break_ttpm", "layout"], "fields": [{"fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Document Type", "options": "DocType"}, {"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Type", "options": "Quick Entry\nSide Panel\nData Fields\nGrid Row\nRequired Fields"}, {"fieldname": "section_break_ttpm", "fieldtype": "Section Break"}, {"fieldname": "layout", "fieldtype": "Code", "label": "Layout", "options": "JSON"}, {"fieldname": "column_break_post", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-02-21 13:09:49.573515", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Fields Layout", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}