{"actions": [], "allow_rename": 1, "autoname": "field:title", "creation": "2025-07-14 12:19:49.725022", "doctype": "DocType", "engine": "InnoDB", "field_order": ["title", "private", "column_break_exbw", "user", "section_break_hfza", "layout"], "fields": [{"fieldname": "column_break_exbw", "fieldtype": "Column Break"}, {"fieldname": "section_break_hfza", "fieldtype": "Section Break"}, {"default": "[]", "fieldname": "layout", "fieldtype": "Code", "label": "Layout", "options": "JSON"}, {"fieldname": "title", "fieldtype": "Data", "label": "Name", "unique": 1}, {"depends_on": "private", "fieldname": "user", "fieldtype": "Link", "label": "User", "mandatory_depends_on": "private", "options": "User"}, {"default": "0", "fieldname": "private", "fieldtype": "Check", "label": "Private"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-14 12:36:10.831351", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Dashboard", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "title"}