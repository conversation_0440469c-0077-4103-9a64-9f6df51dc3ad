{"actions": [], "autoname": "autoincrement", "creation": "2023-11-27 16:29:10.993403", "doctype": "DocType", "engine": "InnoDB", "field_order": ["label", "icon", "user", "is_standard", "is_default", "column_break_zacm", "type", "dt", "route_name", "pinned", "public", "filters_tab", "filters", "order_by_tab", "order_by", "list_tab", "list_section", "load_default_columns", "columns", "rows", "group_by_tab", "group_by_field", "kanban_tab", "kanban_section", "column_field", "title_field", "kanban_columns", "kanban_fields"], "fields": [{"fieldname": "columns", "fieldtype": "Code", "label": "Columns"}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User"}, {"fieldname": "rows", "fieldtype": "Code", "label": "Rows"}, {"fieldname": "filters", "fieldtype": "Code", "label": "Filters"}, {"fieldname": "filters_tab", "fieldtype": "Tab Break", "label": "Filters"}, {"fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Label"}, {"fieldname": "column_break_zacm", "fieldtype": "Column Break"}, {"fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "DocType", "options": "DocType"}, {"fieldname": "order_by_tab", "fieldtype": "Tab Break", "label": "Order By"}, {"fieldname": "order_by", "fieldtype": "Code", "label": "Order By"}, {"default": "0", "fieldname": "pinned", "fieldtype": "Check", "label": "Pinned"}, {"fieldname": "route_name", "fieldtype": "Data", "label": "Route Name"}, {"default": "0", "fieldname": "load_default_columns", "fieldtype": "Check", "label": "<PERSON>ad De<PERSON>ult <PERSON>"}, {"default": "0", "fieldname": "public", "fieldtype": "Check", "label": "Public"}, {"fieldname": "icon", "fieldtype": "Data", "label": "Icon"}, {"default": "list", "fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "list\ngroup_by\nkanban"}, {"fieldname": "group_by_tab", "fieldtype": "Tab Break", "label": "Group By"}, {"fieldname": "group_by_field", "fieldtype": "Data", "label": "Group By Field"}, {"fieldname": "list_section", "fieldtype": "Section Break"}, {"fieldname": "kanban_section", "fieldtype": "Section Break"}, {"fieldname": "column_field", "fieldtype": "Data", "label": "Column Field"}, {"fieldname": "list_tab", "fieldtype": "Tab Break", "label": "List"}, {"fieldname": "kanban_tab", "fieldtype": "Tab Break", "label": "Ka<PERSON><PERSON>"}, {"fieldname": "kanban_columns", "fieldtype": "Code", "label": "Kanban Columns"}, {"fieldname": "kanban_fields", "fieldtype": "Code", "label": "Kanban Fields"}, {"fieldname": "title_field", "fieldtype": "Data", "label": "Title Field"}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard"}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "label": "<PERSON>"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-02-20 15:36:55.059065", "modified_by": "Administrator", "module": "FCRM", "name": "CRM View Settings", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}