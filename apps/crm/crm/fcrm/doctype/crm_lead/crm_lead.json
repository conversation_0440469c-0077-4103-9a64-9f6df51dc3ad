{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2023-07-24 12:19:39.616298", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "email_append_to": 1, "engine": "InnoDB", "field_order": ["details", "organization", "website", "territory", "industry", "job_title", "source", "lead_owner", "person_tab", "salutation", "first_name", "last_name", "email", "mobile_no", "organization_tab", "section_break_uixv", "naming_series", "lead_name", "middle_name", "gender", "phone", "column_break_dbsv", "status", "no_of_employees", "annual_revenue", "image", "converted", "products_tab", "products", "section_break_ggwh", "total", "column_break_uisv", "net_total", "sla_tab", "sla", "sla_creation", "column_break_ffnp", "sla_status", "communication_status", "response_details_section", "response_by", "column_break_pweh", "first_response_time", "first_responded_on", "log_tab", "status_change_log"], "fields": [{"default": "CRM-LEAD-.YYYY.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "CRM-LEAD-.YYYY.-"}, {"fieldname": "salutation", "fieldtype": "Link", "label": "Salutation", "options": "Salutation"}, {"fieldname": "first_name", "fieldtype": "Data", "label": "First Name", "reqd": 1}, {"fieldname": "middle_name", "fieldtype": "Data", "label": "Middle Name"}, {"fieldname": "last_name", "fieldtype": "Data", "label": "Last Name"}, {"fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"default": "New", "fieldname": "status", "fieldtype": "Link", "in_list_view": 1, "label": "Status", "options": "CRM Lead Status", "reqd": 1, "search_index": 1}, {"fieldname": "email", "fieldtype": "Data", "label": "Email", "options": "Email", "search_index": 1}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile No", "options": "Phone"}, {"fieldname": "phone", "fieldtype": "Data", "label": "Phone", "options": "Phone"}, {"fieldname": "section_break_uixv", "fieldtype": "Section Break"}, {"fieldname": "column_break_dbsv", "fieldtype": "Column Break"}, {"fieldname": "no_of_employees", "fieldtype": "Select", "label": "No. of Employees", "options": "1-10\n11-50\n51-200\n201-500\n501-1000\n1000+"}, {"fieldname": "annual_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Annual Revenue"}, {"fieldname": "lead_owner", "fieldtype": "Link", "label": "Lead Owner", "options": "User"}, {"fieldname": "source", "fieldtype": "Link", "label": "Source", "options": "CRM Lead Source"}, {"fieldname": "industry", "fieldtype": "Link", "label": "Industry", "options": "CRM Industry"}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "lead_name", "fieldtype": "Data", "label": "Full Name", "search_index": 1}, {"fieldname": "job_title", "fieldtype": "Data", "label": "Job Title"}, {"fieldname": "organization_tab", "fieldtype": "Tab Break", "label": "Others", "read_only": 1}, {"fieldname": "organization", "fieldtype": "Data", "label": "Organization"}, {"default": "0", "fieldname": "converted", "fieldtype": "Check", "in_list_view": 1, "in_standard_filter": 1, "label": "Converted"}, {"fieldname": "person_tab", "fieldtype": "Tab Break", "label": "Person"}, {"fieldname": "details", "fieldtype": "Tab Break", "label": "Details"}, {"fieldname": "sla_tab", "fieldtype": "Tab Break", "label": "SLA", "read_only": 1}, {"fieldname": "sla", "fieldtype": "Link", "label": "SLA", "options": "CRM Service Level Agreement"}, {"fieldname": "sla_creation", "fieldtype": "Datetime", "label": "SLA Creation", "read_only": 1}, {"fieldname": "column_break_ffnp", "fieldtype": "Column Break"}, {"fieldname": "sla_status", "fieldtype": "Select", "label": "SLA Status", "options": "\nFirst Response Due\nFailed\nFulfilled", "read_only": 1}, {"fieldname": "response_details_section", "fieldtype": "Section Break", "label": "Response Details"}, {"fieldname": "response_by", "fieldtype": "Datetime", "label": "Response By", "read_only": 1}, {"fieldname": "column_break_pweh", "fieldtype": "Column Break"}, {"fieldname": "first_response_time", "fieldtype": "Duration", "label": "First Response Time", "read_only": 1}, {"fieldname": "first_responded_on", "fieldtype": "Datetime", "label": "First Responded On", "read_only": 1}, {"default": "Open", "fieldname": "communication_status", "fieldtype": "Link", "label": "Communication Status", "options": "CRM Communication Status"}, {"fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "CRM Territory"}, {"fieldname": "log_tab", "fieldtype": "Tab Break", "label": "Log", "read_only": 1}, {"fieldname": "status_change_log", "fieldtype": "Table", "label": "Status Change Log", "options": "CRM Status Change Log"}, {"fieldname": "products_tab", "fieldtype": "Tab Break", "label": "Products"}, {"fieldname": "products", "fieldtype": "Table", "label": "Products", "options": "CRM Products"}, {"fieldname": "section_break_ggwh", "fieldtype": "Section Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "column_break_uisv", "fieldtype": "Column Break"}, {"description": "Total after discount", "fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "options": "currency", "read_only": 1}], "grid_page_length": 50, "image_field": "image", "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-14 19:51:06.184569", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Lead", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "row_format": "Dynamic", "sender_field": "email", "sender_name_field": "first_name", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "lead_name", "track_changes": 1}