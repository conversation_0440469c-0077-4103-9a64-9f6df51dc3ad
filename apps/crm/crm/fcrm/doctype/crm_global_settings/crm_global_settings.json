{"actions": [], "allow_rename": 1, "autoname": "format:{type}-{dt}", "creation": "2025-02-28 14:37:10.002433", "doctype": "DocType", "engine": "InnoDB", "field_order": ["dt", "column_break_kipp", "type", "section_break_vass", "json"], "fields": [{"default": "DocType", "fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "label": "DocType", "options": "DocType", "reqd": 1}, {"fieldname": "column_break_kipp", "fieldtype": "Column Break"}, {"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "Quick Filters\nSidebar Items", "reqd": 1}, {"fieldname": "section_break_vass", "fieldtype": "Section Break"}, {"fieldname": "json", "fieldtype": "JSON", "label": "JSON"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-03-02 14:03:49.372132", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Global Settings", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}