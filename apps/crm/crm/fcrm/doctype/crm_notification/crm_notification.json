{"actions": [], "allow_rename": 1, "creation": "2024-01-29 19:31:13.613929", "doctype": "DocType", "engine": "InnoDB", "field_order": ["notification_text", "section_break_hace", "from_user", "type", "column_break_dduu", "to_user", "read", "section_break_pbvx", "reference_doctype", "reference_name", "column_break_eant", "notification_type_doctype", "notification_type_doc", "comment", "section_break_vpwa", "message"], "fields": [{"fieldname": "from_user", "fieldtype": "Link", "label": "From User", "options": "User"}, {"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "Mention\nTask\nAssignment\nWhatsApp", "reqd": 1}, {"fieldname": "column_break_dduu", "fieldtype": "Column Break"}, {"fieldname": "to_user", "fieldtype": "Link", "in_list_view": 1, "label": "To User", "options": "User", "reqd": 1}, {"fieldname": "comment", "fieldtype": "Link", "hidden": 1, "label": "Comment", "options": "Comment"}, {"default": "0", "fieldname": "read", "fieldtype": "Check", "label": "Read"}, {"fieldname": "section_break_vpwa", "fieldtype": "Section Break"}, {"fieldname": "message", "fieldtype": "HTML Editor", "in_list_view": 1, "label": "Message"}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Reference Doc", "options": "reference_doctype"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference Doctype", "options": "DocType"}, {"fieldname": "section_break_pbvx", "fieldtype": "Section Break"}, {"fieldname": "column_break_eant", "fieldtype": "Column Break"}, {"fieldname": "notification_type_doctype", "fieldtype": "Link", "label": "Notification Type Doctype", "options": "DocType"}, {"fieldname": "notification_type_doc", "fieldtype": "Dynamic Link", "label": "Notification Type Doc", "options": "notification_type_doctype"}, {"fieldname": "notification_text", "fieldtype": "Text", "label": "Notification Text"}, {"fieldname": "section_break_hace", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-09-23 19:34:08.635305", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Notification", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}