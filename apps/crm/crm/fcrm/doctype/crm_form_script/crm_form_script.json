{"actions": [], "allow_rename": 1, "autoname": "prompt", "creation": "2023-12-28 14:18:09.329868", "doctype": "DocType", "engine": "InnoDB", "field_order": ["dt", "view", "column_break_gboh", "enabled", "is_standard", "section_break_xeox", "script"], "fields": [{"fieldname": "column_break_gboh", "fieldtype": "Column Break"}, {"fieldname": "section_break_xeox", "fieldtype": "Section Break"}, {"fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "label": "DocType", "options": "DocType", "reqd": 1}, {"default": "0", "fieldname": "enabled", "fieldtype": "Check", "hidden": 1, "label": "Enabled"}, {"default": "function setupForm({ doc }) {\n    return {\n        actions: [],\n    }\n}", "documentation_url": "https://docs.frappe.io/crm/custom-actions", "fieldname": "script", "fieldtype": "Code", "label": "<PERSON><PERSON><PERSON>", "options": "JS"}, {"default": "Form", "fieldname": "view", "fieldtype": "Select", "in_list_view": 1, "label": "Apply To", "options": "Form\nList", "set_only_once": 1}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "no_copy": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-19 17:57:24.610295", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Form Script", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}