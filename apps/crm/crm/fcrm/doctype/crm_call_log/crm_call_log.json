{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:id", "creation": "2023-08-28 00:23:36.229137", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["telephony_medium", "section_break_gyqe", "id", "from", "status", "duration", "medium", "start_time", "reference_doctype", "reference_docname", "column_break_ufnp", "to", "type", "receiver", "caller", "recording_url", "end_time", "note", "section_break_kebz", "links"], "fields": [{"fieldname": "id", "fieldtype": "Data", "label": "ID", "unique": 1}, {"fieldname": "from", "fieldtype": "Data", "in_list_view": 1, "label": "From", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Initiated\nRinging\nIn Progress\nCompleted\nFailed\nBusy\nNo Answer\nQueued\nCanceled", "reqd": 1}, {"fieldname": "start_time", "fieldtype": "Datetime", "label": "Start Time"}, {"fieldname": "medium", "fieldtype": "Data", "label": "Medium"}, {"fieldname": "column_break_ufnp", "fieldtype": "Column Break"}, {"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Type", "options": "Incoming\nOutgoing", "reqd": 1}, {"fieldname": "to", "fieldtype": "Data", "in_list_view": 1, "label": "To", "reqd": 1}, {"description": "Call duration in seconds", "fieldname": "duration", "fieldtype": "Duration", "in_list_view": 1, "label": "Duration"}, {"fieldname": "recording_url", "fieldtype": "Data", "label": "Recording URL"}, {"fieldname": "end_time", "fieldtype": "Datetime", "label": "End Time"}, {"fieldname": "note", "fieldtype": "Link", "label": "Note", "options": "FCRM Note"}, {"depends_on": "eval:doc.type == 'Incoming'", "fieldname": "receiver", "fieldtype": "Link", "label": "Call Received By", "options": "User"}, {"depends_on": "eval:doc.type == 'Outgoing'", "fieldname": "caller", "fieldtype": "Link", "label": "Caller", "options": "User"}, {"default": "CRM Lead", "fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference Document Type", "options": "DocType"}, {"fieldname": "reference_docname", "fieldtype": "Dynamic Link", "label": "Reference Name", "options": "reference_doctype"}, {"fieldname": "section_break_kebz", "fieldtype": "Section Break"}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "Dynamic Link"}, {"fieldname": "telephony_medium", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Telephony Medium", "options": "\nManual\n<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "section_break_gyqe", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-01 16:01:54.479309", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Call Log", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}