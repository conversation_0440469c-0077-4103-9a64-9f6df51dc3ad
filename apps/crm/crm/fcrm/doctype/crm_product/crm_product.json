{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:product_code", "creation": "2025-04-28 11:45:09.309636", "doctype": "DocType", "engine": "InnoDB", "field_order": ["naming_series", "product_code", "product_name", "column_break_bpdj", "disabled", "standard_rate", "image", "section_break_rtwm", "description"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "CRM-PROD-.YYYY.-"}, {"fieldname": "product_code", "fieldtype": "Data", "in_list_view": 1, "label": "Product Code", "reqd": 1, "unique": 1}, {"fieldname": "product_name", "fieldtype": "Data", "label": "Product Name"}, {"fieldname": "column_break_bpdj", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"fieldname": "section_break_rtwm", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description"}, {"fieldname": "standard_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Standard Selling Rate"}], "grid_page_length": 50, "image_field": "image", "index_web_pages_for_search": 1, "links": [], "make_attachments_public": 1, "modified": "2025-04-28 12:47:25.087957", "modified_by": "Administrator", "module": "FCRM", "name": "CRM Product", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "search_fields": "product_name,description", "show_name_in_global_search": 1, "show_preview_popup": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "product_name", "track_changes": 1}