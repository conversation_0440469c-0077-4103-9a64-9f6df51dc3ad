name: Build Container Image
on:
  workflow_dispatch:
  push:
    branches:
      - main
      - develop
    tags:
      - "*"

jobs:
  build:
    name: Build

    runs-on: ubuntu-latest

    strategy:
      matrix:
        arch: [amd64, arm64]

    steps:
      - name: Checkout Entire Repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/${{ matrix.arch }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set Branch
        run: |
          export APPS_JSON='[{"url": "https://github.com/${{ github.repository }}","branch": "${{ github.ref_name }}"}]'
          echo "APPS_JSON_BASE64=$(echo $APPS_JSON | base64 -w 0)" >> $GITHUB_ENV
          echo "FRAPPE_BRANCH=${{ github.ref_type == 'tag' || github.ref_name == 'main' && 'version-15' || 'develop' }}" >> $GITHUB_ENV

      - name: Set Image Tag
        run: |
          echo "IMAGE_TAG=${{ github.ref_name == 'develop' && 'latest' || 'v15' }}" >> $GITHUB_ENV
          echo "STABLE_TAG=${{ github.ref_name == 'develop' && 'nightly' || 'stable' }}" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          repository: frappe/frappe_docker
          path: builds

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: builds
          file: builds/images/layered/Containerfile
          tags: >
            ghcr.io/${{ github.repository }}:${{ github.ref_name }},
            ghcr.io/${{ github.repository }}:${{ env.IMAGE_TAG }},
            ghcr.io/${{ github.repository }}:${{ env.STABLE_TAG }}
            
          build-args: |
            "FRAPPE_BRANCH=${{ env.FRAPPE_BRANCH }}"
            "APPS_JSON_BASE64=${{ env.APPS_JSON_BASE64 }}"
