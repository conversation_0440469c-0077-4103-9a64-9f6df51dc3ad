// Simple Sales Order Client Script for CRM Organization Customer Creation
// Copy this code into your Sales Order Client Script customization

frappe.ui.form.on('Sales Order', {
    onload_post_render: function(frm) {
        if (!frm.doc.customer && frm.doc.customer_name && frm.doc.opportunity) {
            // Get CRM Deal from opportunity and create customer
            frappe.call({
                method: 'frappe.client.get_value',
                args: {
                    doctype: 'Opportunity',
                    filters: {'name': frm.doc.opportunity},
                    fieldname: ['crm_deal']
                },
                callback: function(r) {
                    let crm_deal = r.message ? r.message.crm_deal : null;
                    
                    // Create customer using the new function
                    frappe.call({
                        method: 'erpnext.crm.frappe_crm_api.create_customer_from_crm_organization',
                        args: {
                            customer_name: frm.doc.customer_name,
                            crm_deal: crm_deal
                        },
                        callback: function(response) {
                            if (response.message) {
                                frm.set_value('customer', response.message);
                                frappe.show_alert({
                                    message: __('Customer {0} created and set', [response.message]),
                                    indicator: 'green'
                                });
                            }
                        }
                    });
                }
            });
        }
    }
});
