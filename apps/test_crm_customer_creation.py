#!/usr/bin/env python3
"""
Test script to verify CRM Organization customer creation functionality
Run this script to test the customer creation flow
"""

import frappe

def test_customer_creation():
    """Test the customer creation from CRM Organization"""
    
    # Test data
    test_customer_name = "Test Organization Ltd"
    test_crm_deal = None  # Can be set to actual CRM Deal name for testing
    
    print("Testing CRM Organization Customer Creation...")
    
    try:
        # Import the function
        from erpnext.crm.frappe_crm_api import create_customer_from_crm_organization
        
        # Test customer creation
        result = create_customer_from_crm_organization(test_customer_name, test_crm_deal)
        
        if result:
            print(f"✅ Customer created successfully: {result}")
            
            # Verify customer exists
            customer_doc = frappe.get_doc("Customer", result)
            print(f"✅ Customer verified: {customer_doc.customer_name}")
            
            # Clean up test data
            customer_doc.delete()
            print("✅ Test data cleaned up")
            
        else:
            print("❌ Customer creation failed")
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

def test_quotation_customer_creation():
    """Test customer creation through quotation flow"""
    
    print("\nTesting Quotation to Sales Order customer creation...")
    
    try:
        # This would test the quotation.py _make_customer function
        from erpnext.selling.doctype.quotation.quotation import _make_customer
        
        # Create a test quotation (this is just a simulation)
        print("✅ Quotation customer creation functions are available")
        
    except Exception as e:
        print(f"❌ Error testing quotation flow: {str(e)}")

if __name__ == "__main__":
    # Initialize Frappe
    try:
        frappe.init()
        frappe.connect()
        
        test_customer_creation()
        test_quotation_customer_creation()
        
    except Exception as e:
        print(f"❌ Failed to initialize Frappe: {str(e)}")
    finally:
        if frappe.db:
            frappe.db.close()
