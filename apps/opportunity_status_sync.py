# Solution 1: Using on_update hook
import frappe


def opportunity_on_update(doc, method):
    """
    Hook that triggers on opportunity update (including db_set operations)
    """
    # Check if status field was actually changed
    if hasattr(doc, "_doc_before_save"):
        old_status = doc._doc_before_save.get("status")
        new_status = doc.status

        if old_status != new_status:
            update_crm_deal_status(doc, new_status)
    else:
        # For db_set operations, we need to check against database
        old_status = frappe.db.get_value("Opportunity", doc.name, "status")
        if old_status != doc.status:
            update_crm_deal_status(doc, doc.status)


def update_crm_deal_status(doc, new_status):
    """
    Update CRM Deal status based on opportunity status
    """
    status_map = {
        "Replied": "Negotiation",
        "Converted": "Won",
        "Quotation": "Quotation",  # Add this mapping
    }

    if new_status not in ["Open", "Closed", "Lost"]:
        deal_name = "P" + doc.name[3:]
        if frappe.db.exists("CRM Deal", deal_name):
            crm_status = status_map.get(new_status, new_status)
            frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
            frappe.db.commit()  # Ensure the change is committed


# Solution 2: Override set_status method
def override_opportunity_set_status():
    """
    Override the Opportunity set_status method to include CRM Deal sync
    """
    from erpnext.crm.doctype.opportunity.opportunity import Opportunity

    # Store original method
    original_set_status = Opportunity.set_status

    def custom_set_status(self, update=False, status=None, update_modified=True):
        # Get old status before change
        old_status = self.status

        # Call original method
        result = original_set_status(
            self, update=update, status=status, update_modified=update_modified
        )

        # Check if status actually changed and sync with CRM Deal
        if self.status != old_status:
            update_crm_deal_status(self, self.status)

        return result

    # Replace the method
    Opportunity.set_status = custom_set_status


# Solution 3: Database trigger approach (Server Script)
def setup_opportunity_status_trigger():
    """
    Create a server script that monitors opportunity status changes
    """
    server_script_code = """
import frappe

# This runs after any db_set operation on Opportunity
def after_change(doc, method):
    if doc.doctype == "Opportunity" and method == "db_set":
        # Get the field that was changed
        if hasattr(doc, '_changed_fields') and 'status' in doc._changed_fields:
            update_crm_deal_status(doc)

def update_crm_deal_status(doc):
    status_map = {
        "Replied": "Negotiation",
        "Converted": "Won",
        "Quotation": "Quotation"
    }

    if doc.status not in ["Open", "Closed", "Lost"]:
        deal_name = "P" + doc.name[3:]
        if frappe.db.exists("CRM Deal", deal_name):
            crm_status = status_map.get(doc.status, doc.status)
            frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
"""

    return server_script_code


# Solution 4: Most Practical - Hook into quotation submission
def quotation_on_submit(doc, method):
    """
    Hook that runs when quotation is submitted
    This is the most reliable way to catch the status change
    """
    if doc.opportunity:
        # Update CRM Deal status when quotation is submitted
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            frappe.db.set_value("CRM Deal", deal_name, "status", "Quotation")
            frappe.db.commit()


def quotation_on_cancel(doc, method):
    """
    Hook that runs when quotation is cancelled
    Reset opportunity status back to Open
    """
    if doc.opportunity:
        # Update CRM Deal status when quotation is cancelled
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            frappe.db.set_value("CRM Deal", deal_name, "status", "Open")
            frappe.db.commit()


# Solution 5: Monitor db_set operations (Advanced)
def monitor_opportunity_db_changes():
    """
    Monkey patch db_set to monitor opportunity status changes
    """
    import frappe.model.document

    # Store original db_set method
    original_db_set = frappe.model.document.Document.db_set

    def custom_db_set(
        self, fieldname, value=None, update_modified=True, notify=False, commit=False
    ):
        # Check if this is an Opportunity status change
        if self.doctype == "Opportunity" and fieldname == "status":
            old_status = self.get(fieldname)

            # Call original method
            result = original_db_set(
                self, fieldname, value, update_modified, notify, commit
            )

            # If status changed, update CRM Deal
            if old_status != value:
                update_crm_deal_status(self, value)

            return result
        else:
            # For other doctypes/fields, use original method
            return original_db_set(
                self, fieldname, value, update_modified, notify, commit
            )

    # Replace the method
    frappe.model.document.Document.db_set = custom_db_set
