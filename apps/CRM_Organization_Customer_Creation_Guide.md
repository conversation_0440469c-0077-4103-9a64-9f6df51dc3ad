# CRM Organization Customer Creation Guide

This guide explains how to automatically create customers when converting Quotations to Sales Orders where `quotation_to` is "CRM Organization".

## Problem Statement

When creating a Quotation with `quotation_to` set to "CRM Organization", the default ERPNext logic doesn't automatically create a customer when converting to Sales Order. This causes issues because Sales Orders require a customer to be set.

## Solution Overview

The solution involves:

1. **Server-side functions** in `frappe_crm_api.py` to create customers from CRM Organization data
2. **Modified quotation logic** to handle CRM Organization customer creation
3. **Client-side script** for Sales Order to automatically create customers

## Implementation Details

### 1. Server-side Functions Added

#### `create_customer_from_crm_organization(customer_name, crm_deal=None)`
- Creates a customer with the given name
- If `crm_deal` is provided, fetches additional details from the CRM Deal
- Links contacts and addresses from the CRM Deal/Organization
- Returns the customer name

#### `create_contacts_from_crm_deal(crm_deal, customer_name)`
- Links all contacts from the CRM Deal to the customer
- Preserves existing contact information

#### `create_address_from_crm_organization(organization_name, customer_name)`
- Links the organization's address to the customer

### 2. Modified Quotation Logic

The `_make_customer` function in `quotation.py` now handles "CRM Organization":
- Checks if customer already exists by `customer_name`
- Creates new customer using CRM Organization data if not found

### 3. Client Script Options

#### Option A: Simple Client Script (Recommended)
```javascript
frappe.ui.form.on('Sales Order', {
    onload_post_render: function(frm) {
        if (!frm.doc.customer && frm.doc.customer_name && frm.doc.opportunity) {
            // Get CRM Deal from opportunity and create customer
            frappe.call({
                method: 'frappe.client.get_value',
                args: {
                    doctype: 'Opportunity',
                    filters: {'name': frm.doc.opportunity},
                    fieldname: ['crm_deal']
                },
                callback: function(r) {
                    let crm_deal = r.message ? r.message.crm_deal : null;
                    
                    // Create customer using the new function
                    frappe.call({
                        method: 'erpnext.crm.frappe_crm_api.create_customer_from_crm_organization',
                        args: {
                            customer_name: frm.doc.customer_name,
                            crm_deal: crm_deal
                        },
                        callback: function(response) {
                            if (response.message) {
                                frm.set_value('customer', response.message);
                                frappe.show_alert({
                                    message: __('Customer {0} created and set', [response.message]),
                                    indicator: 'green'
                                });
                            }
                        }
                    });
                }
            });
        }
    }
});
```

## How It Works

### Data Flow
1. **Quotation Creation**: User creates quotation with `quotation_to = "CRM Organization"`
2. **Party Selection**: `party_name` is set to CRM Organization name
3. **Customer Name**: `customer_name` is populated from organization
4. **Sales Order Creation**: When converting to Sales Order:
   - System checks if customer exists
   - If not, creates customer using organization data
   - Links contacts from CRM Deal
   - Links address from CRM Organization

### Contact and Address Linking
- **Contacts**: Fetched from CRM Deal's contacts table
- **Address**: Fetched from CRM Organization's address field
- Both are linked to the new customer using Dynamic Links

## Installation Steps

1. **Apply server-side changes**:
   - Modified `erpnext/crm/frappe_crm_api.py`
   - Modified `erpnext/selling/doctype/quotation/quotation.py`

2. **Add client script**:
   - Go to Customization > Client Script
   - Create new Client Script for "Sales Order"
   - Copy the simple client script code

3. **Test the flow**:
   - Create a CRM Deal with contacts and organization
   - Create a Quotation with `quotation_to = "CRM Organization"`
   - Convert to Sales Order
   - Verify customer is created with contacts and address

## Key Features

- **Automatic customer creation** when converting quotation to sales order
- **Contact linking** from CRM Deal contacts table
- **Address linking** from CRM Organization
- **Duplicate prevention** - checks if customer already exists
- **Error handling** with proper logging
- **Backward compatibility** - doesn't affect existing Lead/Prospect flows

## Troubleshooting

### Common Issues

1. **Customer not created**: Check if `customer_name` and `opportunity` fields are populated
2. **Contacts not linked**: Verify CRM Deal has contacts in the contacts table
3. **Address not linked**: Check if CRM Organization has an address set

### Error Logs
Check Error Log doctype for detailed error messages if customer creation fails.

## Benefits

- **Seamless workflow** from CRM Deal to Sales Order
- **Data consistency** between CRM and Sales modules
- **Reduced manual work** - no need to manually create customers
- **Complete customer profile** with contacts and addresses automatically linked
