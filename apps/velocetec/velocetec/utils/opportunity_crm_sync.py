"""
Opportunity and CRM Deal Status Synchronization
Handles status updates between ERPNext Opportunity and CRM Deal doctypes
"""

import frappe


def opportunity_on_update(doc, method):
    """
    Hook that triggers on opportunity update (including db_set operations)
    This catches status changes made by quotation submission
    """
    # Only process if this is not a new document
    if doc.is_new():
        return
    
    # Get the old status from database to compare
    old_status = frappe.db.get_value("Opportunity", doc.name, "status")
    new_status = doc.status
    
    # If status has changed, update CRM Deal
    if old_status != new_status:
        update_crm_deal_status(doc, new_status, old_status)


def opportunity_before_save(doc, method):
    """
    Hook for manual status changes from the front desk
    This handles direct user updates to opportunity status
    """
    # Only process if this is not a new document and status has changed
    if not doc.is_new() and doc.has_value_changed("status"):
        old_status = doc.get_doc_before_save().status if doc.get_doc_before_save() else None
        update_crm_deal_status(doc, doc.status, old_status)


def quotation_on_submit(doc, method):
    """
    Hook that runs when quotation is submitted
    This is the most reliable way to catch quotation-triggered status changes
    """
    if doc.opportunity:
        # Update CRM Deal status when quotation is submitted
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            frappe.db.set_value("CRM Deal", deal_name, "status", "Quotation")
            frappe.db.commit()
            
            # Log the change
            frappe.logger().info(f"Updated CRM Deal {deal_name} status to 'Quotation' due to quotation {doc.name} submission")


def quotation_on_cancel(doc, method):
    """
    Hook that runs when quotation is cancelled
    Resets CRM Deal status based on current opportunity status
    """
    if doc.opportunity:
        # Get the current opportunity status
        opp_status = frappe.db.get_value("Opportunity", doc.opportunity, "status")
        
        # Map opportunity status to CRM Deal status
        status_map = { 
            "Open": "Open",
            "Replied": "Negotiation", 
            "Converted": "Won",
            "Lost": "Lost",
            "Closed": "Lost"
        }
        
        deal_name = "P" + doc.opportunity[3:]  # Convert OPP-xxx to P-xxx
        if frappe.db.exists("CRM Deal", deal_name):
            crm_status = status_map.get(opp_status, "Open")
            frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
            frappe.db.commit()
            
            # Log the change
            frappe.logger().info(f"Updated CRM Deal {deal_name} status to '{crm_status}' due to quotation {doc.name} cancellation")


def update_crm_deal_status(doc, new_status, old_status=None):
    """
    Update CRM Deal status based on opportunity status
    
    Args:
        doc: Opportunity document
        new_status: New opportunity status
        old_status: Previous opportunity status (optional)
    """
    status_map = { 
        "Replied": "Negotiation", 
        "Converted": "Won",
        "Quotation": "Quotation",
        "Lost": "Lost",
        "Closed": "Lost",
        "Open": "Open"
    } 
    
    # Skip if status is in excluded list
    if new_status in ["Open", "Closed", "Lost"] and old_status in ["Open", "Closed", "Lost"]:
        return
    
    deal_name = "P" + doc.name[3:]  # Convert OPP-xxx to P-xxx
    
    if frappe.db.exists("CRM Deal", deal_name): 
        crm_status = status_map.get(new_status, new_status)
        
        # Get current CRM Deal status to avoid unnecessary updates
        current_crm_status = frappe.db.get_value("CRM Deal", deal_name, "status")
        
        # Only update if status actually changed
        if current_crm_status != crm_status:
            frappe.db.set_value("CRM Deal", deal_name, "status", crm_status)
            frappe.db.commit()
            
            # Log the change
            frappe.logger().info(
                f"Updated CRM Deal {deal_name} status from '{current_crm_status}' to '{crm_status}' "
                f"due to opportunity {doc.name} status change from '{old_status}' to '{new_status}'"
            )
    else:
        # Log if CRM Deal doesn't exist
        frappe.logger().warning(f"CRM Deal {deal_name} not found for opportunity {doc.name}")


def sales_order_on_submit(doc, method):
    """
    Hook that runs when sales order is submitted
    Updates opportunity status to 'Converted' and CRM Deal to 'Won'
    """
    # Check if sales order has items with opportunity reference
    for item in doc.items:
        if item.prevdoc_doctype == "Opportunity" and item.prevdoc_docname:
            opportunity_name = item.prevdoc_docname
            deal_name = "P" + opportunity_name[3:]  # Convert OPP-xxx to P-xxx
            
            if frappe.db.exists("CRM Deal", deal_name):
                frappe.db.set_value("CRM Deal", deal_name, "status", "Won")
                frappe.db.commit()
                
                # Log the change
                frappe.logger().info(f"Updated CRM Deal {deal_name} status to 'Won' due to sales order {doc.name} submission")
            break  # Only process first opportunity found
