app_name = "velocetec"
app_title = "Velocetec"
app_publisher = "Sydney Kibanga"
app_description = " "
app_email = " <EMAIL>"
app_license = "mit"
# required_apps = []

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/velocetec/css/velocetec.css"
# app_include_js = "/assets/velocetec/js/velocetec.js"

# include js, css files in header of web template
# web_include_css = "/assets/velocetec/css/velocetec.css"
# web_include_js = "/assets/velocetec/js/velocetec.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "velocetec/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views

doctype_js = {
    "Opportunity": "public/js/opportunity.js",
    "Quotation": "public/js/quotation.js",
    "Sales Order": "public/js/sales_order.js",
    "Routing": "public/js/routing.js",
    "Item": "public/js/item.js",
    "Serial No": "public/js/serial_no.js",
    "Purchase Order": "public/js/purchase_order.js",
}

doctype_list_js = {
    "Custom Field": "public/js/custom_field.js",
    "Property Setter": "public/js/property_setter.js",
}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "velocetec/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "velocetec.utils.jinja_methods",
# 	"filters": "velocetec.utils.jinja_filters"
# }
jinja = {"methods": ["velocetec.api.utils.generate_qrcode"]}

# Installation
# ------------

# before_install = "velocetec.install.before_install"
after_install = [
    "velocetec.patches.create_custom_fields.execute",
    "velocetec.patches.create_property_setters.execute",
    "velocetec.patches.create_velocetec_role.execute",
]
after_migrate = [
    "velocetec.patches.create_custom_fields.execute",
    "velocetec.patches.create_property_setters.execute",
    "velocetec.patches.create_velocetec_role.execute",
    "velocetec.velocetec.patches.add_indexes_to_velocetec_tables.execute",
]

# Uninstallation
# ------------

# before_uninstall = "velocetec.uninstall.before_uninstall"
# after_uninstall = "velocetec.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "velocetec.utils.before_app_install"
# after_app_install = "velocetec.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "velocetec.utils.before_app_uninstall"
# after_app_uninstall = "velocetec.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "velocetec.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
    "BOM": {
        # First populate operations, then override costs
        "validate": [
            "velocetec.api.bom.populate_operations_on_bom",
            "velocetec.api.bom.override_bom_costs",
        ],
        # Skip cost calculation during before_save
        "before_save": "velocetec.api.bom.skip_cost_calculation",
        # Ensure costs are correct before submit
        "before_submit": "velocetec.api.bom.override_bom_costs",
    },
    "Work Order": {
        "after_insert": "velocetec.api.work_order.populate_delivery_type",
    },
    "Delivery Note": {
        "on_submit": "velocetec.api.delivery_note.create_sales_invoice_on_dn_submit"
    },
    "Opportunity": {
        "on_update": "velocetec.utils.opportunity_crm_sync.opportunity_on_update",
        "before_save": "velocetec.utils.opportunity_crm_sync.opportunity_before_save",
    },
    "Quotation": {
        "on_submit": "velocetec.utils.opportunity_crm_sync.quotation_on_submit",
        "on_cancel": "velocetec.utils.opportunity_crm_sync.quotation_on_cancel",
    },
    "Sales Order": {
        "on_submit": "velocetec.utils.opportunity_crm_sync.sales_order_on_submit",
    },
}

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"velocetec.tasks.all"
# 	],
# 	"daily": [
# 		"velocetec.tasks.daily"
# 	],
# 	"hourly": [
# 		"velocetec.tasks.hourly"
# 	],
# 	"weekly": [
# 		"velocetec.tasks.weekly"
# 	],
# 	"monthly": [
# 		"velocetec.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "velocetec.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "velocetec.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "velocetec.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["velocetec.utils.before_request"]
# after_request = ["velocetec.utils.after_request"]

# Job Events
# ----------
# before_job = ["velocetec.utils.before_job"]
# after_job = ["velocetec.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"velocetec.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }
