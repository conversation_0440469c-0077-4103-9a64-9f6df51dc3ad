// Sales Order Client Script for CRM Organization Customer Creation
// This script automatically creates a customer when a Sales Order is created from a Quotation
// where quotation_to is "CRM Organization"

frappe.ui.form.on('Sales Order', {
    onload_post_render: function(frm) {
        // Only proceed if customer is not set but customer_name exists
        if (!frm.doc.customer && frm.doc.customer_name) {
            create_customer_from_crm_organization(frm);
        }
    },
    
    refresh: function(frm) {
        // Add a button to manually create customer if needed
        if (!frm.doc.customer && frm.doc.customer_name && frm.doc.opportunity) {
            frm.add_custom_button(__('Create Customer from CRM'), function() {
                create_customer_from_crm_organization(frm);
            }, __('Actions'));
        }
    }
});

function create_customer_from_crm_organization(frm) {
    // Show loading indicator
    frappe.show_alert({
        message: __('Creating customer...'),
        indicator: 'blue'
    });
    
    // Get CRM Deal from opportunity if available
    let crm_deal = null;
    
    if (frm.doc.opportunity) {
        // First get the CRM Deal from the opportunity
        frappe.call({
            method: 'frappe.client.get_value',
            args: {
                doctype: 'Opportunity',
                filters: {'name': frm.doc.opportunity},
                fieldname: ['crm_deal', 'party_name']
            },
            callback: function(r) {
                if (r.message) {
                    crm_deal = r.message.crm_deal;
                    
                    // Now create the customer
                    create_customer_with_deal_info(frm, crm_deal);
                } else {
                    // Fallback to creating customer without CRM Deal info
                    create_customer_with_deal_info(frm, null);
                }
            },
            error: function(err) {
                console.log('Error getting opportunity details:', err);
                // Fallback to creating customer without CRM Deal info
                create_customer_with_deal_info(frm, null);
            }
        });
    } else {
        // No opportunity linked, create customer without CRM Deal info
        create_customer_with_deal_info(frm, null);
    }
}

function create_customer_with_deal_info(frm, crm_deal) {
    frappe.call({
        method: 'erpnext.crm.frappe_crm_api.create_customer_from_crm_organization',
        args: {
            customer_name: frm.doc.customer_name,
            crm_deal: crm_deal
        },
        callback: function(r) {
            if (r.message) {
                // Set the customer in the form
                frm.set_value('customer', r.message);
                
                // Show success message
                frappe.show_alert({
                    message: __('Customer {0} created and linked successfully', [r.message]),
                    indicator: 'green'
                });
                
                // Refresh the form to load customer details
                frm.refresh_field('customer');
                
                // Trigger customer change to load related data
                frm.trigger('customer');
                
            } else {
                frappe.msgprint({
                    title: __('Error'),
                    message: __('Failed to create customer. Please check error logs.'),
                    indicator: 'red'
                });
            }
        },
        error: function(err) {
            console.log('Error creating customer:', err);
            frappe.msgprint({
                title: __('Error'),
                message: err.message || __('Failed to create customer. Please try again.'),
                indicator: 'red'
            });
        }
    });
}

// Optional: Add validation to prevent saving without customer
frappe.ui.form.on('Sales Order', {
    validate: function(frm) {
        // If customer_name exists but customer is not set, show warning
        if (frm.doc.customer_name && !frm.doc.customer) {
            frappe.msgprint({
                title: __('Customer Required'),
                message: __('Customer is required. Please create customer from CRM Organization first.'),
                indicator: 'orange'
            });
            frappe.validated = false;
        }
    }
});
